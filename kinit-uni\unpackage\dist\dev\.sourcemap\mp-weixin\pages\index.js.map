{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/index.vue?4e7c", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/index.vue?c635", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/index.vue?2976", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/index.vue?4031", "uni-app:///pages/index.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/index.vue?51a4", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/index.vue?3025"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "<PERSON>F<PERSON>er", "SoulSubmissionCard", "mixins", "data", "siteName", "submissionList", "filteredSubmissionList", "filterOptions", "categories", "provinces", "genders", "quickProvinces", "label", "value", "currentProv<PERSON>ce", "showAdvancedFilter", "currentFilters", "searchKeyword", "genderOptions", "icon", "genderIndex", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "ageOptions", "showCustomAge", "searchSuggestions", "pagination", "page", "size", "total", "pages", "loading", "refreshing", "hasMore", "deboun<PERSON><PERSON><PERSON>r", "onLoad", "onShow", "methods", "goToNetworkTest", "uni", "url", "initPage", "loadSiteConfig", "res", "console", "loadFilterOptions", "submissionApi", "loadSubmissionList", "reset", "params", "existingIds", "newItems", "title", "applyLocalFilters", "filtered", "selectProvince", "toggleAdvancedFilter", "selectGender", "selectAgeOption", "isAgeOptionActive", "setNoAgeLimit", "handleMinAgeChange", "handleMaxAgeChange", "getAgePercentage", "handleSearch", "applySuggestion", "resetFilters", "applyFilters", "handleFilterChange", "showSearch", "hideSearch", "submission", "tab", "clearTimeout", "watch", "handler", "immediate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACqRtpB;AACA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAIA;EACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC,iBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC,gBACA;QAAAN;QAAAO;MAAA,GACA;QAAAP;QAAAO;MAAA,GACA;QAAAP;QAAAO;MAAA,EACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC,aACA;QAAAZ;QAAAU;QAAAC;MAAA,GACA;QAAAX;QAAAU;QAAAC;MAAA,GACA;QAAAX;QAAAU;QAAAC;MAAA,GACA;QAAAX;QAAAU;QAAAC;MAAA,GACA;QAAAX;QAAAU;QAAAC;MAAA,GACA;QAAAX;QAAAU;QAAAC;MAAA,EACA;MACAE;MACAC;MACAC;QACAC;QACAC;QAAA;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAH;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAG;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;kBACA;kBACA;kBACA;gBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGAC;kBACAtB;kBACAC;gBAAA,GACA,wBAGA;gBACA;kBACAqB;gBACA;;gBAEA;gBACA;kBACAA;gBACA;;gBAEA;gBACA;kBACAA;gBACA;;gBAEA;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OAEAH;cAAA;gBAAAH;gBAEA;kBAAA,YACAA;kBAEA;oBACA;kBACA;oBACA;oBACAO;sBAAA;oBAAA;oBACAC;sBAAA;oBAAA;oBACA;kBACA;kBAEA;kBACA;kBACA;kBAEA;oBACA;kBACA;;kBAEA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAL;kBACAa;kBACAlC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmC;MACA;;MAEA;MACAC;QACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;QAAA7C;QAAAC;MAAA;MACA;IACA;IAEA;IACA6C;MACA;MACA5B;QACAa;QACAlC;MACA;IACA;IAEAkD;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;EAAA,mFAEA;IACA;IACA;EACA,8EAEA;IACA;IACA;EACA,4EAEA;IACA;MACA;IACA;EACA,kFAEA;IACA;EACA,8EAEAC;IACAhC;MACAC;IACA;EACA,4EAGAgC;IACA;MACA;QACA;QACA;MACA;QACAjC;UACAC;QACA;QACA;MACA;QACAD;UACAC;QACA;QACA;IAAA;EAEA,4FAGA;IAAA;IACA;MACAiC;IACA;IACA;MACA;IACA;EACA,aACA;EAEA;EACAC;IACAtE;MACAuE;QACA;MACA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9qBA;AAAA;AAAA;AAAA;AAAiuC,CAAgB,8rCAAG,EAAC,C;;;;;;;;;;;ACArvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2a183b29&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2a183b29&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2a183b29\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=2a183b29&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    soulSubmissionCard: function () {\n      return import(\n        /* webpackChunkName: \"components/soul-submission-card/soul-submission-card\" */ \"@/components/soul-submission-card/soul-submission-card.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.quickProvinces.slice(0, Math.ceil(_vm.quickProvinces.length / 2))\n  var g0 = Math.ceil(_vm.quickProvinces.length / 2)\n  var l1 = _vm.quickProvinces.slice(Math.ceil(_vm.quickProvinces.length / 2))\n  var l2 = _vm.showAdvancedFilter\n    ? _vm.__map(_vm.ageOptions, function (option, index) {\n        var $orig = _vm.__get_orig(option)\n        var m0 = _vm.isAgeOptionActive(option)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g1 = _vm.loading && _vm.filteredSubmissionList.length === 0\n  var g2 = !_vm.loading && _vm.filteredSubmissionList.length === 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, province) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        province = _temp2.province\n      var _temp, _temp2\n      return _vm.selectProvince(province)\n    }\n    _vm.e1 = function ($event, province) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        province = _temp4.province\n      var _temp3, _temp4\n      return _vm.selectProvince(province)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        l1: l1,\n        l2: l2,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page-container\">\r\n    <!-- 自定义导航栏 -->\r\n    <view class=\"custom-navbar\">\r\n      <view class=\"navbar-content\">\r\n        <view class=\"navbar-left\">\r\n          <uni-icons type=\"ghost\" size=\"60\" color=\"#f78ca0\"></uni-icons>\r\n          <text class=\"navbar-title\">{{ siteName }}</text>\r\n        </view>\r\n        <view class=\"navbar-actions\">\r\n          <view class=\"action-btn\" @click=\"goToNetworkTest\">\r\n            <uni-icons type=\"gear\" size=\"24\" color=\"#f78ca0\"></uni-icons>\r\n          </view>\r\n          <view class=\"action-btn\" @click=\"showSearch\">\r\n            <uni-icons type=\"search\" size=\"24\" color=\"#f78ca0\"></uni-icons>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 省份筛选区域 -->\r\n    <view class=\"category-section\">\r\n      <view class=\"section-title\">\r\n        <uni-icons type=\"location\" size=\"32\" color=\"#f78ca0\"></uni-icons>\r\n        <text class=\"title-text\">按省份筛选</text>\r\n      </view>\r\n      <scroll-view scroll-x=\"true\" class=\"category-scroll\">\r\n        <view class=\"category-buttons-container\">\r\n          <view class=\"category-row\">\r\n            <view\r\n              v-for=\"(province, index) in quickProvinces.slice(0, Math.ceil(quickProvinces.length / 2))\"\r\n              :key=\"index\"\r\n              class=\"category-btn\"\r\n              :class=\"{ active: currentProvince === province.value }\"\r\n              @click=\"selectProvince(province)\"\r\n            >\r\n              <text class=\"category-text\">{{ province.label }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"category-row\">\r\n            <view\r\n              v-for=\"(province, index) in quickProvinces.slice(Math.ceil(quickProvinces.length / 2))\"\r\n              :key=\"index + Math.ceil(quickProvinces.length / 2)\"\r\n              class=\"category-btn\"\r\n              :class=\"{ active: currentProvince === province.value }\"\r\n              @click=\"selectProvince(province)\"\r\n            >\r\n              <text class=\"category-text\">{{ province.label }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n\r\n\r\n\r\n    <!-- 投稿列表 -->\r\n    <view class=\"posts-section\">\r\n      <view class=\"section-title\">\r\n        <view class=\"title-left\">\r\n          <uni-icons type=\"star\" size=\"32\" color=\"#f78ca0\"></uni-icons>\r\n          <text class=\"title-text\">最新搭子</text>\r\n        </view>\r\n        <view class=\"filter-btn-container\" @click=\"toggleAdvancedFilter\">\r\n          <uni-icons type=\"tune\" size=\"24\" color=\"#f78ca0\"></uni-icons>\r\n          <text class=\"filter-btn-text\">筛选</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 高级筛选区域 -->\r\n      <view v-if=\"showAdvancedFilter\" class=\"advanced-filter-section\">\r\n        <view class=\"filter-grid\">\r\n          <view class=\"filter-item\">\r\n            <text class=\"filter-label\">关键词搜索</text>\r\n            <input\r\n              v-model=\"searchKeyword\"\r\n              class=\"filter-input\"\r\n              placeholder=\"年龄、城市、兴趣爱好...\"\r\n              @input=\"handleSearch\"\r\n            />\r\n          </view>\r\n\r\n          <view class=\"filter-item\">\r\n            <text class=\"filter-label\">性别</text>\r\n            <view class=\"gender-options\">\r\n              <view\r\n                v-for=\"(option, index) in genderOptions\"\r\n                :key=\"index\"\r\n                class=\"gender-option\"\r\n                :class=\"{ active: genderIndex === index }\"\r\n                @click=\"selectGender(index)\"\r\n              >\r\n                <uni-icons :type=\"option.icon\" size=\"20\" :color=\"genderIndex === index ? '#f78ca0' : '#999'\"></uni-icons>\r\n                <text class=\"gender-text\" :class=\"{ active: genderIndex === index }\">{{ option.label }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"filter-item\">\r\n            <text class=\"filter-label\">年龄范围</text>\r\n            <view class=\"age-range-container\">\r\n              <!-- 年龄范围显示 -->\r\n              <view class=\"age-display\">\r\n                <text class=\"age-text\">\r\n                  {{ ageRange.min }}岁 - {{ ageRange.max === 99 ? '不限' : ageRange.max + '岁' }}\r\n                </text>\r\n              </view>\r\n\r\n              <!-- 年龄快捷选择 -->\r\n              <view class=\"age-quick-select\">\r\n                <view\r\n                  v-for=\"(option, index) in ageOptions\"\r\n                  :key=\"index\"\r\n                  class=\"age-option\"\r\n                  :class=\"{ active: isAgeOptionActive(option) }\"\r\n                  @click=\"selectAgeOption(option)\"\r\n                >\r\n                  <text class=\"age-option-text\">{{ option.label }}</text>\r\n                </view>\r\n              </view>\r\n\r\n              <!-- 自定义年龄范围 -->\r\n              <view class=\"custom-age-range\" v-if=\"showCustomAge\">\r\n                <!-- 最小年龄滑块 -->\r\n                <view class=\"slider-container\">\r\n                  <text class=\"slider-label\">最小年龄: {{ ageRange.min }}岁</text>\r\n                  <slider\r\n                    class=\"age-slider\"\r\n                    :value=\"ageRange.min\"\r\n                    :min=\"16\"\r\n                    :max=\"60\"\r\n                    :step=\"1\"\r\n                    activeColor=\"#f78ca0\"\r\n                    backgroundColor=\"#e5e7eb\"\r\n                    block-color=\"#f78ca0\"\r\n                    block-size=\"20\"\r\n                    @change=\"handleMinAgeChange\"\r\n                  />\r\n                </view>\r\n\r\n                <!-- 最大年龄滑块 -->\r\n                <view class=\"slider-container\">\r\n                  <text class=\"slider-label\">最大年龄: {{ ageRange.max === 99 ? '不限' : ageRange.max + '岁' }}</text>\r\n                  <slider\r\n                    class=\"age-slider\"\r\n                    :value=\"ageRange.max === 99 ? 60 : ageRange.max\"\r\n                    :min=\"16\"\r\n                    :max=\"60\"\r\n                    :step=\"1\"\r\n                    activeColor=\"#a6c1ee\"\r\n                    backgroundColor=\"#e5e7eb\"\r\n                    block-color=\"#a6c1ee\"\r\n                    block-size=\"20\"\r\n                    @change=\"handleMaxAgeChange\"\r\n                  />\r\n                  <view class=\"no-limit-option\" @click=\"setNoAgeLimit\">\r\n                    <text class=\"no-limit-text\" :class=\"{ active: ageRange.max === 99 }\">不限年龄上限</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"filter-actions\">\r\n            <view class=\"filter-btn reset\" @click=\"resetFilters\">\r\n              <uni-icons type=\"refresh\" size=\"20\" color=\"#868f96\"></uni-icons>\r\n              <text>重置</text>\r\n            </view>\r\n            <view class=\"filter-btn apply\" @click=\"applyFilters\">\r\n              <uni-icons type=\"checkmarkempty\" size=\"20\" color=\"#fff\"></uni-icons>\r\n              <text>应用筛选</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <scroll-view\r\n        scroll-y=\"true\"\r\n        class=\"submission-list\"\r\n        @scrolltolower=\"loadMore\"\r\n        refresher-enabled=\"true\"\r\n        :refresher-triggered=\"refreshing\"\r\n        @refresherrefresh=\"onRefresh\"\r\n      >\r\n        <view class=\"list-container\">\r\n          <!-- 初始加载状态 -->\r\n          <view v-if=\"loading && filteredSubmissionList.length === 0\" class=\"initial-loading\">\r\n            <uni-icons type=\"spinner-cycle\" size=\"40\" color=\"#f78ca0\"></uni-icons>\r\n            <text class=\"loading-text\">加载中...</text>\r\n          </view>\r\n\r\n          <!-- 投稿网格 -->\r\n          <view v-else class=\"submission-grid\">\r\n            <soul-submission-card\r\n              v-for=\"(submission, index) in filteredSubmissionList\"\r\n              :key=\"`submission-${submission.id}-${index}`\"\r\n              :submission=\"submission\"\r\n              @click=\"goToDetail\"\r\n            ></soul-submission-card>\r\n          </view>\r\n\r\n          <!-- 加载状态 -->\r\n          <view v-if=\"loading\" class=\"loading-container\">\r\n            <uni-icons type=\"heart\" size=\"48\" color=\"#f78ca0\" class=\"loading-icon\"></uni-icons>\r\n            <text class=\"loading-text\">正在努力加载搭子信息中...</text>\r\n          </view>\r\n\r\n          <!-- 空状态 -->\r\n          <view v-if=\"!loading && filteredSubmissionList.length === 0\" class=\"empty-container\">\r\n            <view class=\"empty-icon\">\r\n              <uni-icons type=\"search\" size=\"80\" color=\"#c8a8e9\"></uni-icons>\r\n            </view>\r\n            <text class=\"empty-title\">没有找到相关搭子</text>\r\n            <text class=\"empty-subtitle\">试试调整筛选条件或搜索关键词</text>\r\n\r\n            <!-- 搜索建议 -->\r\n            <view class=\"search-suggestions\">\r\n              <text class=\"suggestions-title\">热门搜索建议：</text>\r\n              <view class=\"suggestions-tags\">\r\n                <view\r\n                  v-for=\"(tag, index) in searchSuggestions\"\r\n                  :key=\"index\"\r\n                  class=\"suggestion-tag\"\r\n                  @click=\"applySuggestion(tag)\"\r\n                >\r\n                  <text class=\"tag-text\">{{ tag }}</text>\r\n                </view>\r\n              </view>\r\n            </view>\r\n\r\n            <!-- 重置按钮 -->\r\n            <view class=\"reset-filters-btn\" @click=\"resetFilters\">\r\n              <uni-icons type=\"refresh\" size=\"20\" color=\"#f78ca0\"></uni-icons>\r\n              <text class=\"reset-text\">重置筛选条件</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <!-- 底部菜单栏 -->\r\n    <view class=\"bottom-tabbar\">\r\n      <view class=\"tab-item active\" @click=\"switchTab('home')\">\r\n        <uni-icons type=\"home\" size=\"24\" color=\"#f78ca0\"></uni-icons>\r\n        <text class=\"tab-text active\">主页</text>\r\n      </view>\r\n      <view class=\"tab-item\" @click=\"switchTab('add')\">\r\n        <view class=\"add-btn\">\r\n          <uni-icons type=\"plus\" size=\"24\" color=\"#fff\"></uni-icons>\r\n        </view>\r\n        <text class=\"tab-text\">投稿</text>\r\n      </view>\r\n      <view class=\"tab-item\" @click=\"switchTab('profile')\">\r\n        <uni-icons type=\"person\" size=\"24\" color=\"#999\"></uni-icons>\r\n        <text class=\"tab-text\">我的</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 搜索弹窗 -->\r\n    <uni-popup ref=\"searchPopup\" type=\"top\" background-color=\"#ffffff\">\r\n      <view class=\"search-container\">\r\n        <view class=\"search-bar\">\r\n          <uni-icons type=\"search\" size=\"24\" color=\"#FF6B9D\"></uni-icons>\r\n          <input\r\n            v-model=\"searchKeyword\"\r\n            placeholder=\"搜索投稿内容...\"\r\n            class=\"search-input\"\r\n            @confirm=\"handleSearch\"\r\n          />\r\n          <text class=\"cancel-btn\" @click=\"hideSearch\">取消</text>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { wxShareMixins } from '@/common/mixins/share.js'\r\nimport submissionApi from '@/common/api/submission.js'\r\nimport SoulFilter from '@/components/soul-filter/soul-filter.vue'\r\nimport SoulSubmissionCard from '@/components/soul-submission-card/soul-submission-card.vue'\r\n\r\nexport default {\r\n  components: {\r\n    SoulFilter,\r\n    SoulSubmissionCard\r\n  },\r\n  mixins: [wxShareMixins],\r\n  data() {\r\n    return {\r\n      siteName: '搭子星球', // 默认网站名称\r\n      submissionList: [],\r\n      filteredSubmissionList: [],\r\n      filterOptions: {\r\n        categories: [],\r\n        provinces: [],\r\n        genders: []\r\n      },\r\n      quickProvinces: [\r\n        { label: '全部', value: '' },\r\n        { label: '北京', value: '北京' },\r\n        { label: '上海', value: '上海' },\r\n        { label: '广东', value: '广东' },\r\n        { label: '浙江', value: '浙江' },\r\n        { label: '江苏', value: '江苏' },\r\n        { label: '四川', value: '四川' },\r\n        { label: '河南', value: '河南' },\r\n        { label: '山东', value: '山东' },\r\n        { label: '湖北', value: '湖北' },\r\n        { label: '湖南', value: '湖南' },\r\n        { label: '河北', value: '河北' },\r\n        { label: '福建', value: '福建' },\r\n        { label: '安徽', value: '安徽' },\r\n        { label: '江西', value: '江西' },\r\n        { label: '辽宁', value: '辽宁' },\r\n        { label: '天津', value: '天津' },\r\n        { label: '重庆', value: '重庆' },\r\n        { label: '山西', value: '山西' },\r\n        { label: '陕西', value: '陕西' },\r\n        { label: '吉林', value: '吉林' },\r\n        { label: '黑龙江', value: '黑龙江' },\r\n        { label: '内蒙古', value: '内蒙古' },\r\n        { label: '广西', value: '广西' },\r\n        { label: '海南', value: '海南' },\r\n        { label: '贵州', value: '贵州' },\r\n        { label: '云南', value: '云南' },\r\n        { label: '西藏', value: '西藏' },\r\n        { label: '甘肃', value: '甘肃' },\r\n        { label: '青海', value: '青海' },\r\n        { label: '宁夏', value: '宁夏' },\r\n        { label: '新疆', value: '新疆' }\r\n      ],\r\n      currentProvince: '',\r\n      showAdvancedFilter: false,\r\n      currentFilters: {},\r\n      searchKeyword: '',\r\n      genderOptions: [\r\n        { label: '不限', icon: 'person' },\r\n        { label: '男', icon: 'person' },\r\n        { label: '女', icon: 'person-filled' }\r\n      ],\r\n      genderIndex: 0,\r\n      ageRange: {\r\n        min: 16,\r\n        max: 99\r\n      },\r\n      ageOptions: [\r\n        { label: '18-25岁', min: 18, max: 25 },\r\n        { label: '26-30岁', min: 26, max: 30 },\r\n        { label: '31-35岁', min: 31, max: 35 },\r\n        { label: '36-40岁', min: 36, max: 40 },\r\n        { label: '40岁以上', min: 40, max: 99 },\r\n        { label: '自定义', min: null, max: null }\r\n      ],\r\n      showCustomAge: false,\r\n      searchSuggestions: ['游戏', '电影', '运动', '音乐', '旅行', '美食', '读书', '摄影'],\r\n      pagination: {\r\n        page: 1,\r\n        size: 10, // 减少每页数量，提升加载速度\r\n        total: 0,\r\n        pages: 0\r\n      },\r\n      loading: false, // 初始加载状态为false\r\n      refreshing: false,\r\n      hasMore: true,\r\n      debounceTimer: null\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.initPage()\r\n  },\r\n  onShow() {\r\n    // 页面显示时刷新数据\r\n    this.refreshData()\r\n  },\r\n\r\n  methods: {\r\n    // 跳转到网络测试页面\r\n    goToNetworkTest() {\r\n      uni.navigateTo({\r\n        url: '/pages/test-network'\r\n      })\r\n    },\r\n\r\n    async initPage() {\r\n      await this.loadSiteConfig()\r\n      await this.loadFilterOptions()\r\n      await this.loadSubmissionList(true)\r\n    },\r\n\r\n    async loadSiteConfig() {\r\n      try {\r\n        const res = await this.$store.dispatch('system/getSystemConfig')\r\n        if (res && res.site_name) {\r\n          this.siteName = res.site_name\r\n        }\r\n      } catch (error) {\r\n        console.error('加载网站配置失败:', error)\r\n      }\r\n    },\r\n\r\n    async loadFilterOptions() {\r\n      try {\r\n        const res = await submissionApi.getFilterOptions()\r\n        if (res.code === 200) {\r\n          this.filterOptions = res.data\r\n        }\r\n      } catch (error) {\r\n        console.error('加载筛选选项失败:', error)\r\n      }\r\n    },\r\n\r\n    async loadSubmissionList(reset = false) {\r\n      if (this.loading) return\r\n\r\n      if (reset) {\r\n        this.pagination.page = 1\r\n        this.submissionList = []\r\n        this.hasMore = true\r\n      }\r\n\r\n      if (!this.hasMore) return\r\n\r\n      this.loading = true\r\n\r\n      try {\r\n        const params = {\r\n          page: this.pagination.page,\r\n          size: this.pagination.size,\r\n          ...this.currentFilters\r\n        }\r\n\r\n        // 添加搜索关键词\r\n        if (this.searchKeyword) {\r\n          params.keyword = this.searchKeyword\r\n        }\r\n\r\n        // 添加省份筛选\r\n        if (this.currentProvince) {\r\n          params.province = this.currentProvince\r\n        }\r\n\r\n        // 添加性别筛选\r\n        if (this.genderIndex > 0) {\r\n          params.gender = this.genderOptions[this.genderIndex].label\r\n        }\r\n\r\n        // 添加年龄筛选\r\n        if (this.ageRange.min > 16) {\r\n          params.min_age = this.ageRange.min\r\n        }\r\n        if (this.ageRange.max < 99) {\r\n          params.max_age = this.ageRange.max\r\n        }\r\n\r\n        const res = await submissionApi.getSubmissionList(params)\r\n\r\n        if (res.code === 200) {\r\n          const { items, total, pages } = res.data\r\n\r\n          if (reset) {\r\n            this.submissionList = items\r\n          } else {\r\n            // 加载更多时，需要去重（特别是置顶投稿）\r\n            const existingIds = new Set(this.submissionList.map(item => item.id))\r\n            const newItems = items.filter(item => !existingIds.has(item.id))\r\n            this.submissionList.push(...newItems)\r\n          }\r\n\r\n          this.pagination.total = total\r\n          this.pagination.pages = pages\r\n          this.hasMore = this.pagination.page < pages\r\n\r\n          if (!reset) {\r\n            this.pagination.page++\r\n          }\r\n\r\n          // 应用本地筛选\r\n          this.applyLocalFilters()\r\n        }\r\n      } catch (error) {\r\n        console.error('加载投稿列表失败:', error)\r\n        uni.showToast({\r\n          title: '加载失败',\r\n          icon: 'none'\r\n        })\r\n      } finally {\r\n        this.loading = false\r\n        this.refreshing = false\r\n      }\r\n    },\r\n\r\n    // 应用本地筛选（仅用于排序，筛选已在后端完成）\r\n    applyLocalFilters() {\r\n      let filtered = [...this.submissionList]\r\n\r\n      // 排序：置顶优先，然后按创建时间倒序\r\n      filtered.sort((a, b) => {\r\n        if (a.is_top && !b.is_top) return -1\r\n        if (!a.is_top && b.is_top) return 1\r\n        return new Date(b.create_datetime) - new Date(a.create_datetime)\r\n      })\r\n\r\n      this.filteredSubmissionList = filtered\r\n    },\r\n\r\n    // 省份选择\r\n    selectProvince(province) {\r\n      this.currentProvince = province.value\r\n      // 使用防抖延迟加载\r\n      this.debounceLoadData()\r\n    },\r\n\r\n    // 切换高级筛选显示\r\n    toggleAdvancedFilter() {\r\n      this.showAdvancedFilter = !this.showAdvancedFilter\r\n    },\r\n\r\n    // 性别选择\r\n    selectGender(index) {\r\n      this.genderIndex = index\r\n      // 使用防抖延迟加载\r\n      this.debounceLoadData()\r\n    },\r\n\r\n    // 选择年龄选项\r\n    selectAgeOption(option) {\r\n      if (option.min === null && option.max === null) {\r\n        // 自定义选项\r\n        this.showCustomAge = true\r\n      } else {\r\n        this.showCustomAge = false\r\n        this.ageRange.min = option.min\r\n        this.ageRange.max = option.max\r\n        this.loadSubmissionList(true) // 重新从后端加载数据\r\n      }\r\n    },\r\n\r\n    // 检查年龄选项是否激活\r\n    isAgeOptionActive(option) {\r\n      if (option.min === null && option.max === null) {\r\n        return this.showCustomAge\r\n      }\r\n      return this.ageRange.min === option.min && this.ageRange.max === option.max\r\n    },\r\n\r\n    // 设置无年龄上限\r\n    setNoAgeLimit() {\r\n      this.ageRange.max = 99\r\n      this.loadSubmissionList(true) // 重新从后端加载数据\r\n    },\r\n\r\n    // 最小年龄变化\r\n    handleMinAgeChange(e) {\r\n      this.ageRange.min = e.detail.value\r\n      // 确保最小年龄不大于最大年龄（除非最大年龄是99）\r\n      if (this.ageRange.max !== 99 && this.ageRange.min > this.ageRange.max) {\r\n        this.ageRange.max = this.ageRange.min\r\n      }\r\n      this.loadSubmissionList(true) // 重新从后端加载数据\r\n    },\r\n\r\n    // 最大年龄变化\r\n    handleMaxAgeChange(e) {\r\n      this.ageRange.max = e.detail.value\r\n      // 确保最大年龄不小于最小年龄\r\n      if (this.ageRange.max < this.ageRange.min) {\r\n        this.ageRange.min = this.ageRange.max\r\n      }\r\n      this.loadSubmissionList(true) // 重新从后端加载数据\r\n    },\r\n\r\n    // 计算年龄在滑块上的百分比位置\r\n    getAgePercentage(age) {\r\n      const minAge = 16\r\n      const maxAge = 50\r\n      return ((age - minAge) / (maxAge - minAge)) * 100\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch() {\r\n      this.applyLocalFilters()\r\n    },\r\n\r\n    // 应用搜索建议\r\n    applySuggestion(tag) {\r\n      this.searchKeyword = tag\r\n      this.applyLocalFilters()\r\n    },\r\n\r\n    // 重置筛选\r\n    resetFilters() {\r\n      this.currentProvince = ''\r\n      this.searchKeyword = ''\r\n      this.genderIndex = 0\r\n      this.ageRange = { min: 18, max: 35 }\r\n      this.applyLocalFilters()\r\n    },\r\n\r\n    // 应用筛选\r\n    applyFilters() {\r\n      this.applyLocalFilters()\r\n      uni.showToast({\r\n        title: '筛选已应用',\r\n        icon: 'success'\r\n      })\r\n    },\r\n\r\n    handleFilterChange(filters) {\r\n      this.currentFilters = filters\r\n      this.loadSubmissionList(true)\r\n    },\r\n\r\n    showSearch() {\r\n      this.$refs.searchPopup.open()\r\n    },\r\n\r\n    hideSearch() {\r\n      this.$refs.searchPopup.close()\r\n    },\r\n\r\n    handleSearch() {\r\n      this.hideSearch()\r\n      this.loadSubmissionList(true)\r\n    },\r\n\r\n    onRefresh() {\r\n      this.refreshing = true\r\n      this.loadSubmissionList(true)\r\n    },\r\n\r\n    loadMore() {\r\n      if (this.hasMore && !this.loading) {\r\n        this.loadSubmissionList()\r\n      }\r\n    },\r\n\r\n    refreshData() {\r\n      this.loadSubmissionList(true)\r\n    },\r\n\r\n    goToDetail(submission) {\r\n      uni.navigateTo({\r\n        url: `/pages/submission/detail?id=${submission.id}`\r\n      })\r\n    },\r\n\r\n    // 底部菜单切换\r\n    switchTab(tab) {\r\n      switch(tab) {\r\n        case 'home':\r\n          // 当前页面，不需要跳转\r\n          break\r\n        case 'add':\r\n          uni.navigateTo({\r\n            url: '/pages/submission/create'\r\n          })\r\n          break\r\n        case 'profile':\r\n          uni.navigateTo({\r\n            url: '/pages/mine/index'\r\n          })\r\n          break\r\n      }\r\n    },\r\n\r\n    // 防抖加载数据\r\n    debounceLoadData() {\r\n      if (this.debounceTimer) {\r\n        clearTimeout(this.debounceTimer)\r\n      }\r\n      this.debounceTimer = setTimeout(() => {\r\n        this.loadSubmissionList(true)\r\n      }, 300) // 300ms防抖\r\n    }\r\n  },\r\n\r\n  // 初始化时应用筛选\r\n  watch: {\r\n    submissionList: {\r\n      handler() {\r\n        this.applyLocalFilters()\r\n      },\r\n      immediate: true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);\r\n  padding-bottom: 80rpx; // 适配新的底部菜单栏高度\r\n}\r\n\r\n.custom-navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n  @include glass-effect(0.6);\r\n  padding-top: var(--status-bar-height);\r\n\r\n  .navbar-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24rpx 40rpx;\r\n\r\n    .navbar-left {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16rpx;\r\n    }\r\n\r\n    .navbar-title {\r\n      font-size: 48rpx;\r\n      font-weight: 800;\r\n      background: linear-gradient(45deg, #f78ca0, #f9748f, #fe9a8b, #ffc8a9);\r\n      background-clip: text;\r\n      -webkit-background-clip: text;\r\n      color: transparent;\r\n    }\r\n\r\n    .navbar-actions {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8rpx;\r\n      margin-right: 20rpx; // 增加右边距，避免被系统按钮遮挡\r\n\r\n      .action-btn {\r\n        width: 60rpx;\r\n        height: 60rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        background: rgba(255, 255, 255, 0.1);\r\n        transition: all 0.3s ease;\r\n\r\n        &:active {\r\n          background: rgba(255, 255, 255, 0.2);\r\n          transform: scale(0.95);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 分类区域\r\n.category-section {\r\n  padding: 24rpx 20rpx; // 缩小内边距\r\n\r\n  .section-title {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 20rpx; // 缩小间距\r\n    gap: 12rpx;\r\n\r\n    .title-text {\r\n      font-size: 28rpx; // 缩小字体\r\n      font-weight: 700;\r\n      color: $soul-gray-700;\r\n    }\r\n  }\r\n\r\n  .category-scroll {\r\n\r\n    .category-buttons-container {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 12rpx;\r\n      padding: 0 12rpx;\r\n\r\n      .category-row {\r\n        display: flex;\r\n        gap: 12rpx;\r\n        overflow-x: auto;\r\n        white-space: nowrap;\r\n\r\n        .category-btn {\r\n          background: rgba(255, 255, 255, 0.5);\r\n          backdrop-filter: blur(10rpx) saturate(150%);\r\n          border: 1rpx solid rgba(209, 213, 219, 0.2);\r\n          border-radius: 12rpx;\r\n          padding: 8rpx 16rpx; // 进一步缩小内边距\r\n          transition: all 0.3s ease;\r\n          box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\r\n          flex-shrink: 0; // 防止压缩\r\n\r\n          &:hover {\r\n            background: rgba(255, 255, 255, 0.8);\r\n            transform: translateY(-4rpx);\r\n            box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);\r\n          }\r\n\r\n          &.active {\r\n            background: linear-gradient(to right, #fbc2eb, #a6c1ee);\r\n            color: white;\r\n            box-shadow: 0 8rpx 30rpx rgba(172, 191, 233, 0.4);\r\n\r\n            .category-text {\r\n              color: white;\r\n              font-weight: 700;\r\n            }\r\n          }\r\n\r\n          .category-text {\r\n            font-size: 22rpx; // 进一步缩小字体\r\n            font-weight: 600;\r\n            color: #4b5563;\r\n            white-space: nowrap;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n// 投稿区域\r\n.posts-section {\r\n  .section-title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    padding: 0 20rpx;\r\n\r\n    .title-left {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 12rpx;\r\n\r\n      .title-text {\r\n        font-size: 28rpx;\r\n        font-weight: 700;\r\n        color: $soul-gray-700;\r\n      }\r\n    }\r\n\r\n    .filter-btn-container {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8rpx;\r\n      background: rgba(255, 255, 255, 0.8);\r\n      border: 1rpx solid #f78ca0;\r\n      border-radius: 20rpx;\r\n      padding: 8rpx 16rpx;\r\n      transition: all 0.3s ease;\r\n\r\n      &:hover {\r\n        background: rgba(247, 140, 160, 0.1);\r\n        transform: translateY(-2rpx);\r\n      }\r\n\r\n      .filter-btn-text {\r\n        font-size: 22rpx;\r\n        color: #f78ca0;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n  }\r\n\r\n  .advanced-filter-section {\r\n    @include glass-effect(0.6);\r\n    margin: 0 20rpx 20rpx;\r\n    border-radius: 24rpx;\r\n    padding: 24rpx;\r\n    box-shadow: 0 8rpx 30rpx rgba(0,0,0,0.08);\r\n\r\n    .filter-grid {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20rpx;\r\n    }\r\n\r\n    .filter-item {\r\n      .filter-label {\r\n        display: block;\r\n        font-weight: 600;\r\n        color: #718096;\r\n        margin-bottom: 12rpx;\r\n        font-size: 24rpx;\r\n      }\r\n\r\n      .filter-input {\r\n        width: 100%;\r\n        padding: 20rpx 16rpx;\r\n        border-radius: 12rpx;\r\n        border: 1rpx solid rgba(209, 213, 219, 0.5);\r\n        background-color: rgba(255, 255, 255, 0.8);\r\n        font-size: 28rpx;\r\n        min-height: 80rpx;\r\n        box-sizing: border-box;\r\n        transition: all 0.2s ease;\r\n\r\n        &:focus {\r\n          border-color: #a6c1ee;\r\n          box-shadow: 0 0 0 4rpx rgba(166, 193, 238, 0.3);\r\n        }\r\n      }\r\n\r\n      .gender-options {\r\n        display: flex;\r\n        gap: 16rpx;\r\n\r\n        .gender-option {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          gap: 8rpx;\r\n          padding: 20rpx 16rpx;\r\n          border-radius: 12rpx;\r\n          border: 1rpx solid rgba(209, 213, 219, 0.5);\r\n          background-color: rgba(255, 255, 255, 0.8);\r\n          transition: all 0.2s ease;\r\n          cursor: pointer;\r\n\r\n          &.active {\r\n            border-color: #f78ca0;\r\n            background-color: rgba(247, 140, 160, 0.1);\r\n            box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.2);\r\n          }\r\n\r\n          .gender-text {\r\n            font-size: 24rpx;\r\n            color: $soul-gray-600;\r\n            font-weight: 500;\r\n\r\n            &.active {\r\n              color: #f78ca0;\r\n              font-weight: 600;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .age-range-container {\r\n        .age-display {\r\n          text-align: center;\r\n          margin-bottom: 24rpx;\r\n\r\n          .age-text {\r\n            font-size: 32rpx;\r\n            color: $soul-gray-700;\r\n            font-weight: 600;\r\n            background: linear-gradient(135deg, #f78ca0, #a6c1ee);\r\n            background-clip: text;\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n          }\r\n        }\r\n\r\n        .age-quick-select {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          gap: 16rpx;\r\n          margin-bottom: 32rpx;\r\n\r\n          .age-option {\r\n            flex: 1;\r\n            min-width: 120rpx;\r\n            height: 60rpx;\r\n            background: rgba(255, 255, 255, 0.8);\r\n            border: 2rpx solid #e5e7eb;\r\n            border-radius: 30rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.3s ease;\r\n            backdrop-filter: blur(10rpx);\r\n\r\n            &.active {\r\n              background: linear-gradient(135deg, #f78ca0, #a6c1ee);\r\n              border-color: transparent;\r\n              transform: translateY(-2rpx);\r\n              box-shadow: 0 8rpx 20rpx rgba(247, 140, 160, 0.3);\r\n\r\n              .age-option-text {\r\n                color: #fff;\r\n                font-weight: 600;\r\n              }\r\n            }\r\n\r\n            .age-option-text {\r\n              font-size: 24rpx;\r\n              color: $soul-gray-700;\r\n              font-weight: 500;\r\n              transition: all 0.3s ease;\r\n            }\r\n          }\r\n        }\r\n\r\n        .custom-age-range {\r\n          margin-top: 24rpx;\r\n          padding: 24rpx;\r\n          background: rgba(255, 255, 255, 0.6);\r\n          border-radius: 20rpx;\r\n          backdrop-filter: blur(10rpx);\r\n        }\r\n\r\n        .age-range-visual {\r\n          margin: 32rpx 0;\r\n\r\n          .age-line {\r\n            position: relative;\r\n            height: 8rpx;\r\n            margin: 40rpx 0 32rpx;\r\n\r\n            .age-line-bg {\r\n              position: absolute;\r\n              top: 0;\r\n              left: 0;\r\n              right: 0;\r\n              height: 8rpx;\r\n              background: #e5e7eb;\r\n              border-radius: 4rpx;\r\n            }\r\n\r\n            .age-line-active {\r\n              position: absolute;\r\n              top: 0;\r\n              height: 8rpx;\r\n              background: linear-gradient(135deg, #f78ca0, #a6c1ee);\r\n              border-radius: 4rpx;\r\n              transition: all 0.3s ease;\r\n            }\r\n\r\n            .age-thumb {\r\n              position: absolute;\r\n              top: -16rpx;\r\n              width: 40rpx;\r\n              height: 40rpx;\r\n              background: #fff;\r\n              border: 3rpx solid #f78ca0;\r\n              border-radius: 50%;\r\n              transform: translateX(-50%);\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\r\n              transition: all 0.3s ease;\r\n\r\n              &.age-thumb-max {\r\n                border-color: #a6c1ee;\r\n              }\r\n\r\n              .age-thumb-text {\r\n                position: absolute;\r\n                top: -36rpx;\r\n                font-size: 20rpx;\r\n                color: $soul-gray-700;\r\n                font-weight: 600;\r\n                white-space: nowrap;\r\n                background: rgba(255, 255, 255, 0.9);\r\n                padding: 4rpx 8rpx;\r\n                border-radius: 8rpx;\r\n                box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n              }\r\n            }\r\n          }\r\n\r\n          .age-scale {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            margin-top: 16rpx;\r\n\r\n            .scale-text {\r\n              font-size: 20rpx;\r\n              color: $soul-gray-500;\r\n              font-weight: 500;\r\n            }\r\n          }\r\n        }\r\n\r\n        .slider-container {\r\n          margin: 20rpx 0;\r\n\r\n          .slider-label {\r\n            display: block;\r\n            font-size: 22rpx;\r\n            color: $soul-gray-600;\r\n            margin-bottom: 12rpx;\r\n            font-weight: 500;\r\n          }\r\n\r\n          .age-slider {\r\n            width: 100%;\r\n          }\r\n\r\n          .no-limit-option {\r\n            margin-top: 16rpx;\r\n            text-align: center;\r\n\r\n            .no-limit-text {\r\n              font-size: 24rpx;\r\n              color: $soul-gray-600;\r\n              padding: 12rpx 24rpx;\r\n              border: 2rpx solid #e5e7eb;\r\n              border-radius: 20rpx;\r\n              background: rgba(255, 255, 255, 0.8);\r\n              transition: all 0.3s ease;\r\n\r\n              &.active {\r\n                background: linear-gradient(135deg, #f78ca0, #a6c1ee);\r\n                border-color: transparent;\r\n                color: #fff;\r\n                font-weight: 600;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .filter-actions {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      gap: 16rpx;\r\n      margin-top: 12rpx;\r\n\r\n      .filter-btn {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8rpx;\r\n        padding: 16rpx 24rpx;\r\n        border-radius: 12rpx;\r\n        font-weight: 600;\r\n        font-size: 22rpx;\r\n        transition: all 0.2s ease;\r\n\r\n        &.reset {\r\n          background: linear-gradient(to right, #868f96 0%, #596164 100%);\r\n          color: white;\r\n        }\r\n\r\n        &.apply {\r\n          background: linear-gradient(to right, #fbc2eb, #a6c1ee);\r\n          color: white;\r\n        }\r\n\r\n        &:hover {\r\n          opacity: 0.85;\r\n          transform: translateY(-2rpx);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.submission-list {\r\n  flex: 1;\r\n  height: calc(100vh - 500rpx); // 调整高度\r\n}\r\n\r\n.list-container {\r\n  padding: 0 20rpx 140rpx 20rpx; // 增加底部内边距，避免被底部菜单栏遮盖\r\n\r\n  .initial-loading {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 120rpx 40rpx;\r\n    gap: 20rpx;\r\n\r\n    .loading-text {\r\n      font-size: 26rpx;\r\n      color: $soul-gray-500;\r\n    }\r\n\r\n    uni-icons {\r\n      animation: spin 1s linear infinite;\r\n    }\r\n  }\r\n}\r\n\r\n.submission-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 16rpx; // 缩小间距\r\n}\r\n\r\n.loading-container {\r\n  grid-column: 1 / -1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80rpx 0;\r\n  color: #f78ca0;\r\n\r\n  .loading-icon {\r\n    animation: spin 2s linear infinite;\r\n    margin-bottom: 24rpx;\r\n  }\r\n\r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    font-weight: 600;\r\n  }\r\n}\r\n\r\n.empty-container {\r\n  grid-column: 1 / -1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80rpx 40rpx;\r\n  text-align: center;\r\n\r\n  .empty-icon {\r\n    margin-bottom: 24rpx;\r\n  }\r\n\r\n  .empty-title {\r\n    font-size: 32rpx;\r\n    color: $soul-gray-700;\r\n    font-weight: 600;\r\n    margin-bottom: 12rpx;\r\n  }\r\n\r\n  .empty-subtitle {\r\n    font-size: 26rpx;\r\n    color: $soul-gray-500;\r\n    margin-bottom: 40rpx;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  .search-suggestions {\r\n    width: 100%;\r\n    margin-bottom: 40rpx;\r\n\r\n    .suggestions-title {\r\n      font-size: 24rpx;\r\n      color: $soul-gray-600;\r\n      margin-bottom: 20rpx;\r\n      display: block;\r\n    }\r\n\r\n    .suggestions-tags {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 16rpx;\r\n      justify-content: center;\r\n\r\n      .suggestion-tag {\r\n        padding: 12rpx 24rpx;\r\n        background: rgba(247, 140, 160, 0.1);\r\n        border: 2rpx solid rgba(247, 140, 160, 0.3);\r\n        border-radius: 20rpx;\r\n        transition: all 0.3s ease;\r\n\r\n        &:active {\r\n          background: rgba(247, 140, 160, 0.2);\r\n          transform: scale(0.95);\r\n        }\r\n\r\n        .tag-text {\r\n          font-size: 24rpx;\r\n          color: #f78ca0;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .reset-filters-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 12rpx;\r\n    padding: 16rpx 32rpx;\r\n    background: rgba(255, 255, 255, 0.8);\r\n    border: 2rpx solid #f78ca0;\r\n    border-radius: 25rpx;\r\n    transition: all 0.3s ease;\r\n    backdrop-filter: blur(10rpx);\r\n\r\n    &:active {\r\n      background: rgba(247, 140, 160, 0.1);\r\n      transform: scale(0.95);\r\n    }\r\n\r\n    .reset-text {\r\n      font-size: 26rpx;\r\n      color: #f78ca0;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n// 底部菜单栏\r\n.bottom-tabbar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20rpx);\r\n  display: flex;\r\n  justify-content: space-around;\r\n  align-items: center;\r\n  padding: 16rpx 0;\r\n  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));\r\n  border-top: 1rpx solid rgba(209, 213, 219, 0.2);\r\n  z-index: 1000;\r\n  height: 120rpx;\r\n\r\n  .tab-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: 8rpx;\r\n    padding: 12rpx 20rpx;\r\n    transition: all 0.3s ease;\r\n    min-width: 80rpx;\r\n\r\n    &.active {\r\n      .tab-text {\r\n        color: #f78ca0;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n\r\n    .add-btn {\r\n      width: 56rpx;\r\n      height: 56rpx;\r\n      background: linear-gradient(135deg, #f78ca0, #fbc2eb);\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      box-shadow: 0 6rpx 20rpx rgba(247, 140, 160, 0.4);\r\n      transform: translateY(-8rpx);\r\n    }\r\n\r\n    .tab-text {\r\n      font-size: 24rpx;\r\n      color: $soul-gray-500;\r\n      font-weight: 500;\r\n      text-align: center;\r\n      line-height: 1.2;\r\n\r\n      &.active {\r\n        color: #f78ca0;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.search-container {\r\n  padding: 20rpx;\r\n  padding-top: calc(var(--status-bar-height) + 20rpx);\r\n  @include glass-effect(0.9);\r\n  border-bottom: 1rpx solid $soul-gray-200;\r\n\r\n  .search-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    background: rgba(255, 255, 255, 0.8);\r\n    border-radius: 24rpx;\r\n    padding: 20rpx 24rpx;\r\n\r\n    .search-input {\r\n      flex: 1;\r\n      margin: 0 16rpx;\r\n      font-size: 28rpx;\r\n      color: $soul-gray-800;\r\n    }\r\n\r\n    .cancel-btn {\r\n      font-size: 28rpx;\r\n      color: $soul-primary;\r\n      padding: 8rpx 16rpx;\r\n    }\r\n  }\r\n}\r\n\r\n// 动画\r\n@keyframes spin {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=2a183b29&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=2a183b29&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752120110334\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}