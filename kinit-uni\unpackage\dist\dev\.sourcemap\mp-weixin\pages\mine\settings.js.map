{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/settings.vue?7d77", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/settings.vue?4d3a", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/settings.vue?a556", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/settings.vue?0b04", "uni-app:///pages/mine/settings.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/settings.vue?f13d", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/settings.vue?e9b6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "notificationSettings", "push", "sound", "vibrate", "cacheSize", "computed", "onLoad", "methods", "goBack", "uni", "loadSettings", "toggleNotification", "title", "icon", "calculateCacheSize", "clearCache", "content", "success", "setTimeout", "checkUpdate", "goToProfile", "url", "goToPrivacy", "goToHelp", "goToAbout", "contactUs", "itemList", "phoneNumber", "logout", "res", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqoB,CAAgB,0pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACgMzpB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC,4BACA,8CACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;IACA;IAEAC;MACA;MACA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEAC;MACA;MACA;MACAF;MAEAA;QACAG;QACAC;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MAAA;MACAN;QACAG;QACAI;QACAC;UACA;YACAR;cAAAG;YAAA;YAEAM;cACAT;cACA;cACAA;gBACAG;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IAEAM;MACAV;QAAAG;MAAA;MAEAM;QACAT;QACAA;UACAG;UACAC;QACA;MACA;IACA;IAEAO;MACAX;QACAY;MACA;IACA;IAEAC;MACAb;QACAY;MACA;IACA;IAEAE;MACAd;QACAY;MACA;IACA;IAEAG;MACAf;QACAY;MACA;IACA;IAEAI;MACAhB;QACAiB;QACAT;UACA;YACA;cACAR;gBACAG;gBACAC;cACA;cACA;YACA;cACAJ;gBACAV;gBACAkB;kBACAR;oBACAG;oBACAC;kBACA;gBACA;cACA;cACA;YACA;cACAJ;gBACAkB;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEAC;MAAA;MACAnB;QACAG;QACAI;QACAC;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAY;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA;oBAAA,OAEA;kBAAA;oBACApB;sBACAG;sBACAC;oBACA;oBAEAK;sBACAT;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAqB;oBACArB;sBACAG;sBACAC;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzWA;AAAA;AAAA;AAAA;AAAouC,CAAgB,isCAAG,EAAC,C;;;;;;;;;;;ACAxvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/settings.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/settings.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./settings.vue?vue&type=template&id=68887b36&scoped=true&\"\nvar renderjs\nimport script from \"./settings.vue?vue&type=script&lang=js&\"\nexport * from \"./settings.vue?vue&type=script&lang=js&\"\nimport style0 from \"./settings.vue?vue&type=style&index=0&id=68887b36&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"68887b36\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/settings.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./settings.vue?vue&type=template&id=68887b36&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./settings.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./settings.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"settings-page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-content\">\n        <view class=\"navbar-left\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n        </view>\n        <text class=\"navbar-title\">设置</text>\n        <view class=\"navbar-right\"></view>\n      </view>\n    </view>\n\n    <scroll-view scroll-y=\"true\" class=\"settings-content\">\n      <!-- 账户设置 -->\n      <view class=\"settings-section\">\n        <view class=\"section-title\">账户设置</view>\n        <view class=\"settings-card\">\n          <view class=\"setting-item\" @click=\"goToProfile\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"person\" size=\"24\" color=\"#f78ca0\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">个人资料</text>\n              <text class=\"setting-desc\">编辑个人信息</text>\n            </view>\n            <view class=\"setting-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n\n          <view class=\"setting-item\" @click=\"goToPrivacy\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"locked\" size=\"24\" color=\"#a6c1ee\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">隐私设置</text>\n              <text class=\"setting-desc\">管理隐私权限</text>\n            </view>\n            <view class=\"setting-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 通知设置 -->\n      <view class=\"settings-section\">\n        <view class=\"section-title\">通知设置</view>\n        <view class=\"settings-card\">\n          <view class=\"setting-item\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"notification\" size=\"24\" color=\"#feb47b\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">推送通知</text>\n              <text class=\"setting-desc\">接收新消息通知</text>\n            </view>\n            <switch \n              :checked=\"notificationSettings.push\" \n              @change=\"toggleNotification('push', $event)\"\n              color=\"#f78ca0\"\n            />\n          </view>\n\n          <view class=\"setting-item\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"sound\" size=\"24\" color=\"#c8a8e9\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">声音提醒</text>\n              <text class=\"setting-desc\">播放提示音</text>\n            </view>\n            <switch \n              :checked=\"notificationSettings.sound\" \n              @change=\"toggleNotification('sound', $event)\"\n              color=\"#f78ca0\"\n            />\n          </view>\n\n          <view class=\"setting-item\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"vibrate\" size=\"24\" color=\"#10b981\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">震动提醒</text>\n              <text class=\"setting-desc\">震动反馈</text>\n            </view>\n            <switch \n              :checked=\"notificationSettings.vibrate\" \n              @change=\"toggleNotification('vibrate', $event)\"\n              color=\"#f78ca0\"\n            />\n          </view>\n        </view>\n      </view>\n\n      <!-- 应用设置 -->\n      <view class=\"settings-section\">\n        <view class=\"section-title\">应用设置</view>\n        <view class=\"settings-card\">\n          <view class=\"setting-item\" @click=\"clearCache\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"trash\" size=\"24\" color=\"#ef4444\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">清除缓存</text>\n              <text class=\"setting-desc\">{{ cacheSize }}</text>\n            </view>\n            <view class=\"setting-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n\n          <view class=\"setting-item\" @click=\"checkUpdate\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"reload\" size=\"24\" color=\"#3b82f6\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">检查更新</text>\n              <text class=\"setting-desc\">当前版本 v1.0.0</text>\n            </view>\n            <view class=\"setting-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 帮助与支持 -->\n      <view class=\"settings-section\">\n        <view class=\"section-title\">帮助与支持</view>\n        <view class=\"settings-card\">\n          <view class=\"setting-item\" @click=\"goToHelp\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"help\" size=\"24\" color=\"#8b5cf6\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">使用帮助</text>\n              <text class=\"setting-desc\">常见问题解答</text>\n            </view>\n            <view class=\"setting-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n\n          <view class=\"setting-item\" @click=\"contactUs\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"chat\" size=\"24\" color=\"#06b6d4\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">联系我们</text>\n              <text class=\"setting-desc\">意见反馈</text>\n            </view>\n            <view class=\"setting-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n\n          <view class=\"setting-item\" @click=\"goToAbout\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"info\" size=\"24\" color=\"#10b981\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">关于我们</text>\n              <text class=\"setting-desc\">了解更多信息</text>\n            </view>\n            <view class=\"setting-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 退出登录 -->\n      <view v-if=\"isLoggedIn\" class=\"settings-section\">\n        <view class=\"settings-card\">\n          <view class=\"setting-item logout\" @click=\"logout\">\n            <view class=\"setting-icon\">\n              <uni-icons type=\"close\" size=\"24\" color=\"#ef4444\"></uni-icons>\n            </view>\n            <view class=\"setting-content\">\n              <text class=\"setting-title\">退出登录</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  data() {\n    return {\n      notificationSettings: {\n        push: true,\n        sound: true,\n        vibrate: false\n      },\n      cacheSize: '12.5MB'\n    }\n  },\n  computed: {\n    ...mapGetters('user', ['isLoggedIn'])\n  },\n  onLoad() {\n    this.loadSettings()\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n\n    loadSettings() {\n      // 从本地存储加载设置\n      const settings = uni.getStorageSync('notificationSettings')\n      if (settings) {\n        this.notificationSettings = { ...this.notificationSettings, ...settings }\n      }\n      \n      // 计算缓存大小\n      this.calculateCacheSize()\n    },\n\n    toggleNotification(type, event) {\n      this.notificationSettings[type] = event.detail.value\n      // 保存到本地存储\n      uni.setStorageSync('notificationSettings', this.notificationSettings)\n      \n      uni.showToast({\n        title: event.detail.value ? '已开启' : '已关闭',\n        icon: 'success'\n      })\n    },\n\n    calculateCacheSize() {\n      // 模拟计算缓存大小\n      const sizes = ['8.2MB', '12.5MB', '15.8MB', '6.3MB', '20.1MB']\n      this.cacheSize = sizes[Math.floor(Math.random() * sizes.length)]\n    },\n\n    clearCache() {\n      uni.showModal({\n        title: '清除缓存',\n        content: '确定要清除应用缓存吗？这将删除临时文件和图片缓存。',\n        success: (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: '清理中...' })\n            \n            setTimeout(() => {\n              uni.hideLoading()\n              this.cacheSize = '0MB'\n              uni.showToast({\n                title: '缓存清理完成',\n                icon: 'success'\n              })\n            }, 1500)\n          }\n        }\n      })\n    },\n\n    checkUpdate() {\n      uni.showLoading({ title: '检查中...' })\n      \n      setTimeout(() => {\n        uni.hideLoading()\n        uni.showToast({\n          title: '已是最新版本',\n          icon: 'success'\n        })\n      }, 1000)\n    },\n\n    goToProfile() {\n      uni.navigateTo({\n        url: '/pages/mine/profile'\n      })\n    },\n\n    goToPrivacy() {\n      uni.navigateTo({\n        url: '/pages/mine/privacy'\n      })\n    },\n\n    goToHelp() {\n      uni.navigateTo({\n        url: '/pages/mine/help'\n      })\n    },\n\n    goToAbout() {\n      uni.navigateTo({\n        url: '/pages/mine/about'\n      })\n    },\n\n    contactUs() {\n      uni.showActionSheet({\n        itemList: ['微信客服', '邮箱反馈', '电话咨询'],\n        success: (res) => {\n          switch(res.tapIndex) {\n            case 0:\n              uni.showToast({\n                title: '微信客服功能开发中',\n                icon: 'none'\n              })\n              break\n            case 1:\n              uni.setClipboardData({\n                data: '<EMAIL>',\n                success: () => {\n                  uni.showToast({\n                    title: '邮箱已复制',\n                    icon: 'success'\n                  })\n                }\n              })\n              break\n            case 2:\n              uni.makePhoneCall({\n                phoneNumber: '************'\n              })\n              break\n          }\n        }\n      })\n    },\n\n    logout() {\n      uni.showModal({\n        title: '退出登录',\n        content: '确定要退出登录吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              await this.$store.dispatch('user/logout')\n              uni.showToast({\n                title: '已退出登录',\n                icon: 'success'\n              })\n              \n              setTimeout(() => {\n                uni.navigateBack()\n              }, 1000)\n            } catch (error) {\n              console.error('退出登录失败:', error)\n              uni.showToast({\n                title: '退出失败，请重试',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.settings-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);\n}\n\n.custom-navbar {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  @include glass-effect(0.6);\n  padding-top: var(--status-bar-height);\n\n  .navbar-content {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 24rpx;\n\n    .navbar-left, .navbar-right {\n      width: 60rpx;\n      display: flex;\n      justify-content: center;\n    }\n\n    .navbar-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n  }\n}\n\n.settings-content {\n  height: calc(100vh - 120rpx);\n  padding: 20rpx;\n  padding-bottom: 40rpx;\n}\n\n.settings-section {\n  margin-bottom: 24rpx;\n\n  .section-title {\n    font-size: 24rpx;\n    font-weight: 600;\n    color: $soul-gray-600;\n    margin-bottom: 12rpx;\n    padding-left: 8rpx;\n  }\n\n  .settings-card {\n    @include glass-effect(0.6);\n    border-radius: 20rpx;\n    overflow: hidden;\n    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n\n    .setting-item {\n      display: flex;\n      align-items: center;\n      padding: 24rpx;\n      border-bottom: 1rpx solid rgba(0,0,0,0.05);\n      transition: all 0.3s ease;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:active:not(.logout) {\n        background: rgba(247, 140, 160, 0.05);\n      }\n\n      &.logout {\n        justify-content: center;\n\n        &:active {\n          background: rgba(239, 68, 68, 0.05);\n        }\n\n        .setting-content .setting-title {\n          color: #ef4444;\n          font-weight: 600;\n        }\n      }\n\n      .setting-icon {\n        width: 48rpx;\n        height: 48rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: rgba(255, 255, 255, 0.8);\n        border-radius: 12rpx;\n        margin-right: 16rpx;\n      }\n\n      .setting-content {\n        flex: 1;\n\n        .setting-title {\n          display: block;\n          font-size: 26rpx;\n          font-weight: 600;\n          color: $soul-gray-800;\n          margin-bottom: 4rpx;\n        }\n\n        .setting-desc {\n          font-size: 22rpx;\n          color: $soul-gray-500;\n        }\n      }\n\n      .setting-arrow {\n        opacity: 0.6;\n      }\n\n      switch {\n        transform: scale(0.8);\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./settings.vue?vue&type=style&index=0&id=68887b36&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./settings.vue?vue&type=style&index=0&id=68887b36&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752119233658\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}