{"version": 3, "sources": ["webpack:///E:/kaifa/投稿/kinit2/kinit-uni/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?bf23", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?b6f4", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?5a8e", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?b5f6", "uni-app:///uni_modules/uni-popup/components/uni-popup/uni-popup.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?5191", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?86e3"], "names": ["name", "components", "emits", "props", "animation", "type", "default", "isMaskClick", "maskClick", "backgroundColor", "safeArea", "maskBackgroundColor", "watch", "handler", "immediate", "isDesktop", "showPopup", "data", "duration", "ani", "showTrans", "popup<PERSON><PERSON><PERSON>", "popupHeight", "config", "top", "bottom", "center", "left", "right", "message", "dialog", "share", "maskClass", "position", "transClass", "maskShow", "mkclick", "popupstyle", "computed", "bg", "mounted", "uni", "windowWidth", "windowHeight", "windowTop", "screenHeight", "safeAreaInsets", "fixSize", "destroyed", "created", "methods", "setH5Visible", "closeMask", "disableMask", "clear", "e", "open", "clearTimeout", "direction", "console", "show", "close", "touchstart", "onTap", "paddingBottom", "display", "flexDirection", "justifyContent", "alignItems"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAsoB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuB1pB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA,eAuBA;EACAA;EACAC,aAIA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;EACA;EAEAM;IACA;AACA;AACA;IACAP;MACAQ;QACA;QACA;MACA;MACAC;IACA;IACAC;MACAF;QACA;QACA;MACA;MACAC;IACA;IACA;AACA;AACA;AACA;IACAN;MACAK;QACA;MACA;MACAC;IACA;IACAP;MACAM;QACA;MACA;MACAC;IACA;IACA;IACAE,qCAKA;EACA;EACAC;IACA;MACAC;MACAC;MACAH;MACAI;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAR;QACAD;QACAG;QACAC;QACAnB;MACA;MACAyB;QACAD;QACAN;QACAC;MACA;MACAO;MACAC;MACAC;IACA;EACA;EACAC;IACAvB;MACA;IACA;IACAwB;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA,4BAOAC;QANAC;QACAC;QACAC;QACAlC;QACAmC;QACAC;MAEA;MACA;MACA;MACA;QAEA;MAKA;QACA;MACA;IACA;IACAC;EAOA;EAEA;EACAC;IACA;EACA;EAQAC;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC,uCAKA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;IACAC;MAEAC;MAEA;IACA;IAEAC;MACA;MACA;QACAC;QACA;MACA;MACA;MACA;QACAC;MACA;MACA;QACAC;QACA;MACA;MACA;MACA;QACAC;QACAvD;MACA;IACA;IACAwD;MAAA;MACA;MACA;QACAD;QACAvD;MACA;MACAoD;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAK;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAvC;MAAA;MACA;MACA;MACA;QACAS;QACAN;QACAC;QACAnB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAgB;MACA;MACA;MACA;QACAQ;QACAN;QACAC;QACAH;QACAuC;QACAvD;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAiB;MACA;MACA;MACA;QACAO;QAEAgC;QACAC;QAEAzC;QACAE;QACAC;QACAJ;QACA2C;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAzC;MACA;MACA;MACA;QACAM;QACAN;QACAF;QACAD;QACAf;QAEAwD;QACAC;MAEA;MACA;MACA;MACA;MACA;IACA;IACAtC;MACA;MACA;MACA;QACAK;QACAR;QACAG;QACAJ;QACAf;QAEAwD;QACAC;MAEA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpaA;AAAA;AAAA;AAAA;AAA6sC,CAAgB,0qCAAG,EAAC,C;;;;;;;;;;;ACAjuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-popup/components/uni-popup/uni-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-popup.vue?vue&type=template&id=7c43d41b&\"\nvar renderjs\nimport script from \"./uni-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-popup.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=template&id=7c43d41b&\"", "var components\ntry {\n  components = {\n    uniTransition: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-transition/components/uni-transition/uni-transition\" */ \"@/uni_modules/uni-transition/components/uni-transition/uni-transition.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"showPopup\" class=\"uni-popup\" :class=\"[popupstyle, isDesktop ? 'fixforpc-z-index' : '']\">\r\n\t\t<view @touchstart=\"touchstart\">\r\n\t\t\t<uni-transition key=\"1\" v-if=\"maskShow\" name=\"mask\" mode-class=\"fade\" :styles=\"maskClass\"\r\n\t\t\t\t:duration=\"duration\" :show=\"showTrans\" @click=\"onTap\" />\r\n\t\t\t<uni-transition key=\"2\" :mode-class=\"ani\" name=\"content\" :styles=\"transClass\" :duration=\"duration\"\r\n\t\t\t\t:show=\"showTrans\" @click=\"onTap\">\r\n\t\t\t\t<view class=\"uni-popup__wrapper\" :style=\"{ backgroundColor: bg }\" :class=\"[popupstyle]\" @click=\"clear\">\r\n\t\t\t\t\t<slot />\r\n\t\t\t\t</view>\r\n\t\t\t</uni-transition>\r\n\t\t</view>\r\n\t\t<!-- #ifdef H5 -->\r\n\t\t<keypress v-if=\"maskShow\" @esc=\"onTap\" />\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef H5\r\n\timport keypress from './keypress.js'\r\n\t// #endif\r\n\r\n\t/**\r\n\t * PopUp 弹出层\r\n\t * @description 弹出层组件，为了解决遮罩弹层的问题\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=329\r\n\t * @property {String} type = [top|center|bottom|left|right|message|dialog|share] 弹出方式\r\n\t * \t@value top 顶部弹出\r\n\t * \t@value center 中间弹出\r\n\t * \t@value bottom 底部弹出\r\n\t * \t@value left\t\t左侧弹出\r\n\t * \t@value right  右侧弹出\r\n\t * \t@value message 消息提示\r\n\t * \t@value dialog 对话框\r\n\t * \t@value share 底部分享示例\r\n\t * @property {Boolean} animation = [true|false] 是否开启动画\r\n\t * @property {Boolean} maskClick = [true|false] 蒙版点击是否关闭弹窗(废弃)\r\n\t * @property {Boolean} isMaskClick = [true|false] 蒙版点击是否关闭弹窗\r\n\t * @property {String}  backgroundColor 主窗口背景色\r\n\t * @property {String}  maskBackgroundColor 蒙版颜色\r\n\t * @property {Boolean} safeArea\t\t   是否适配底部安全区\r\n\t * @event {Function} change 打开关闭弹窗触发，e={show: false}\r\n\t * @event {Function} maskClick 点击遮罩触发\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'uniPopup',\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef H5\r\n\t\t\tkeypress\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\temits: ['change', 'maskClick'],\r\n\t\tprops: {\r\n\t\t\t// 开启动画\r\n\t\t\tanimation: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 弹出层类型，可选值，top: 顶部弹出层；bottom：底部弹出层；center：全屏弹出层\r\n\t\t\t// message: 消息提示 ; dialog : 对话框\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'center'\r\n\t\t\t},\r\n\t\t\t// maskClick\r\n\t\t\tisMaskClick: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\t// TODO 2 个版本后废弃属性 ，使用 isMaskClick\r\n\t\t\tmaskClick: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: null\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'none'\r\n\t\t\t},\r\n\t\t\tsafeArea: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tmaskBackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'rgba(0, 0, 0, 0.4)'\r\n\t\t\t},\r\n\t\t},\r\n\r\n\t\twatch: {\r\n\t\t\t/**\r\n\t\t\t * 监听type类型\r\n\t\t\t */\r\n\t\t\ttype: {\r\n\t\t\t\thandler: function(type) {\r\n\t\t\t\t\tif (!this.config[type]) return\r\n\t\t\t\t\tthis[this.config[type]](true)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\tisDesktop: {\r\n\t\t\t\thandler: function(newVal) {\r\n\t\t\t\t\tif (!this.config[newVal]) return\r\n\t\t\t\t\tthis[this.config[this.type]](true)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 监听遮罩是否可点击\r\n\t\t\t * @param {Object} val\r\n\t\t\t */\r\n\t\t\tmaskClick: {\r\n\t\t\t\thandler: function(val) {\r\n\t\t\t\t\tthis.mkclick = val\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\tisMaskClick: {\r\n\t\t\t\thandler: function(val) {\r\n\t\t\t\t\tthis.mkclick = val\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t},\r\n\t\t\t// H5 下禁止底部滚动\r\n\t\t\tshowPopup(show) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\r\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow = show ? 'hidden' : 'visible'\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tduration: 300,\r\n\t\t\t\tani: [],\r\n\t\t\t\tshowPopup: false,\r\n\t\t\t\tshowTrans: false,\r\n\t\t\t\tpopupWidth: 0,\r\n\t\t\t\tpopupHeight: 0,\r\n\t\t\t\tconfig: {\r\n\t\t\t\t\ttop: 'top',\r\n\t\t\t\t\tbottom: 'bottom',\r\n\t\t\t\t\tcenter: 'center',\r\n\t\t\t\t\tleft: 'left',\r\n\t\t\t\t\tright: 'right',\r\n\t\t\t\t\tmessage: 'top',\r\n\t\t\t\t\tdialog: 'center',\r\n\t\t\t\t\tshare: 'bottom'\r\n\t\t\t\t},\r\n\t\t\t\tmaskClass: {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tbackgroundColor: 'rgba(0, 0, 0, 0.4)'\r\n\t\t\t\t},\r\n\t\t\t\ttransClass: {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0\r\n\t\t\t\t},\r\n\t\t\t\tmaskShow: true,\r\n\t\t\t\tmkclick: true,\r\n\t\t\t\tpopupstyle: this.isDesktop ? 'fixforpc-top' : 'top'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisDesktop() {\r\n\t\t\t\treturn this.popupWidth >= 500 && this.popupHeight >= 500\r\n\t\t\t},\r\n\t\t\tbg() {\r\n\t\t\t\tif (this.backgroundColor === '' || this.backgroundColor === 'none') {\r\n\t\t\t\t\treturn 'transparent'\r\n\t\t\t\t}\r\n\t\t\t\treturn this.backgroundColor\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tconst fixSize = () => {\r\n\t\t\t\tconst {\r\n\t\t\t\t\twindowWidth,\r\n\t\t\t\t\twindowHeight,\r\n\t\t\t\t\twindowTop,\r\n\t\t\t\t\tsafeArea,\r\n\t\t\t\t\tscreenHeight,\r\n\t\t\t\t\tsafeAreaInsets\r\n\t\t\t\t} = uni.getSystemInfoSync()\r\n\t\t\t\tthis.popupWidth = windowWidth\r\n\t\t\t\tthis.popupHeight = windowHeight + (windowTop || 0)\r\n\t\t\t\t// TODO fix by mehaotian 是否适配底部安全区 ,目前微信ios 、和 app ios 计算有差异，需要框架修复\r\n\t\t\t\tif (safeArea && this.safeArea) {\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tthis.safeAreaInsets = screenHeight - safeArea.bottom\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\tthis.safeAreaInsets = safeAreaInsets.bottom\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.safeAreaInsets = 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tfixSize()\r\n\t\t\t// #ifdef H5\r\n\t\t\t// window.addEventListener('resize', fixSize)\r\n\t\t\t// this.$once('hook:beforeDestroy', () => {\r\n\t\t\t// \twindow.removeEventListener('resize', fixSize)\r\n\t\t\t// })\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// #ifndef VUE3\r\n\t\t// TODO vue2\r\n\t\tdestroyed() {\r\n\t\t\tthis.setH5Visible()\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE3\r\n\t\t// TODO vue3\r\n\t\tunmounted() {\r\n\t\t\tthis.setH5Visible()\r\n\t\t},\r\n\t\t// #endif\r\n\t\tcreated() {\r\n\t\t\t// this.mkclick =  this.isMaskClick || this.maskClick\r\n\t\t\tif (this.isMaskClick === null && this.maskClick === null) {\r\n\t\t\t\tthis.mkclick = true\r\n\t\t\t} else {\r\n\t\t\t\tthis.mkclick = this.isMaskClick !== null ? this.isMaskClick : this.maskClick\r\n\t\t\t}\r\n\t\t\tif (this.animation) {\r\n\t\t\t\tthis.duration = 300\r\n\t\t\t} else {\r\n\t\t\t\tthis.duration = 0\r\n\t\t\t}\r\n\t\t\t// TODO 处理 message 组件生命周期异常的问题\r\n\t\t\tthis.messageChild = null\r\n\t\t\t// TODO 解决头条冒泡的问题\r\n\t\t\tthis.clearPropagation = false\r\n\t\t\tthis.maskClass.backgroundColor = this.maskBackgroundColor\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetH5Visible() {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\r\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow = 'visible'\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 公用方法，不显示遮罩层\r\n\t\t\t */\r\n\t\t\tcloseMask() {\r\n\t\t\t\tthis.maskShow = false\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 公用方法，遮罩层禁止点击\r\n\t\t\t */\r\n\t\t\tdisableMask() {\r\n\t\t\t\tthis.mkclick = false\r\n\t\t\t},\r\n\t\t\t// TODO nvue 取消冒泡\r\n\t\t\tclear(e) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\te.stopPropagation()\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.clearPropagation = true\r\n\t\t\t},\r\n\r\n\t\t\topen(direction) {\r\n\t\t\t\t// fix by mehaotian 处理快速打开关闭的情况\r\n\t\t\t\tif (this.showPopup) {\r\n\t\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\t\tthis.showPopup = false\r\n\t\t\t\t}\r\n\t\t\t\tlet innerType = ['top', 'center', 'bottom', 'left', 'right', 'message', 'dialog', 'share']\r\n\t\t\t\tif (!(direction && innerType.indexOf(direction) !== -1)) {\r\n\t\t\t\t\tdirection = this.type\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.config[direction]) {\r\n\t\t\t\t\tconsole.error('缺少类型：', direction)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis[this.config[direction]]()\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tshow: true,\r\n\t\t\t\t\ttype: direction\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose(type) {\r\n\t\t\t\tthis.showTrans = false\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tshow: false,\r\n\t\t\t\t\ttype: this.type\r\n\t\t\t\t})\r\n\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\t// // 自定义关闭事件\r\n\t\t\t\t// this.customOpen && this.customClose()\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.showPopup = false\r\n\t\t\t\t}, 300)\r\n\t\t\t},\r\n\t\t\t// TODO 处理冒泡事件，头条的冒泡事件有问题 ，先这样兼容\r\n\t\t\ttouchstart() {\r\n\t\t\t\tthis.clearPropagation = false\r\n\t\t\t},\r\n\r\n\t\t\tonTap() {\r\n\t\t\t\tif (this.clearPropagation) {\r\n\t\t\t\t\t// fix by mehaotian 兼容 nvue\r\n\t\t\t\t\tthis.clearPropagation = false\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('maskClick')\r\n\t\t\t\tif (!this.mkclick) return\r\n\t\t\t\tthis.close()\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 顶部弹出样式处理\r\n\t\t\t */\r\n\t\t\ttop(type) {\r\n\t\t\t\tthis.popupstyle = this.isDesktop ? 'fixforpc-top' : 'top'\r\n\t\t\t\tthis.ani = ['slide-top']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tif (this.messageChild && this.type === 'message') {\r\n\t\t\t\t\t\tthis.messageChild.timerClose()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 底部弹出样式处理\r\n\t\t\t */\r\n\t\t\tbottom(type) {\r\n\t\t\t\tthis.popupstyle = 'bottom'\r\n\t\t\t\tthis.ani = ['slide-bottom']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tpaddingBottom: this.safeAreaInsets + 'px',\r\n\t\t\t\t\tbackgroundColor: this.bg\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 中间弹出样式处理\r\n\t\t\t */\r\n\t\t\tcenter(type) {\r\n\t\t\t\tthis.popupstyle = 'center'\r\n\t\t\t\tthis.ani = ['zoom-out', 'fade']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column',\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tjustifyContent: 'center',\r\n\t\t\t\t\talignItems: 'center'\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t},\r\n\t\t\tleft(type) {\r\n\t\t\t\tthis.popupstyle = 'left'\r\n\t\t\t\tthis.ani = ['slide-left']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg,\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column'\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t},\r\n\t\t\tright(type) {\r\n\t\t\t\tthis.popupstyle = 'right'\r\n\t\t\t\tthis.ani = ['slide-right']\r\n\t\t\t\tthis.transClass = {\r\n\t\t\t\t\tposition: 'fixed',\r\n\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\tright: 0,\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tbackgroundColor: this.bg,\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\tflexDirection: 'column'\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t}\r\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\r\n\t\t\t\tif (type) return\r\n\t\t\t\tthis.showPopup = true\r\n\t\t\t\tthis.showTrans = true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.uni-popup {\r\n\t\tposition: fixed;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 99;\r\n\r\n\t\t/* #endif */\r\n\t\t&.top,\r\n\t\t&.left,\r\n\t\t&.right {\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\ttop: var(--window-top);\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifndef H5 */\r\n\t\t\ttop: 0;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\r\n\t\t.uni-popup__wrapper {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tdisplay: block;\r\n\t\t\t/* #endif */\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t/* iphonex 等安全区设置，底部安全区适配 */\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t// padding-bottom: constant(safe-area-inset-bottom);\r\n\t\t\t// padding-bottom: env(safe-area-inset-bottom);\r\n\t\t\t/* #endif */\r\n\t\t\t&.left,\r\n\t\t\t&.right {\r\n\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\tpadding-top: var(--window-top);\r\n\t\t\t\t/* #endif */\r\n\t\t\t\t/* #ifndef H5 */\r\n\t\t\t\tpadding-top: 0;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.fixforpc-z-index {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tz-index: 999;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.fixforpc-top {\r\n\t\ttop: 0;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-popup.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752119233667\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}