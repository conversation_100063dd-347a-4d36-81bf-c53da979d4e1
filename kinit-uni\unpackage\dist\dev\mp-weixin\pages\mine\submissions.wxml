<view class="submissions-page data-v-608f4c38"><view class="custom-navbar data-v-608f4c38"><view class="navbar-content data-v-608f4c38"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left data-v-608f4c38" bindtap="__e"><uni-icons vue-id="275363a2-1" type="left" size="32" color="#FF6B9D" class="data-v-608f4c38" bind:__l="__l"></uni-icons></view><text class="navbar-title data-v-608f4c38">我的投稿</text><view class="navbar-right data-v-608f4c38"><view data-event-opts="{{[['tap',[['goToCreate',['$event']]]]]}}" class="action-btn data-v-608f4c38" bindtap="__e"><uni-icons vue-id="275363a2-2" type="plus" size="24" color="#f78ca0" class="data-v-608f4c38" bind:__l="__l"></uni-icons></view></view></view></view><view class="filter-tabs data-v-608f4c38"><block wx:for="{{statusTabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchStatus',['$0'],[[['statusTabs','',index,'value']]]]]]]}}" class="{{['filter-tab','data-v-608f4c38',(currentStatus===tab.value)?'active':'']}}" bindtap="__e"><text class="tab-text data-v-608f4c38">{{tab.label}}</text><block wx:if="{{tab.count>0}}"><view class="tab-badge data-v-608f4c38">{{tab.count}}</view></block></view></block></view><scroll-view class="submissions-content data-v-608f4c38" scroll-y="true"><view class="submissions-list data-v-608f4c38"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['filteredSubmissions','',index]]]]]]]}}" class="submission-item data-v-608f4c38" bindtap="__e"><view class="item-cover data-v-608f4c38"><image class="cover-image data-v-608f4c38" src="{{item.$orig.cover_image||'/static/images/placeholder.jpg'}}" mode="aspectFill"></image><view class="{{item.m0}}">{{''+item.m1+''}}</view></view><view class="item-info data-v-608f4c38"><view class="info-header data-v-608f4c38"><text class="submission-title data-v-608f4c38">{{item.$orig.title||item.$orig.city+'-'+item.$orig.submission_code}}</text><text class="submission-time data-v-608f4c38">{{item.m2}}</text></view><view class="info-content data-v-608f4c38"><view class="info-row data-v-608f4c38"><view class="info-item data-v-608f4c38"><uni-icons vue-id="{{'275363a2-3-'+index}}" type="location" size="16" color="#f78ca0" class="data-v-608f4c38" bind:__l="__l"></uni-icons><text class="info-text data-v-608f4c38">{{item.m3}}</text></view><view class="info-item data-v-608f4c38"><uni-icons vue-id="{{'275363a2-4-'+index}}" type="calendar" size="16" color="#a6c1ee" class="data-v-608f4c38" bind:__l="__l"></uni-icons><text class="info-text data-v-608f4c38">{{item.$orig.age+"岁"}}</text></view></view><view class="info-row data-v-608f4c38"><view class="info-item data-v-608f4c38"><uni-icons vue-id="{{'275363a2-5-'+index}}" type="person" size="16" color="#feb47b" class="data-v-608f4c38" bind:__l="__l"></uni-icons><text class="info-text data-v-608f4c38">{{item.$orig.occupation}}</text></view><view class="info-item data-v-608f4c38"><uni-icons vue-id="{{'275363a2-6-'+index}}" type="eye" size="16" color="#c8a8e9" class="data-v-608f4c38" bind:__l="__l"></uni-icons><text class="info-text data-v-608f4c38">{{(item.$orig.views||0)+"次浏览"}}</text></view></view></view><view class="item-actions data-v-608f4c38"><view data-event-opts="{{[['tap',[['editSubmission',['$0'],[[['filteredSubmissions','',index]]]]]]]}}" class="action-btn edit data-v-608f4c38" catchtap="__e"><uni-icons vue-id="{{'275363a2-7-'+index}}" type="compose" size="16" color="#f78ca0" class="data-v-608f4c38" bind:__l="__l"></uni-icons><text class="action-text data-v-608f4c38">编辑</text></view><view data-event-opts="{{[['tap',[['deleteSubmission',['$0'],[[['filteredSubmissions','',index]]]]]]]}}" class="action-btn delete data-v-608f4c38" catchtap="__e"><uni-icons vue-id="{{'275363a2-8-'+index}}" type="trash" size="16" color="#ef4444" class="data-v-608f4c38" bind:__l="__l"></uni-icons><text class="action-text data-v-608f4c38">删除</text></view></view></view></view></block></view><block wx:if="{{$root.g0===0}}"><view class="empty-state data-v-608f4c38"><view class="empty-icon data-v-608f4c38"><uni-icons vue-id="275363a2-9" type="compose" size="80" color="#d1d5db" class="data-v-608f4c38" bind:__l="__l"></uni-icons></view><text class="empty-text data-v-608f4c38">{{$root.m4}}</text><view data-event-opts="{{[['tap',[['goToCreate',['$event']]]]]}}" class="empty-action data-v-608f4c38" bindtap="__e"><text class="action-text data-v-608f4c38">发布第一个投稿</text></view></view></block></scroll-view></view>