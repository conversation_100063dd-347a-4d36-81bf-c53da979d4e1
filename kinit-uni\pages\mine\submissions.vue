<template>
  <view class="submissions-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="left" size="32" color="#FF6B9D"></uni-icons>
        </view>
        <text class="navbar-title">我的投稿</text>
        <view class="navbar-right">
          <view class="action-btn" @click="goToCreate">
            <uni-icons type="plus" size="24" color="#f78ca0"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 状态筛选 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in statusTabs" 
        :key="index"
        class="filter-tab"
        :class="{ active: currentStatus === tab.value }"
        @click="switchStatus(tab.value)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
      </view>
    </view>

    <scroll-view scroll-y="true" class="submissions-content">
      <!-- 投稿列表 -->
      <view class="submissions-list">
        <view 
          v-for="(item, index) in filteredSubmissions" 
          :key="index"
          class="submission-item"
          @click="goToDetail(item)"
        >
          <!-- 封面图片 -->
          <view class="item-cover">
            <image 
              :src="item.cover_image || '/static/images/placeholder.jpg'" 
              class="cover-image" 
              mode="aspectFill"
            />
            <view class="status-badge" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </view>
          </view>

          <!-- 投稿信息 -->
          <view class="item-info">
            <view class="info-header">
              <text class="submission-title">{{ item.title || `${item.city}-${item.submission_code}` }}</text>
              <text class="submission-time">{{ formatTime(item.created_at) }}</text>
            </view>

            <view class="info-content">
              <view class="info-row">
                <view class="info-item">
                  <uni-icons type="location" size="16" color="#f78ca0"></uni-icons>
                  <text class="info-text">{{ formatLocation(item) }}</text>
                </view>
                <view class="info-item">
                  <uni-icons type="calendar" size="16" color="#a6c1ee"></uni-icons>
                  <text class="info-text">{{ item.age }}岁</text>
                </view>
              </view>

              <view class="info-row">
                <view class="info-item">
                  <uni-icons type="person" size="16" color="#feb47b"></uni-icons>
                  <text class="info-text">{{ item.occupation }}</text>
                </view>
                <view class="info-item">
                  <uni-icons type="eye" size="16" color="#c8a8e9"></uni-icons>
                  <text class="info-text">{{ item.views || 0 }}次浏览</text>
                </view>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="item-actions">
              <view class="action-btn edit" @click.stop="editSubmission(item)">
                <uni-icons type="compose" size="16" color="#f78ca0"></uni-icons>
                <text class="action-text">编辑</text>
              </view>
              <view class="action-btn delete" @click.stop="deleteSubmission(item)">
                <uni-icons type="trash" size="16" color="#ef4444"></uni-icons>
                <text class="action-text">删除</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="filteredSubmissions.length === 0" class="empty-state">
        <view class="empty-icon">
          <uni-icons type="compose" size="80" color="#d1d5db"></uni-icons>
        </view>
        <text class="empty-text">{{ getEmptyText() }}</text>
        <view class="empty-action" @click="goToCreate">
          <text class="action-text">发布第一个投稿</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentStatus: 'all',
      statusTabs: [
        { label: '全部', value: 'all', count: 0 },
        { label: '审核中', value: 'pending', count: 0 },
        { label: '已通过', value: 'approved', count: 0 },
        { label: '已拒绝', value: 'rejected', count: 0 }
      ],
      submissionsList: [
        // 模拟数据
        {
          id: 1,
          title: '寻找游戏搭子',
          submission_code: 'zhiyu001',
          city: '杭州',
          province: '浙江',
          age: 22,
          gender: '女',
          occupation: '学生',
          status: 'approved',
          cover_image: '/static/images/demo1.jpg',
          views: 156,
          created_at: '2024-01-15 14:30:00'
        },
        {
          id: 2,
          title: '一起看电影',
          submission_code: 'zhiyu002',
          city: '上海',
          province: '上海',
          age: 25,
          gender: '女',
          occupation: '设计师',
          status: 'pending',
          cover_image: '/static/images/demo2.jpg',
          views: 0,
          created_at: '2024-01-16 09:15:00'
        }
      ]
    }
  },
  computed: {
    filteredSubmissions() {
      if (this.currentStatus === 'all') {
        return this.submissionsList
      }
      return this.submissionsList.filter(item => item.status === this.currentStatus)
    }
  },
  onLoad() {
    this.checkLoginAndLoad()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },

    checkLoginAndLoad() {
      if (!this.$store.getters['user/isLoggedIn']) {
        uni.showModal({
          title: '登录提示',
          content: '需要登录后才能查看我的投稿',
          confirmText: '立即登录',
          cancelText: '返回',
          success: (res) => {
            if (res.confirm) {
              this.performWxLogin()
            } else {
              uni.navigateBack()
            }
          }
        })
        return
      }
      this.loadSubmissions()
    },

    async performWxLogin() {
      try {
        // 检查是否在开发环境
        // #ifdef H5
        // 开发环境使用模拟登录
        await this.mockLogin()
        return
        // #endif

        const loginRes = await uni.login({ provider: 'weixin' })

        if (!loginRes.code) {
          throw new Error('获取微信登录code失败')
        }

        const userInfoRes = await uni.getUserProfile({
          desc: '用于完善用户资料'
        })

        if (!userInfoRes.userInfo) {
          throw new Error('获取用户信息失败')
        }

        await this.$store.dispatch('user/wxLogin', {
          code: loginRes.code,
          userInfo: userInfoRes.userInfo
        })

        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })

        this.loadSubmissions()

      } catch (error) {
        console.error('微信登录失败:', error)

        // 如果微信登录失败，尝试模拟登录
        try {
          await this.mockLogin()
        } catch (mockError) {
          console.error('模拟登录也失败:', mockError)
          uni.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          })
          uni.navigateBack()
        }
      }
    },

    // 模拟登录（开发环境使用）
    async mockLogin() {
      try {
        // 模拟用户信息
        const mockUserInfo = {
          id: 1,
          nickname: '测试用户',
          avatar: '/static/images/default-avatar.png',
          gender: 1,
          desc: '这是一个测试用户'
        }

        // 模拟token
        const mockToken = 'mock_token_' + Date.now()

        // 直接设置到store
        this.$store.commit('user/SET_TOKEN', mockToken)
        this.$store.commit('user/SET_USER_INFO', mockUserInfo)

        uni.showToast({
          title: '模拟登录成功',
          icon: 'success'
        })

        this.loadSubmissions()

      } catch (error) {
        throw new Error('模拟登录失败: ' + error.message)
      }
    },

    loadSubmissions() {
      // 更新状态标签的数量
      this.statusTabs[0].count = this.submissionsList.length
      this.statusTabs[1].count = this.submissionsList.filter(item => item.status === 'pending').length
      this.statusTabs[2].count = this.submissionsList.filter(item => item.status === 'approved').length
      this.statusTabs[3].count = this.submissionsList.filter(item => item.status === 'rejected').length
    },

    switchStatus(status) {
      this.currentStatus = status
    },

    getStatusClass(status) {
      const statusMap = {
        'pending': 'pending',
        'approved': 'approved',
        'rejected': 'rejected'
      }
      return statusMap[status] || 'pending'
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '审核中',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return statusMap[status] || '未知'
    },

    getEmptyText() {
      const textMap = {
        'all': '还没有投稿，快去发布第一个吧～',
        'pending': '没有审核中的投稿',
        'approved': '没有已通过的投稿',
        'rejected': '没有被拒绝的投稿'
      }
      return textMap[this.currentStatus] || '暂无数据'
    },

    formatLocation(item) {
      const municipalities = ['北京', '上海', '天津', '重庆']
      if (item.province && item.city) {
        if (municipalities.includes(item.province)) {
          return item.city
        }
        return `${item.province}-${item.city}`
      }
      return item.city || item.province || '未知'
    },

    formatTime(timeStr) {
      const date = new Date(timeStr)
      const now = new Date()
      const diff = now - date

      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else if (days < 7) {
        return `${days}天前`
      } else {
        return date.toLocaleDateString()
      }
    },

    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/submission/detail?id=${item.id}`
      })
    },

    goToCreate() {
      uni.navigateTo({
        url: '/pages/submission/create'
      })
    },

    editSubmission(item) {
      uni.navigateTo({
        url: `/pages/submission/edit?id=${item.id}`
      })
    },

    deleteSubmission(item) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个投稿吗？删除后无法恢复。',
        success: (res) => {
          if (res.confirm) {
            // 执行删除操作
            const index = this.submissionsList.findIndex(sub => sub.id === item.id)
            if (index > -1) {
              this.submissionsList.splice(index, 1)
              this.loadSubmissions()
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.submissions-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;

    .navbar-left, .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: center;
    }

    .navbar-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }

    .action-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(0.95);
      }
    }
  }
}

.filter-tabs {
  display: flex;
  padding: 16rpx 20rpx;
  gap: 8rpx;

  .filter-tab {
    position: relative;
    flex: 1;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 30rpx;
    transition: all 0.3s ease;

    &.active {
      background: #f78ca0;

      .tab-text {
        color: white;
        font-weight: 600;
      }

      .tab-badge {
        background: rgba(255, 255, 255, 0.3);
        color: white;
      }
    }

    .tab-text {
      font-size: 24rpx;
      color: $soul-gray-700;
      transition: all 0.3s ease;
    }

    .tab-badge {
      position: absolute;
      top: -8rpx;
      right: -8rpx;
      min-width: 32rpx;
      height: 32rpx;
      background: #f78ca0;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18rpx;
      color: white;
      font-weight: 600;
      padding: 0 8rpx;
    }
  }
}

.submissions-content {
  height: calc(100vh - 200rpx);
  padding: 0 20rpx 40rpx;
}

.submissions-list {
  .submission-item {
    @include glass-effect(0.6);
    border-radius: 20rpx;
    margin-bottom: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    .item-cover {
      position: relative;
      height: 200rpx;

      .cover-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .status-badge {
        position: absolute;
        top: 12rpx;
        right: 12rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 20rpx;
        font-weight: 600;
        color: white;

        &.pending {
          background: #f59e0b;
        }

        &.approved {
          background: #10b981;
        }

        &.rejected {
          background: #ef4444;
        }
      }
    }

    .item-info {
      padding: 20rpx;

      .info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .submission-title {
          font-size: 26rpx;
          font-weight: 600;
          color: $soul-gray-800;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .submission-time {
          font-size: 20rpx;
          color: $soul-gray-500;
          margin-left: 16rpx;
        }
      }

      .info-content {
        margin-bottom: 16rpx;

        .info-row {
          display: flex;
          gap: 24rpx;
          margin-bottom: 8rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .info-item {
            display: flex;
            align-items: center;
            gap: 6rpx;
            flex: 1;

            .info-text {
              font-size: 22rpx;
              color: $soul-gray-600;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }

      .item-actions {
        display: flex;
        gap: 12rpx;
        padding-top: 16rpx;
        border-top: 1rpx solid rgba(0,0,0,0.05);

        .action-btn {
          flex: 1;
          height: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;
          border-radius: 12rpx;
          transition: all 0.3s ease;

          &.edit {
            background: rgba(247, 140, 160, 0.1);
            border: 1rpx solid rgba(247, 140, 160, 0.3);

            .action-text {
              color: #f78ca0;
            }
          }

          &.delete {
            background: rgba(239, 68, 68, 0.1);
            border: 1rpx solid rgba(239, 68, 68, 0.3);

            .action-text {
              color: #ef4444;
            }
          }

          .action-text {
            font-size: 22rpx;
            font-weight: 500;
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-icon {
    margin-bottom: 24rpx;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 26rpx;
    color: $soul-gray-500;
    margin-bottom: 32rpx;
    line-height: 1.4;
  }

  .empty-action {
    padding: 16rpx 32rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);

    .action-text {
      font-size: 24rpx;
      color: white;
      font-weight: 600;
    }
  }
}
</style>
