@charset "UTF-8";
/* uView的全局SCSS主题文件 */
/**
 * Soul风格主题变量
 */
/* Soul风格颜色变量 */
/* 主色调 - 温柔的粉色系 */
/* 辅助色 */
/* 中性色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更圆润的设计 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* Soul风格特有样式 */
/* 毛玻璃效果 */
/* 渐变背景 */
/* 阴影效果 */
/* 卡片样式 */
/* 按钮样式 */
.detail-page.data-v-a398096a {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}
.custom-navbar.data-v-a398096a {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  padding-top: 25px;
}
.custom-navbar .navbar-content.data-v-a398096a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
}
.custom-navbar .navbar-content .navbar-left.data-v-a398096a, .custom-navbar .navbar-content .navbar-right.data-v-a398096a {
  width: 60rpx;
  display: flex;
  justify-content: center;
}
.custom-navbar .navbar-content .navbar-title.data-v-a398096a {
  font-size: 28rpx;
  font-weight: 600;
  color: #424242;
}
.custom-navbar .navbar-content .favorite-btn.data-v-a398096a {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}
.custom-navbar .navbar-content .favorite-btn.data-v-a398096a:active {
  background: rgba(255, 255, 255, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.detail-content.data-v-a398096a {
  min-height: calc(100vh - 200rpx);
  padding-bottom: 40rpx;
}
.image-section.data-v-a398096a {
  position: relative;
  height: 400rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  border-radius: 0 0 24rpx 24rpx;
}
.image-section .blur-background.data-v-a398096a {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}
.image-section .blur-background .blur-image.data-v-a398096a {
  width: 100%;
  height: 100%;
  -webkit-filter: blur(20rpx);
          filter: blur(20rpx);
  opacity: 0.3;
}
.image-section .image-swiper.data-v-a398096a {
  position: relative;
  height: 100%;
  z-index: 2;
}
.image-section .image-swiper .detail-image.data-v-a398096a {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.image-section .pinned-badge.data-v-a398096a {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #ff7e5f, #feb47b);
  border-radius: 32rpx;
  padding: 6rpx 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  z-index: 10;
}
.image-section .pinned-badge .pinned-text.data-v-a398096a {
  font-size: 18rpx;
  color: #FFFFFF;
  font-weight: 700;
  margin-left: 4rpx;
}
.info-card.data-v-a398096a, .content-card.data-v-a398096a, .time-card.data-v-a398096a {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  margin: 0 20rpx 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.info-card .card-header.data-v-a398096a, .content-card .card-header.data-v-a398096a {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 8rpx;
}
.info-card .card-header .card-title.data-v-a398096a, .content-card .card-header .card-title.data-v-a398096a {
  font-size: 24rpx;
  font-weight: 700;
  color: #616161;
}
.info-card .info-grid.data-v-a398096a, .content-card .info-grid.data-v-a398096a {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
}
.info-card .info-grid .info-item.data-v-a398096a, .content-card .info-grid .info-item.data-v-a398096a {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  padding: 12rpx 16rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}
.info-card .info-grid .info-item .info-value.data-v-a398096a, .content-card .info-grid .info-item .info-value.data-v-a398096a {
  font-size: 22rpx;
  font-weight: 600;
  color: #616161;
}
.info-card .info-grid .info-item .info-value.accept-distance.data-v-a398096a, .content-card .info-grid .info-item .info-value.accept-distance.data-v-a398096a {
  color: #10b981;
}
.info-card .content-text .description-text.data-v-a398096a, .content-card .content-text .description-text.data-v-a398096a {
  font-size: 24rpx;
  color: #616161;
  line-height: 1.5;
}
.time-card .time-info.data-v-a398096a {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}
.time-card .time-info .time-text.data-v-a398096a {
  font-size: 20rpx;
  color: #9E9E9E;
}
.related-submissions.data-v-a398096a {
  margin: 0 20rpx 16rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.related-submissions .section-header.data-v-a398096a {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
}
.related-submissions .section-header .section-title.data-v-a398096a {
  font-size: 24rpx;
  font-weight: 700;
  color: #616161;
}
.related-submissions .submissions-scroll.data-v-a398096a {
  width: 100%;
  white-space: nowrap;
}
.related-submissions .submissions-container.data-v-a398096a {
  display: flex;
  gap: 16rpx;
  padding: 0 4rpx;
}
.related-submissions .mini-submission-card.data-v-a398096a {
  position: relative;
  width: 200rpx;
  height: 240rpx;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 24rpx;
  overflow: hidden;
  flex-shrink: 0;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
}
.related-submissions .mini-submission-card.data-v-a398096a:hover {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
}
.related-submissions .mini-submission-card .mini-top-badge.data-v-a398096a {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  z-index: 10;
  background: linear-gradient(135deg, #feb47b 0%, #FFE066 100%);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
}
.related-submissions .mini-submission-card .mini-top-badge .mini-top-text.data-v-a398096a {
  font-size: 16rpx;
  color: #FFFFFF;
  font-weight: 600;
}
.related-submissions .mini-submission-card .mini-cover-container.data-v-a398096a {
  position: relative;
  width: 100%;
  height: 160rpx;
  overflow: hidden;
  background-color: #f0f0f0;
  flex-shrink: 0;
}
.related-submissions .mini-submission-card .mini-cover-container .mini-cover.data-v-a398096a {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.related-submissions .mini-submission-card .mini-info-container.data-v-a398096a {
  height: 80rpx;
  padding: 6rpx 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-shrink: 0;
}
.related-submissions .mini-submission-card .mini-info-row.data-v-a398096a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4rpx;
}
.related-submissions .mini-submission-card .mini-info-row .mini-info-item.data-v-a398096a {
  display: flex;
  align-items: center;
  gap: 2rpx;
  flex: 1;
  justify-content: flex-start;
  min-width: 0;
  overflow: hidden;
}
.related-submissions .mini-submission-card .mini-info-row .mini-info-item .mini-info-text.data-v-a398096a {
  font-size: 15rpx;
  color: #616161;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}
.contact-section.data-v-a398096a {
  margin: 0 20rpx 16rpx;
}
.contact-section .contact-btn.data-v-a398096a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 32rpx;
  padding: 24rpx 40rpx;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  overflow: hidden;
}
.contact-section .contact-btn.data-v-a398096a::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}
.contact-section .contact-btn.data-v-a398096a:active {
  -webkit-transform: translateY(2rpx) scale(0.98);
          transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}
.contact-section .contact-btn.data-v-a398096a:active::before {
  left: 100%;
}
.contact-section .contact-btn .btn-icon.data-v-a398096a {
  -webkit-animation: heartBeat-data-v-a398096a 2s ease-in-out infinite;
          animation: heartBeat-data-v-a398096a 2s ease-in-out infinite;
}
.contact-section .contact-btn .btn-text.data-v-a398096a {
  font-size: 30rpx;
  color: #FFFFFF;
  font-weight: 700;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.contact-section .contact-btn .btn-sparkle.data-v-a398096a {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  font-size: 24rpx;
  -webkit-animation: pulse-data-v-a398096a 2s ease-in-out infinite;
          animation: pulse-data-v-a398096a 2s ease-in-out infinite;
}
.contact-modal.data-v-a398096a {
  position: relative;
  width: 680rpx;
  max-height: 80vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.3);
  -webkit-animation: modalSlideIn-data-v-a398096a 0.3s ease-out;
          animation: modalSlideIn-data-v-a398096a 0.3s ease-out;
  margin: 10vh 0;
}
.contact-modal .modal-bg-decoration.data-v-a398096a {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}
.contact-modal .modal-bg-decoration .bg-circle.data-v-a398096a {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  -webkit-animation: float-data-v-a398096a 6s ease-in-out infinite;
          animation: float-data-v-a398096a 6s ease-in-out infinite;
}
.contact-modal .modal-bg-decoration .bg-circle.bg-circle-1.data-v-a398096a {
  width: 120rpx;
  height: 120rpx;
  top: -60rpx;
  right: -60rpx;
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.contact-modal .modal-bg-decoration .bg-circle.bg-circle-2.data-v-a398096a {
  width: 80rpx;
  height: 80rpx;
  bottom: 100rpx;
  left: -40rpx;
  -webkit-animation-delay: 2s;
          animation-delay: 2s;
}
.contact-modal .modal-bg-decoration .bg-circle.bg-circle-3.data-v-a398096a {
  width: 60rpx;
  height: 60rpx;
  top: 200rpx;
  right: 50rpx;
  -webkit-animation-delay: 4s;
          animation-delay: 4s;
}
.contact-modal .modal-close.data-v-a398096a {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
}
.contact-modal .modal-close.data-v-a398096a:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
.contact-modal .modal-header.data-v-a398096a {
  text-align: center;
  padding: 48rpx 40rpx 32rpx;
  position: relative;
}
.contact-modal .modal-header .header-icon.data-v-a398096a {
  margin-bottom: 16rpx;
  -webkit-animation: heartBeat-data-v-a398096a 2s ease-in-out infinite;
          animation: heartBeat-data-v-a398096a 2s ease-in-out infinite;
}
.contact-modal .modal-header .modal-title.data-v-a398096a {
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 8rpx;
  display: block;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.contact-modal .modal-header .modal-subtitle.data-v-a398096a {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}
.contact-modal .modal-content.data-v-a398096a {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  position: relative;
  overflow-y: auto;
  max-height: calc(80vh - 200rpx);
}
.contact-modal .modal-content .steps-guide.data-v-a398096a {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border-radius: 20rpx;
}
.contact-modal .modal-content .steps-guide .step-item.data-v-a398096a {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.contact-modal .modal-content .steps-guide .step-item .step-number.data-v-a398096a {
  width: 48rpx;
  height: 48rpx;
  background: #FF6B9D;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
}
.contact-modal .modal-content .steps-guide .step-item .step-text.data-v-a398096a {
  font-size: 22rpx;
  color: #8B4513;
  text-align: center;
  font-weight: 500;
}
.contact-modal .modal-content .steps-guide .step-arrow.data-v-a398096a {
  margin: 0 16rpx;
  margin-top: -24rpx;
}
.contact-modal .modal-content .section-header.data-v-a398096a {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.contact-modal .modal-content .section-header .section-title.data-v-a398096a {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-left: 12rpx;
}
.contact-modal .modal-content .contact-id-section.data-v-a398096a {
  margin-bottom: 32rpx;
}
.contact-modal .modal-content .contact-id-section .contact-id-card.data-v-a398096a {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
}
.contact-modal .modal-content .contact-id-section .contact-id-card .id-display.data-v-a398096a {
  flex: 1;
}
.contact-modal .modal-content .contact-id-section .contact-id-card .id-display .contact-id.data-v-a398096a {
  font-size: 32rpx;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 2rpx;
}
.contact-modal .modal-content .contact-id-section .contact-id-card .copy-btn.data-v-a398096a {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
}
.contact-modal .modal-content .contact-id-section .contact-id-card .copy-btn.data-v-a398096a:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
.contact-modal .modal-content .contact-id-section .contact-id-card .copy-btn .copy-text.data-v-a398096a {
  font-size: 24rpx;
  color: #ffffff;
  margin-left: 8rpx;
  font-weight: 500;
}
.contact-modal .modal-content .service-qr-section.data-v-a398096a {
  margin-bottom: 24rpx;
}
.contact-modal .modal-content .service-qr-section .qr-container.data-v-a398096a {
  text-align: center;
}
.contact-modal .modal-content .service-qr-section .qr-container .qr-frame.data-v-a398096a {
  position: relative;
  display: inline-block;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(168, 237, 234, 0.3);
}
.contact-modal .modal-content .service-qr-section .qr-container .qr-frame .service-qr.data-v-a398096a {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  display: block;
}
.contact-modal .modal-content .service-qr-section .qr-container .qr-frame .qr-corners.data-v-a398096a {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.contact-modal .modal-content .service-qr-section .qr-container .qr-frame .qr-corners .corner.data-v-a398096a {
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #FF6B9D;
}
.contact-modal .modal-content .service-qr-section .qr-container .qr-frame .qr-corners .corner.corner-tl.data-v-a398096a {
  top: 12rpx;
  left: 12rpx;
  border-right: none;
  border-bottom: none;
  border-radius: 8rpx 0 0 0;
}
.contact-modal .modal-content .service-qr-section .qr-container .qr-frame .qr-corners .corner.corner-tr.data-v-a398096a {
  top: 12rpx;
  right: 12rpx;
  border-left: none;
  border-bottom: none;
  border-radius: 0 8rpx 0 0;
}
.contact-modal .modal-content .service-qr-section .qr-container .qr-frame .qr-corners .corner.corner-bl.data-v-a398096a {
  bottom: 12rpx;
  left: 12rpx;
  border-right: none;
  border-top: none;
  border-radius: 0 0 0 8rpx;
}
.contact-modal .modal-content .service-qr-section .qr-container .qr-frame .qr-corners .corner.corner-br.data-v-a398096a {
  bottom: 12rpx;
  right: 12rpx;
  border-left: none;
  border-top: none;
  border-radius: 0 0 8rpx 0;
}
.contact-modal .modal-content .service-qr-section .qr-container .service-tip.data-v-a398096a {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
.contact-modal .modal-content .warm-tips.data-v-a398096a {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border-radius: 16rpx;
  padding: 20rpx;
}
.contact-modal .modal-content .warm-tips .tips-header.data-v-a398096a {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.contact-modal .modal-content .warm-tips .tips-header .tips-title.data-v-a398096a {
  font-size: 26rpx;
  font-weight: 600;
  color: #8B4513;
  margin-left: 8rpx;
}
.contact-modal .modal-content .warm-tips .tips-content.data-v-a398096a {
  font-size: 24rpx;
  color: #8B4513;
  line-height: 1.5;
}
.loading-overlay.data-v-a398096a {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
/* 动画效果 */
@-webkit-keyframes modalSlideIn-data-v-a398096a {
0% {
    opacity: 0;
    -webkit-transform: scale(0.8) translateY(100rpx);
            transform: scale(0.8) translateY(100rpx);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1) translateY(0);
            transform: scale(1) translateY(0);
}
}
@keyframes modalSlideIn-data-v-a398096a {
0% {
    opacity: 0;
    -webkit-transform: scale(0.8) translateY(100rpx);
            transform: scale(0.8) translateY(100rpx);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1) translateY(0);
            transform: scale(1) translateY(0);
}
}
@-webkit-keyframes float-data-v-a398096a {
0%, 100% {
    -webkit-transform: translateY(0) rotate(0deg);
            transform: translateY(0) rotate(0deg);
}
50% {
    -webkit-transform: translateY(-20rpx) rotate(180deg);
            transform: translateY(-20rpx) rotate(180deg);
}
}
@keyframes float-data-v-a398096a {
0%, 100% {
    -webkit-transform: translateY(0) rotate(0deg);
            transform: translateY(0) rotate(0deg);
}
50% {
    -webkit-transform: translateY(-20rpx) rotate(180deg);
            transform: translateY(-20rpx) rotate(180deg);
}
}
@-webkit-keyframes heartBeat-data-v-a398096a {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
}
}
@keyframes heartBeat-data-v-a398096a {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
}
}
@-webkit-keyframes pulse-data-v-a398096a {
0%, 100% {
    opacity: 1;
}
50% {
    opacity: 0.7;
}
}
@keyframes pulse-data-v-a398096a {
0%, 100% {
    opacity: 1;
}
50% {
    opacity: 0.7;
}
}
