@charset "UTF-8";
/* uView的全局SCSS主题文件 */
/**
 * Soul风格主题变量
 */
/* Soul风格颜色变量 */
/* 主色调 - 温柔的粉色系 */
/* 辅助色 */
/* 中性色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更圆润的设计 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* Soul风格特有样式 */
/* 毛玻璃效果 */
/* 渐变背景 */
/* 阴影效果 */
/* 卡片样式 */
/* 按钮样式 */
.create-page.data-v-3d953676 {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}
.custom-navbar.data-v-3d953676 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  padding-top: 25px;
}
.custom-navbar .navbar-content.data-v-3d953676 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
}
.custom-navbar .navbar-content .navbar-left.data-v-3d953676, .custom-navbar .navbar-content .navbar-right.data-v-3d953676 {
  width: 60rpx;
  display: flex;
  justify-content: center;
}
.custom-navbar .navbar-content .navbar-title.data-v-3d953676 {
  font-size: 28rpx;
  font-weight: 600;
  color: #424242;
}
.form-content.data-v-3d953676 {
  height: calc(100vh - 120rpx);
}
.form-container.data-v-3d953676 {
  padding: 20rpx;
  padding-bottom: 40rpx;
}
.form-card.data-v-3d953676 {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.form-card .card-header.data-v-3d953676 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  gap: 8rpx;
}
.form-card .card-header .card-title.data-v-3d953676 {
  font-size: 24rpx;
  font-weight: 700;
  color: #616161;
}
.form-card .card-header .card-subtitle.data-v-3d953676 {
  font-size: 20rpx;
  color: #9E9E9E;
  margin-left: auto;
}
.form-item.data-v-3d953676 {
  margin-bottom: 24rpx;
}
.form-item.data-v-3d953676:last-child {
  margin-bottom: 0;
}
.form-item .form-label.data-v-3d953676 {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #616161;
  margin-bottom: 12rpx;
}
.form-item .form-input.data-v-3d953676 {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  color: #424242;
  box-sizing: border-box;
}
.form-item .form-input.data-v-3d953676:focus {
  border-color: #f78ca0;
  box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.1);
}
.form-item .form-textarea.data-v-3d953676 {
  width: 100%;
  min-height: 160rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  color: #424242;
  box-sizing: border-box;
  resize: none;
}
.form-item .form-textarea.data-v-3d953676:focus {
  border-color: #f78ca0;
  box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.1);
}
.form-item .char-count.data-v-3d953676 {
  text-align: right;
  font-size: 20rpx;
  color: #9E9E9E;
  margin-top: 8rpx;
}
.city-selector.data-v-3d953676 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);
}
.city-selector .city-text.data-v-3d953676 {
  font-size: 26rpx;
  color: #424242;
}
.city-selector .city-text.placeholder.data-v-3d953676 {
  color: #999;
}
.gender-options.data-v-3d953676 {
  display: flex;
  gap: 16rpx;
}
.gender-options .gender-option.data-v-3d953676 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}
.gender-options .gender-option.active.data-v-3d953676 {
  border-color: #f78ca0;
  background: rgba(247, 140, 160, 0.1);
}
.gender-options .gender-option .gender-text.data-v-3d953676 {
  font-size: 24rpx;
  color: #616161;
  font-weight: 500;
}
.distance-options.data-v-3d953676 {
  display: flex;
  gap: 16rpx;
}
.distance-options .distance-option.data-v-3d953676 {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}
.distance-options .distance-option.active.data-v-3d953676 {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}
.distance-options .distance-option .distance-text.data-v-3d953676 {
  font-size: 24rpx;
  color: #616161;
  font-weight: 500;
}
.image-upload-container .image-grid.data-v-3d953676 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.image-upload-container .image-item.data-v-3d953676 {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.image-upload-container .image-item .upload-image.data-v-3d953676 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.image-upload-container .image-item .image-actions.data-v-3d953676 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 8rpx;
}
.image-upload-container .image-item .image-actions .main-badge.data-v-3d953676 {
  background: #f78ca0;
  color: white;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
.image-upload-container .image-item .image-actions .delete-btn.data-v-3d953676 {
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.image-upload-container .upload-btn.data-v-3d953676 {
  width: 100%;
  height: 200rpx;
  border: 2rpx dashed #d1d5db;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.5);
}
.image-upload-container .upload-btn .upload-text.data-v-3d953676 {
  font-size: 22rpx;
  color: #999;
}
.submit-section.data-v-3d953676 {
  margin-top: 40rpx;
  text-align: center;
}
.submit-section .submit-btn.data-v-3d953676 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 44rpx;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}
.submit-section .submit-btn.data-v-3d953676:active:not(.disabled) {
  -webkit-transform: translateY(2rpx) scale(0.98);
          transform: translateY(2rpx) scale(0.98);
}
.submit-section .submit-btn.disabled.data-v-3d953676 {
  opacity: 0.6;
}
.submit-section .submit-btn .loading-icon.data-v-3d953676 {
  -webkit-animation: spin-data-v-3d953676 1s linear infinite;
          animation: spin-data-v-3d953676 1s linear infinite;
}
.submit-section .submit-btn .submit-text.data-v-3d953676 {
  font-size: 30rpx;
  color: white;
  font-weight: 700;
}
.submit-section .tips-text.data-v-3d953676 {
  margin-top: 16rpx;
  font-size: 22rpx;
  color: #9E9E9E;
  line-height: 1.4;
}
.city-picker.data-v-3d953676 {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}
.city-picker .picker-header.data-v-3d953676 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.city-picker .picker-header .picker-title.data-v-3d953676 {
  font-size: 28rpx;
  font-weight: 600;
  color: #424242;
}
.city-picker .picker-view.data-v-3d953676 {
  height: 400rpx;
}
.city-picker .picker-item.data-v-3d953676 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 26rpx;
  color: #616161;
}
.city-picker .picker-actions.data-v-3d953676 {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}
.city-picker .picker-actions .picker-btn.data-v-3d953676 {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
}
.city-picker .picker-actions .picker-btn.cancel.data-v-3d953676 {
  color: #757575;
  border-right: 1rpx solid #f0f0f0;
}
.city-picker .picker-actions .picker-btn.confirm.data-v-3d953676 {
  color: #f78ca0;
}
@-webkit-keyframes spin-data-v-3d953676 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-3d953676 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
