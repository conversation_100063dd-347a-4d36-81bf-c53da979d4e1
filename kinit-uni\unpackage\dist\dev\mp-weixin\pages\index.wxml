<view class="page-container data-v-2a183b29"><view class="custom-navbar data-v-2a183b29"><view class="navbar-content data-v-2a183b29"><view class="navbar-left data-v-2a183b29"><uni-icons vue-id="18efaafd-1" type="ghost" size="60" color="#f78ca0" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="navbar-title data-v-2a183b29">{{siteName}}</text></view><view class="navbar-actions data-v-2a183b29"><view data-event-opts="{{[['tap',[['goToNetworkTest',['$event']]]]]}}" class="action-btn data-v-2a183b29" bindtap="__e"><uni-icons vue-id="18efaafd-2" type="gear" size="24" color="#f78ca0" class="data-v-2a183b29" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['showSearch',['$event']]]]]}}" class="action-btn data-v-2a183b29" bindtap="__e"><uni-icons vue-id="18efaafd-3" type="search" size="24" color="#f78ca0" class="data-v-2a183b29" bind:__l="__l"></uni-icons></view></view></view></view><view class="category-section data-v-2a183b29"><view class="section-title data-v-2a183b29"><uni-icons vue-id="18efaafd-4" type="location" size="32" color="#f78ca0" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="title-text data-v-2a183b29">按省份筛选</text></view><scroll-view class="category-scroll data-v-2a183b29" scroll-x="true"><view class="category-buttons-container data-v-2a183b29"><view class="category-row data-v-2a183b29"><block wx:for="{{$root.l0}}" wx:for-item="province" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({province})}}" class="{{['category-btn','data-v-2a183b29',(currentProvince===province.value)?'active':'']}}" bindtap="__e"><text class="category-text data-v-2a183b29">{{province.label}}</text></view></block></view><view class="category-row data-v-2a183b29"><block wx:for="{{$root.l1}}" wx:for-item="province" wx:for-index="index"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({province})}}" class="{{['category-btn','data-v-2a183b29',(currentProvince===province.value)?'active':'']}}" bindtap="__e"><text class="category-text data-v-2a183b29">{{province.label}}</text></view></block></view></view></scroll-view></view><view class="posts-section data-v-2a183b29"><view class="section-title data-v-2a183b29"><view class="title-left data-v-2a183b29"><uni-icons vue-id="18efaafd-5" type="star" size="32" color="#f78ca0" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="title-text data-v-2a183b29">最新搭子</text></view><view data-event-opts="{{[['tap',[['toggleAdvancedFilter',['$event']]]]]}}" class="filter-btn-container data-v-2a183b29" bindtap="__e"><uni-icons vue-id="18efaafd-6" type="tune" size="24" color="#f78ca0" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="filter-btn-text data-v-2a183b29">筛选</text></view></view><block wx:if="{{showAdvancedFilter}}"><view class="advanced-filter-section data-v-2a183b29"><view class="filter-grid data-v-2a183b29"><view class="filter-item data-v-2a183b29"><text class="filter-label data-v-2a183b29">关键词搜索</text><input class="filter-input data-v-2a183b29" placeholder="年龄、城市、兴趣爱好..." data-event-opts="{{[['input',[['__set_model',['','searchKeyword','$event',[]]],['handleSearch',['$event']]]]]}}" value="{{searchKeyword}}" bindinput="__e"/></view><view class="filter-item data-v-2a183b29"><text class="filter-label data-v-2a183b29">性别</text><view class="gender-options data-v-2a183b29"><block wx:for="{{genderOptions}}" wx:for-item="option" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectGender',[index]]]]]}}" class="{{['gender-option','data-v-2a183b29',(genderIndex===index)?'active':'']}}" bindtap="__e"><uni-icons vue-id="{{'18efaafd-7-'+index}}" type="{{option.icon}}" size="20" color="{{genderIndex===index?'#f78ca0':'#999'}}" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="{{['gender-text','data-v-2a183b29',(genderIndex===index)?'active':'']}}">{{option.label}}</text></view></block></view></view><view class="filter-item data-v-2a183b29"><text class="filter-label data-v-2a183b29">年龄范围</text><view class="age-range-container data-v-2a183b29"><view class="age-display data-v-2a183b29"><text class="age-text data-v-2a183b29">{{''+ageRange.min+"岁 - "+(ageRange.max===99?'不限':ageRange.max+'岁')+''}}</text></view><view class="age-quick-select data-v-2a183b29"><block wx:for="{{$root.l2}}" wx:for-item="option" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectAgeOption',['$0'],[[['ageOptions','',index]]]]]]]}}" class="{{['age-option','data-v-2a183b29',(option.m0)?'active':'']}}" bindtap="__e"><text class="age-option-text data-v-2a183b29">{{option.$orig.label}}</text></view></block></view><block wx:if="{{showCustomAge}}"><view class="custom-age-range data-v-2a183b29"><view class="slider-container data-v-2a183b29"><text class="slider-label data-v-2a183b29">{{"最小年龄: "+ageRange.min+"岁"}}</text><slider class="age-slider data-v-2a183b29" value="{{ageRange.min}}" min="{{16}}" max="{{60}}" step="{{1}}" activeColor="#f78ca0" backgroundColor="#e5e7eb" block-color="#f78ca0" block-size="20" data-event-opts="{{[['change',[['handleMinAgeChange',['$event']]]]]}}" bindchange="__e"></slider></view><view class="slider-container data-v-2a183b29"><text class="slider-label data-v-2a183b29">{{"最大年龄: "+(ageRange.max===99?'不限':ageRange.max+'岁')}}</text><slider class="age-slider data-v-2a183b29" value="{{ageRange.max===99?60:ageRange.max}}" min="{{16}}" max="{{60}}" step="{{1}}" activeColor="#a6c1ee" backgroundColor="#e5e7eb" block-color="#a6c1ee" block-size="20" data-event-opts="{{[['change',[['handleMaxAgeChange',['$event']]]]]}}" bindchange="__e"></slider><view data-event-opts="{{[['tap',[['setNoAgeLimit',['$event']]]]]}}" class="no-limit-option data-v-2a183b29" bindtap="__e"><text class="{{['no-limit-text','data-v-2a183b29',(ageRange.max===99)?'active':'']}}">不限年龄上限</text></view></view></view></block></view></view><view class="filter-actions data-v-2a183b29"><view data-event-opts="{{[['tap',[['resetFilters',['$event']]]]]}}" class="filter-btn reset data-v-2a183b29" bindtap="__e"><uni-icons vue-id="18efaafd-8" type="refresh" size="20" color="#868f96" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="data-v-2a183b29">重置</text></view><view data-event-opts="{{[['tap',[['applyFilters',['$event']]]]]}}" class="filter-btn apply data-v-2a183b29" bindtap="__e"><uni-icons vue-id="18efaafd-9" type="checkmarkempty" size="20" color="#fff" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="data-v-2a183b29">应用筛选</text></view></view></view></view></block><scroll-view class="submission-list data-v-2a183b29" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="list-container data-v-2a183b29"><view class="submission-grid data-v-2a183b29"><block wx:for="{{filteredSubmissionList}}" wx:for-item="submission" wx:for-index="index"><soul-submission-card vue-id="{{'18efaafd-10-'+index}}" submission="{{submission}}" data-event-opts="{{[['^click',[['goToDetail']]]]}}" bind:click="__e" class="data-v-2a183b29" bind:__l="__l"></soul-submission-card></block></view><block wx:if="{{loading}}"><view class="loading-container data-v-2a183b29"><uni-icons class="loading-icon data-v-2a183b29" vue-id="18efaafd-11" type="heart" size="48" color="#f78ca0" bind:__l="__l"></uni-icons><text class="loading-text data-v-2a183b29">正在努力加载搭子信息中...</text></view></block><block wx:if="{{$root.g1}}"><view class="empty-container data-v-2a183b29"><view class="empty-icon data-v-2a183b29"><uni-icons vue-id="18efaafd-12" type="search" size="80" color="#c8a8e9" class="data-v-2a183b29" bind:__l="__l"></uni-icons></view><text class="empty-title data-v-2a183b29">没有找到相关搭子</text><text class="empty-subtitle data-v-2a183b29">试试调整筛选条件或搜索关键词</text><view class="search-suggestions data-v-2a183b29"><text class="suggestions-title data-v-2a183b29">热门搜索建议：</text><view class="suggestions-tags data-v-2a183b29"><block wx:for="{{searchSuggestions}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['applySuggestion',['$0'],[[['searchSuggestions','',index]]]]]]]}}" class="suggestion-tag data-v-2a183b29" bindtap="__e"><text class="tag-text data-v-2a183b29">{{tag}}</text></view></block></view></view><view data-event-opts="{{[['tap',[['resetFilters',['$event']]]]]}}" class="reset-filters-btn data-v-2a183b29" bindtap="__e"><uni-icons vue-id="18efaafd-13" type="refresh" size="20" color="#f78ca0" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="reset-text data-v-2a183b29">重置筛选条件</text></view></view></block></view></scroll-view></view><view class="bottom-tabbar data-v-2a183b29"><view data-event-opts="{{[['tap',[['switchTab',['home']]]]]}}" class="tab-item active data-v-2a183b29" bindtap="__e"><uni-icons vue-id="18efaafd-14" type="home" size="24" color="#f78ca0" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="tab-text active data-v-2a183b29">主页</text></view><view data-event-opts="{{[['tap',[['switchTab',['add']]]]]}}" class="tab-item data-v-2a183b29" bindtap="__e"><view class="add-btn data-v-2a183b29"><uni-icons vue-id="18efaafd-15" type="plus" size="24" color="#fff" class="data-v-2a183b29" bind:__l="__l"></uni-icons></view><text class="tab-text data-v-2a183b29">投稿</text></view><view data-event-opts="{{[['tap',[['switchTab',['profile']]]]]}}" class="tab-item data-v-2a183b29" bindtap="__e"><uni-icons vue-id="18efaafd-16" type="person" size="24" color="#999" class="data-v-2a183b29" bind:__l="__l"></uni-icons><text class="tab-text data-v-2a183b29">我的</text></view></view><uni-popup vue-id="18efaafd-17" type="top" background-color="#ffffff" data-ref="searchPopup" class="data-v-2a183b29 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="search-container data-v-2a183b29"><view class="search-bar data-v-2a183b29"><uni-icons vue-id="{{('18efaafd-18')+','+('18efaafd-17')}}" type="search" size="24" color="#FF6B9D" class="data-v-2a183b29" bind:__l="__l"></uni-icons><input class="search-input data-v-2a183b29" placeholder="搜索投稿内容..." data-event-opts="{{[['confirm',[['handleSearch',['$event']]]],['input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" value="{{searchKeyword}}" bindconfirm="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['hideSearch',['$event']]]]]}}" class="cancel-btn data-v-2a183b29" bindtap="__e">取消</text></view></view></uni-popup></view>