{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/index.vue?29c4", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/index.vue?b9eb", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/index.vue?4082", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/index.vue?6339", "uni-app:///pages/mine/index.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/index.vue?fafa", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/index.vue?af3b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userStats", "submissions", "favorites", "views", "pendingSubmissions", "computed", "displayUserInfo", "nickname", "avatar", "desc", "onLoad", "onShow", "methods", "goBack", "uni", "checkLoginStatus", "console", "showLoginModal", "title", "content", "confirmText", "cancelText", "success", "performWxLogin", "provider", "loginRes", "userInfoRes", "code", "userInfo", "icon", "loadUserStats", "userApi", "res", "<PERSON><PERSON><PERSON><PERSON>", "count", "sizeType", "sourceType", "uploadAvatar", "goToMySubmissions", "url", "goToMyFavorites", "goToProfile", "goToSettings", "goToHelp", "goToAbout", "goToCreate", "switchTab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACgKtpB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC,0CACA;IAEAC;MAAA;MACA;QACAC;QACAC;QACAC;MACA;IACA;EAAA,EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEAC;MAAA;MACAH;QACAI;QACAC;QACAC;QACAC;QACAC;UACA;YACA;UACA;YACAR;UACA;QACA;MACA;IACA;IAEAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAT;kBAAAU;gBAAA;cAAA;gBAAAC;gBAAA;gBAAA,OAGAX;kBACAL;gBACA;cAAA;gBAFAiB;gBAAA;gBAAA,OAKA;kBACAC;kBACAC;gBACA;cAAA;gBAEAd;kBACAI;kBACAW;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAb;gBACAF;kBACAI;kBACAW;gBACA;gBACAf;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;kBACA7B;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA2B;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;gBACA;gBACA;kBACAf;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA6B;MAAA;MACA;QACA;QACA;MACA;MAEAnB;QACAoB;QACAC;QACAC;QACAd;UACA;QACA;MACA;IACA;IAEAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAvB;kBAAAI;gBAAA;gBAAA;gBAAA,OAEAa;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAxB;gBACA;cAAA;gBAEAM;kBACAI;kBACAW;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAb;gBACAF;kBACAI;kBACAW;gBACA;cAAA;gBAAA;gBAEAf;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAwB;MACAxB;QACAyB;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;MAEA1B;QACAyB;MACA;IACA;IAEAE;MACA3B;QACAyB;MACA;IACA;IAEAG;MACA5B;QACAyB;MACA;IACA;IAEAI;MACA7B;QACAyB;MACA;IACA;IAEAK;MACA9B;QACAyB;MACA;IACA;IAEAM;MACA/B;QACAyB;MACA;IACA;IAEA;IACAO;MACA;QACA;UACAhC;YACAyB;UACA;UACA;QACA;UACAzB;YACAyB;UACA;UACA;QACA;UACA;UACA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5YA;AAAA;AAAA;AAAA;AAAiuC,CAAgB,8rCAAG,EAAC,C;;;;;;;;;;;ACArvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4bd6864f&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4bd6864f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4bd6864f&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"mine-page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-content\">\n        <view class=\"navbar-left\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n        </view>\n        <text class=\"navbar-title\">个人中心</text>\n        <view class=\"navbar-right\">\n          <view class=\"action-btn\" @click=\"goToSettings\">\n            <uni-icons type=\"gear\" size=\"24\" color=\"#f78ca0\"></uni-icons>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view scroll-y=\"true\" class=\"mine-content\">\n      <!-- 用户信息卡片 -->\n      <view class=\"user-card\">\n        <view class=\"user-avatar\">\n          <image\n            :src=\"displayUserInfo.avatar\"\n            class=\"avatar-image\"\n            mode=\"aspectFill\"\n            @click=\"chooseAvatar\"\n          />\n          <view class=\"avatar-edit\">\n            <uni-icons type=\"camera\" size=\"16\" color=\"#fff\"></uni-icons>\n          </view>\n        </view>\n\n        <view class=\"user-info\">\n          <text class=\"user-name\">{{ displayUserInfo.nickname }}</text>\n          <text class=\"user-desc\">{{ displayUserInfo.desc }}</text>\n        </view>\n        \n        <view class=\"user-stats\">\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userStats.submissions }}</text>\n            <text class=\"stat-label\">投稿</text>\n          </view>\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userStats.favorites }}</text>\n            <text class=\"stat-label\">收藏</text>\n          </view>\n          <view class=\"stat-item\">\n            <text class=\"stat-number\">{{ userStats.views }}</text>\n            <text class=\"stat-label\">浏览</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 功能菜单 -->\n      <view class=\"menu-section\">\n        <view class=\"menu-card\">\n          <view class=\"menu-item\" @click=\"goToMySubmissions\">\n            <view class=\"menu-icon\">\n              <uni-icons type=\"compose\" size=\"24\" color=\"#f78ca0\"></uni-icons>\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">我的投稿</text>\n              <text class=\"menu-desc\">管理我发布的投稿</text>\n            </view>\n            <view class=\"menu-badge\" v-if=\"userStats.pendingSubmissions > 0\">\n              <text class=\"badge-text\">{{ userStats.pendingSubmissions }}</text>\n            </view>\n            <view class=\"menu-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n\n          <view class=\"menu-item\" @click=\"goToMyFavorites\">\n            <view class=\"menu-icon\">\n              <uni-icons type=\"heart\" size=\"24\" color=\"#feb47b\"></uni-icons>\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">我的收藏</text>\n              <text class=\"menu-desc\">查看收藏的投稿</text>\n            </view>\n            <view class=\"menu-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n\n          <view class=\"menu-item\" @click=\"goToProfile\">\n            <view class=\"menu-icon\">\n              <uni-icons type=\"person\" size=\"24\" color=\"#a6c1ee\"></uni-icons>\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">个人资料</text>\n              <text class=\"menu-desc\">编辑个人信息</text>\n            </view>\n            <view class=\"menu-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n        </view>\n\n        <view class=\"menu-card\">\n          <view class=\"menu-item\" @click=\"goToHelp\">\n            <view class=\"menu-icon\">\n              <uni-icons type=\"help\" size=\"24\" color=\"#c8a8e9\"></uni-icons>\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">帮助与反馈</text>\n              <text class=\"menu-desc\">使用帮助和意见反馈</text>\n            </view>\n            <view class=\"menu-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n\n          <view class=\"menu-item\" @click=\"goToAbout\">\n            <view class=\"menu-icon\">\n              <uni-icons type=\"info\" size=\"24\" color=\"#10b981\"></uni-icons>\n            </view>\n            <view class=\"menu-content\">\n              <text class=\"menu-title\">关于我们</text>\n              <text class=\"menu-desc\">了解更多信息</text>\n            </view>\n            <view class=\"menu-arrow\">\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 快捷操作 -->\n      <view class=\"quick-actions\">\n        <view class=\"action-card\" @click=\"goToCreate\">\n          <view class=\"action-icon\">\n            <uni-icons type=\"plus\" size=\"32\" color=\"#fff\"></uni-icons>\n          </view>\n          <text class=\"action-text\">发布投稿</text>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 底部菜单栏 -->\n    <view class=\"bottom-tabbar\">\n      <view class=\"tab-item\" @click=\"switchTab('home')\">\n        <uni-icons type=\"home\" size=\"32\" color=\"#999\"></uni-icons>\n        <text class=\"tab-text\">首页</text>\n      </view>\n      <view class=\"tab-item\" @click=\"switchTab('add')\">\n        <view class=\"add-btn\">\n          <uni-icons type=\"plus\" size=\"36\" color=\"white\"></uni-icons>\n        </view>\n        <text class=\"tab-text\">投稿</text>\n      </view>\n      <view class=\"tab-item active\">\n        <uni-icons type=\"person-filled\" size=\"32\" color=\"#f78ca0\"></uni-icons>\n        <text class=\"tab-text active\">我的</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport userApi from '@/common/api/user.js'\n\nexport default {\n  data() {\n    return {\n      userStats: {\n        submissions: 0,\n        favorites: 0,\n        views: 0,\n        pendingSubmissions: 0\n      }\n    }\n  },\n  computed: {\n    ...mapGetters('user', ['userInfo', 'isLoggedIn', 'userNickname', 'userAvatar']),\n\n    displayUserInfo() {\n      return {\n        nickname: this.userNickname,\n        avatar: this.userAvatar,\n        desc: this.userInfo?.desc || '这个人很懒，什么都没留下～'\n      }\n    }\n  },\n  onLoad() {\n    this.checkLoginStatus()\n    this.loadUserStats()\n  },\n  onShow() {\n    // 页面显示时刷新数据\n    this.loadUserStats()\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n\n    async checkLoginStatus() {\n      if (!this.isLoggedIn) {\n        // 未登录，显示登录提示\n        this.showLoginModal()\n      } else {\n        // 已登录，获取最新用户信息\n        try {\n          await this.$store.dispatch('user/getUserInfo')\n        } catch (error) {\n          console.error('获取用户信息失败:', error)\n        }\n      }\n    },\n\n    showLoginModal() {\n      uni.showModal({\n        title: '登录提示',\n        content: '需要登录后才能使用个人中心功能',\n        confirmText: '立即登录',\n        cancelText: '稍后再说',\n        success: (res) => {\n          if (res.confirm) {\n            this.performWxLogin()\n          } else {\n            uni.navigateBack()\n          }\n        }\n      })\n    },\n\n    async performWxLogin() {\n      try {\n        // 获取微信登录code\n        const loginRes = await uni.login({ provider: 'weixin' })\n\n        // 获取用户信息\n        const userInfoRes = await uni.getUserProfile({\n          desc: '用于完善用户资料'\n        })\n\n        // 调用登录接口\n        await this.$store.dispatch('user/wxLogin', {\n          code: loginRes.code,\n          userInfo: userInfoRes.userInfo\n        })\n\n        uni.showToast({\n          title: '登录成功',\n          icon: 'success'\n        })\n\n      } catch (error) {\n        console.error('微信登录失败:', error)\n        uni.showToast({\n          title: '登录失败，请重试',\n          icon: 'none'\n        })\n        uni.navigateBack()\n      }\n    },\n\n    async loadUserStats() {\n      if (!this.isLoggedIn) {\n        this.userStats = {\n          submissions: 0,\n          favorites: 0,\n          views: 0,\n          pendingSubmissions: 0\n        }\n        return\n      }\n\n      try {\n        const res = await userApi.getUserStats()\n        if (res.code === 200) {\n          this.userStats = res.data\n        }\n      } catch (error) {\n        console.error('获取用户统计失败:', error)\n        // 使用模拟数据\n        this.userStats = {\n          submissions: 3,\n          favorites: 12,\n          views: 156,\n          pendingSubmissions: 1\n        }\n      }\n    },\n\n    chooseAvatar() {\n      if (!this.isLoggedIn) {\n        this.showLoginModal()\n        return\n      }\n\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          this.uploadAvatar(res.tempFilePaths[0])\n        }\n      })\n    },\n\n    async uploadAvatar(filePath) {\n      try {\n        uni.showLoading({ title: '上传中...' })\n\n        const res = await userApi.uploadAvatar(filePath)\n        if (res.code === 200) {\n          // 更新用户信息\n          await this.$store.dispatch('user/updateUserInfo', {\n            avatar: res.data.avatar_url\n          })\n\n          uni.showToast({\n            title: '头像更新成功',\n            icon: 'success'\n          })\n        }\n      } catch (error) {\n        console.error('头像上传失败:', error)\n        uni.showToast({\n          title: '头像上传失败',\n          icon: 'none'\n        })\n      } finally {\n        uni.hideLoading()\n      }\n    },\n\n    goToMySubmissions() {\n      uni.navigateTo({\n        url: '/pages/mine/submissions'\n      })\n    },\n\n    goToMyFavorites() {\n      if (!this.isLoggedIn) {\n        this.showLoginModal()\n        return\n      }\n\n      uni.navigateTo({\n        url: '/pages/mine/favorites'\n      })\n    },\n\n    goToProfile() {\n      uni.navigateTo({\n        url: '/pages/mine/profile'\n      })\n    },\n\n    goToSettings() {\n      uni.navigateTo({\n        url: '/pages/mine/settings'\n      })\n    },\n\n    goToHelp() {\n      uni.navigateTo({\n        url: '/pages/mine/help'\n      })\n    },\n\n    goToAbout() {\n      uni.navigateTo({\n        url: '/pages/mine/about'\n      })\n    },\n\n    goToCreate() {\n      uni.navigateTo({\n        url: '/pages/submission/create'\n      })\n    },\n\n    // 底部菜单切换\n    switchTab(tab) {\n      switch(tab) {\n        case 'home':\n          uni.switchTab({\n            url: '/pages/index'\n          })\n          break\n        case 'add':\n          uni.navigateTo({\n            url: '/pages/submission/create'\n          })\n          break\n        case 'profile':\n          // 当前页面，不需要跳转\n          break\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.mine-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);\n}\n\n.custom-navbar {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  @include glass-effect(0.6);\n  padding-top: var(--status-bar-height);\n\n  .navbar-content {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 24rpx;\n\n    .navbar-left, .navbar-right {\n      width: 60rpx;\n      display: flex;\n      justify-content: center;\n    }\n\n    .navbar-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n\n    .action-btn {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      transition: all 0.3s ease;\n\n      &:active {\n        background: rgba(255, 255, 255, 0.2);\n        transform: scale(0.95);\n      }\n    }\n  }\n}\n\n.mine-content {\n  height: calc(100vh - 120rpx);\n  padding: 20rpx;\n  padding-bottom: 140rpx; // 增加底部内边距，避免被底部菜单栏遮挡\n}\n\n.user-card {\n  @include glass-effect(0.7);\n  border-radius: 24rpx;\n  padding: 32rpx;\n  margin-bottom: 20rpx;\n  text-align: center;\n  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);\n\n  .user-avatar {\n    position: relative;\n    width: 120rpx;\n    height: 120rpx;\n    margin: 0 auto 24rpx;\n\n    .avatar-image {\n      width: 100%;\n      height: 100%;\n      border-radius: 50%;\n      border: 4rpx solid rgba(247, 140, 160, 0.2);\n    }\n\n    .avatar-edit {\n      position: absolute;\n      bottom: 0;\n      right: 0;\n      width: 36rpx;\n      height: 36rpx;\n      background: #f78ca0;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border: 3rpx solid white;\n    }\n  }\n\n  .user-info {\n    margin-bottom: 32rpx;\n\n    .user-name {\n      display: block;\n      font-size: 32rpx;\n      font-weight: 700;\n      color: $soul-gray-800;\n      margin-bottom: 8rpx;\n    }\n\n    .user-desc {\n      font-size: 24rpx;\n      color: $soul-gray-500;\n      line-height: 1.4;\n    }\n  }\n\n  .user-stats {\n    display: flex;\n    justify-content: space-around;\n\n    .stat-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 8rpx;\n\n      .stat-number {\n        font-size: 36rpx;\n        font-weight: 700;\n        color: #f78ca0;\n      }\n\n      .stat-label {\n        font-size: 22rpx;\n        color: $soul-gray-600;\n      }\n    }\n  }\n}\n\n.menu-section {\n  .menu-card {\n    @include glass-effect(0.6);\n    border-radius: 20rpx;\n    margin-bottom: 16rpx;\n    overflow: hidden;\n    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n\n    .menu-item {\n      display: flex;\n      align-items: center;\n      padding: 24rpx;\n      border-bottom: 1rpx solid rgba(0,0,0,0.05);\n      transition: all 0.3s ease;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:active {\n        background: rgba(247, 140, 160, 0.05);\n      }\n\n      .menu-icon {\n        width: 48rpx;\n        height: 48rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: rgba(255, 255, 255, 0.8);\n        border-radius: 12rpx;\n        margin-right: 16rpx;\n      }\n\n      .menu-content {\n        flex: 1;\n\n        .menu-title {\n          display: block;\n          font-size: 26rpx;\n          font-weight: 600;\n          color: $soul-gray-800;\n          margin-bottom: 4rpx;\n        }\n\n        .menu-desc {\n          font-size: 22rpx;\n          color: $soul-gray-500;\n        }\n      }\n\n      .menu-badge {\n        background: #f78ca0;\n        border-radius: 12rpx;\n        padding: 4rpx 8rpx;\n        margin-right: 12rpx;\n\n        .badge-text {\n          font-size: 18rpx;\n          color: white;\n          font-weight: 600;\n        }\n      }\n\n      .menu-arrow {\n        opacity: 0.6;\n      }\n    }\n  }\n}\n\n.quick-actions {\n  margin-top: 20rpx;\n\n  .action-card {\n    @include glass-effect(0.7);\n    border-radius: 20rpx;\n    padding: 32rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 16rpx;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);\n    transition: all 0.3s ease;\n\n    &:active {\n      transform: translateY(2rpx) scale(0.98);\n    }\n\n    .action-icon {\n      width: 80rpx;\n      height: 80rpx;\n      background: rgba(255, 255, 255, 0.2);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .action-text {\n      font-size: 28rpx;\n      color: white;\n      font-weight: 600;\n    }\n  }\n}\n\n.bottom-tabbar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20rpx);\n  border-top: 1rpx solid rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  padding-bottom: env(safe-area-inset-bottom);\n  z-index: 1000;\n\n  .tab-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 8rpx;\n    padding: 12rpx 0;\n    transition: all 0.3s ease;\n\n    &:active {\n      transform: scale(0.95);\n    }\n\n    .tab-text {\n      font-size: 20rpx;\n      color: #999;\n      font-weight: 500;\n\n      &.active {\n        color: #f78ca0;\n      }\n    }\n\n    .add-btn {\n      width: 64rpx;\n      height: 64rpx;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);\n    }\n\n    &.active {\n      .tab-text {\n        color: #f78ca0;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752120110328\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}