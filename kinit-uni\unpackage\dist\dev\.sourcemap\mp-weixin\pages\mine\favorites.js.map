{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/favorites.vue?bb7e", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/favorites.vue?7fe2", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/favorites.vue?128c", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/favorites.vue?98c8", "uni-app:///pages/mine/favorites.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/favorites.vue?746e", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/favorites.vue?a1a3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "SoulSubmissionCard", "data", "favoritesList", "loading", "editMode", "selectedItems", "pagination", "page", "size", "total", "onLoad", "onShow", "methods", "goBack", "uni", "loadFavorites", "submissionApi", "res", "console", "getMockFavorites", "id", "title", "submission_code", "city", "province", "age", "gender", "occupation", "cover_image", "favorite_time", "showLoginModal", "content", "confirmText", "cancelText", "success", "performWxLogin", "provider", "loginRes", "desc", "userInfoRes", "code", "userInfo", "icon", "mockLogin", "mockUserInfo", "nickname", "avatar", "mockToken", "toggleEditMode", "toggleSelect", "selectAll", "deselectAll", "handleItemClick", "goToDetail", "url", "batchDelete", "formatTime", "goToHome"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1EA;AAAA;AAAA;AAAA;AAAsoB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsG1pB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAT;kBACAC;gBACA;cAAA;gBAHAS;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA,QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAT;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEAC;MAAA;MACAhB;QACAO;QACAU;QACAC;QACAC;QACAC;UACA;YACA;UACA;YACApB;UACA;QACA;MACA;IACA;IAEAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OASArB;kBAAAsB;gBAAA;cAAA;gBAAAC;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGAvB;kBACAwB;gBACA;cAAA;gBAFAC;gBAAA,IAIAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGA;kBACAC;kBACAC;gBACA;cAAA;gBAEA3B;kBACAO;kBACAqB;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAxB;;gBAEA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAJ;kBACAO;kBACAqB;gBACA;gBACA5B;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;kBACAxB;kBACAyB;kBACAC;kBACApB;kBACAY;gBACA,GAEA;gBACAS,wCAEA;gBACA;gBACA;gBAEAjC;kBACAO;kBACAqB;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAM;MACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MAEAvC;QACAwC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAzC;kBACAO;kBACAU;kBACAG;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACA;gCACA;kCACA;kCACA;;kCAEA;kCACA,mDACA;oCAAA;kCAAA,EACA;kCAEA;kCACA;kCAEApB;oCACAO;oCACAqB;kCACA;gCACA;kCACAxB;kCACAJ;oCACAO;oCACAqB;kCACA;gCACA;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAc;MACA;MACA;MACA;MAEA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA3C;QACAwC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzYA;AAAA;AAAA;AAAA;AAAquC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAzvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/favorites.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/favorites.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./favorites.vue?vue&type=template&id=c823fa58&scoped=true&\"\nvar renderjs\nimport script from \"./favorites.vue?vue&type=script&lang=js&\"\nexport * from \"./favorites.vue?vue&type=script&lang=js&\"\nimport style0 from \"./favorites.vue?vue&type=style&index=0&id=c823fa58&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c823fa58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/favorites.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./favorites.vue?vue&type=template&id=c823fa58&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    soulSubmissionCard: function () {\n      return import(\n        /* webpackChunkName: \"components/soul-submission-card/soul-submission-card\" */ \"@/components/soul-submission-card/soul-submission-card.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.favoritesList.length\n  var g1 = _vm.selectedItems.length\n  var g2 = _vm.editMode ? _vm.selectedItems.length : null\n  var g3 = _vm.editMode ? _vm.favoritesList.length : null\n  var g4 = _vm.editMode ? _vm.selectedItems.length : null\n  var g5 = _vm.editMode ? _vm.selectedItems.length : null\n  var l0 = _vm.__map(_vm.favoritesList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g6 = _vm.selectedItems.includes(item.id)\n    var g7 = _vm.editMode ? _vm.selectedItems.includes(item.id) : null\n    var g8 = _vm.editMode ? _vm.selectedItems.includes(item.id) : null\n    var m0 = _vm.formatTime(item.favorite_time)\n    return {\n      $orig: $orig,\n      g6: g6,\n      g7: g7,\n      g8: g8,\n      m0: m0,\n    }\n  })\n  var g9 = _vm.favoritesList.length === 0 && !_vm.loading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        l0: l0,\n        g9: g9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./favorites.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./favorites.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"favorites-page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-content\">\n        <view class=\"navbar-left\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n        </view>\n        <text class=\"navbar-title\">我的收藏</text>\n        <view class=\"navbar-right\">\n          <view class=\"action-btn\" @click=\"toggleEditMode\">\n            <uni-icons :type=\"editMode ? 'checkmarkempty' : 'compose'\" size=\"24\" color=\"#f78ca0\"></uni-icons>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <scroll-view scroll-y=\"true\" class=\"favorites-content\">\n      <!-- 收藏统计 -->\n      <view class=\"stats-card\">\n        <view class=\"stats-content\">\n          <view class=\"stats-item\">\n            <text class=\"stats-number\">{{ favoritesList.length }}</text>\n            <text class=\"stats-label\">收藏总数</text>\n          </view>\n          <view class=\"stats-item\">\n            <text class=\"stats-number\">{{ selectedItems.length }}</text>\n            <text class=\"stats-label\">已选择</text>\n          </view>\n        </view>\n        \n        <view v-if=\"editMode\" class=\"batch-actions\">\n          <view class=\"batch-btn select-all\" @click=\"selectAll\" v-if=\"selectedItems.length < favoritesList.length\">\n            <uni-icons type=\"checkmarkempty\" size=\"16\" color=\"#f78ca0\"></uni-icons>\n            <text class=\"batch-text\">全选</text>\n          </view>\n          <view class=\"batch-btn deselect-all\" @click=\"deselectAll\" v-if=\"selectedItems.length > 0\">\n            <uni-icons type=\"closeempty\" size=\"16\" color=\"#999\"></uni-icons>\n            <text class=\"batch-text\">取消全选</text>\n          </view>\n          <view class=\"batch-btn delete\" @click=\"batchDelete\" v-if=\"selectedItems.length > 0\">\n            <uni-icons type=\"trash\" size=\"16\" color=\"#ef4444\"></uni-icons>\n            <text class=\"batch-text\">删除选中</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 收藏列表 -->\n      <view class=\"favorites-list\">\n        <view \n          v-for=\"(item, index) in favoritesList\" \n          :key=\"index\"\n          class=\"favorite-item\"\n          :class=\"{ selected: selectedItems.includes(item.id) }\"\n          @click=\"handleItemClick(item)\"\n        >\n          <!-- 选择框 -->\n          <view v-if=\"editMode\" class=\"select-checkbox\" @click.stop=\"toggleSelect(item.id)\">\n            <uni-icons \n              :type=\"selectedItems.includes(item.id) ? 'checkmarkempty' : 'circle'\" \n              size=\"20\" \n              :color=\"selectedItems.includes(item.id) ? '#f78ca0' : '#d1d5db'\"\n            ></uni-icons>\n          </view>\n\n          <!-- 投稿卡片 -->\n          <soul-submission-card \n            :submission=\"item\" \n            :show-favorite=\"false\"\n            @click=\"goToDetail(item)\"\n          />\n\n          <!-- 收藏时间 -->\n          <view class=\"favorite-time\">\n            <uni-icons type=\"calendar\" size=\"14\" color=\"#999\"></uni-icons>\n            <text class=\"time-text\">收藏于 {{ formatTime(item.favorite_time) }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"favoritesList.length === 0 && !loading\" class=\"empty-state\">\n        <view class=\"empty-icon\">\n          <uni-icons type=\"heart\" size=\"80\" color=\"#d1d5db\"></uni-icons>\n        </view>\n        <text class=\"empty-text\">还没有收藏任何投稿</text>\n        <text class=\"empty-desc\">去首页看看有没有喜欢的投稿吧～</text>\n        <view class=\"empty-action\" @click=\"goToHome\">\n          <text class=\"action-text\">去首页看看</text>\n        </view>\n      </view>\n\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-state\">\n        <uni-icons type=\"spinner-cycle\" size=\"32\" color=\"#f78ca0\"></uni-icons>\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nimport submissionApi from '@/common/api/submission.js'\nimport SoulSubmissionCard from '@/components/soul-submission-card/soul-submission-card.vue'\n\nexport default {\n  components: {\n    SoulSubmissionCard\n  },\n  data() {\n    return {\n      favoritesList: [],\n      loading: true,\n      editMode: false,\n      selectedItems: [],\n      pagination: {\n        page: 1,\n        size: 20,\n        total: 0\n      }\n    }\n  },\n  onLoad() {\n    this.loadFavorites()\n  },\n  onShow() {\n    // 页面显示时刷新数据\n    this.loadFavorites()\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n\n    async loadFavorites() {\n      if (!this.$store.getters['user/isLoggedIn']) {\n        this.showLoginModal()\n        return\n      }\n\n      this.loading = true\n      \n      try {\n        const res = await submissionApi.getFavoriteList({\n          page: this.pagination.page,\n          size: this.pagination.size\n        })\n        \n        if (res.code === 200) {\n          this.favoritesList = res.data.items || []\n          this.pagination.total = res.data.total || 0\n        } else {\n          throw new Error(res.message || '加载失败')\n        }\n      } catch (error) {\n        console.error('加载收藏列表失败:', error)\n        // 使用模拟数据\n        this.favoritesList = this.getMockFavorites()\n      } finally {\n        this.loading = false\n      }\n    },\n\n    getMockFavorites() {\n      return [\n        {\n          id: 1,\n          title: '寻找游戏搭子',\n          submission_code: 'zhiyu001',\n          city: '杭州',\n          province: '浙江',\n          age: 22,\n          gender: '女',\n          occupation: '学生',\n          cover_image: '/static/images/demo1.jpg',\n          favorite_time: '2024-01-15 14:30:00'\n        },\n        {\n          id: 2,\n          title: '一起看电影',\n          submission_code: 'zhiyu002',\n          city: '上海',\n          province: '上海',\n          age: 25,\n          gender: '女',\n          occupation: '设计师',\n          cover_image: '/static/images/demo2.jpg',\n          favorite_time: '2024-01-16 09:15:00'\n        }\n      ]\n    },\n\n    showLoginModal() {\n      uni.showModal({\n        title: '登录提示',\n        content: '需要登录后才能查看收藏',\n        confirmText: '立即登录',\n        cancelText: '稍后再说',\n        success: (res) => {\n          if (res.confirm) {\n            this.performWxLogin()\n          } else {\n            uni.navigateBack()\n          }\n        }\n      })\n    },\n\n    async performWxLogin() {\n      try {\n        // 检查是否在开发环境\n        // #ifdef H5\n        // 开发环境使用模拟登录\n        await this.mockLogin()\n        return\n        // #endif\n\n        const loginRes = await uni.login({ provider: 'weixin' })\n\n        if (!loginRes.code) {\n          throw new Error('获取微信登录code失败')\n        }\n\n        const userInfoRes = await uni.getUserProfile({\n          desc: '用于完善用户资料'\n        })\n\n        if (!userInfoRes.userInfo) {\n          throw new Error('获取用户信息失败')\n        }\n\n        await this.$store.dispatch('user/wxLogin', {\n          code: loginRes.code,\n          userInfo: userInfoRes.userInfo\n        })\n\n        uni.showToast({\n          title: '登录成功',\n          icon: 'success'\n        })\n\n        this.loadFavorites()\n\n      } catch (error) {\n        console.error('微信登录失败:', error)\n\n        // 如果微信登录失败，尝试模拟登录\n        try {\n          await this.mockLogin()\n        } catch (mockError) {\n          console.error('模拟登录也失败:', mockError)\n          uni.showToast({\n            title: '登录失败，请重试',\n            icon: 'none'\n          })\n          uni.navigateBack()\n        }\n      }\n    },\n\n    // 模拟登录（开发环境使用）\n    async mockLogin() {\n      try {\n        // 模拟用户信息\n        const mockUserInfo = {\n          id: 1,\n          nickname: '测试用户',\n          avatar: '/static/images/default-avatar.png',\n          gender: 1,\n          desc: '这是一个测试用户'\n        }\n\n        // 模拟token\n        const mockToken = 'mock_token_' + Date.now()\n\n        // 直接设置到store\n        this.$store.commit('user/SET_TOKEN', mockToken)\n        this.$store.commit('user/SET_USER_INFO', mockUserInfo)\n\n        uni.showToast({\n          title: '模拟登录成功',\n          icon: 'success'\n        })\n\n        this.loadFavorites()\n\n      } catch (error) {\n        throw new Error('模拟登录失败: ' + error.message)\n      }\n    },\n\n    toggleEditMode() {\n      this.editMode = !this.editMode\n      if (!this.editMode) {\n        this.selectedItems = []\n      }\n    },\n\n    toggleSelect(itemId) {\n      const index = this.selectedItems.indexOf(itemId)\n      if (index > -1) {\n        this.selectedItems.splice(index, 1)\n      } else {\n        this.selectedItems.push(itemId)\n      }\n    },\n\n    selectAll() {\n      this.selectedItems = this.favoritesList.map(item => item.id)\n    },\n\n    deselectAll() {\n      this.selectedItems = []\n    },\n\n    handleItemClick(item) {\n      if (this.editMode) {\n        this.toggleSelect(item.id)\n      } else {\n        this.goToDetail(item)\n      }\n    },\n\n    goToDetail(item) {\n      if (this.editMode) return\n      \n      uni.navigateTo({\n        url: `/pages/submission/detail?id=${item.id}`\n      })\n    },\n\n    async batchDelete() {\n      if (this.selectedItems.length === 0) return\n\n      uni.showModal({\n        title: '确认删除',\n        content: `确定要删除选中的 ${this.selectedItems.length} 个收藏吗？`,\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              // 这里应该调用批量删除API\n              // await submissionApi.batchDeleteFavorites(this.selectedItems)\n              \n              // 模拟删除\n              this.favoritesList = this.favoritesList.filter(\n                item => !this.selectedItems.includes(item.id)\n              )\n              \n              this.selectedItems = []\n              this.editMode = false\n              \n              uni.showToast({\n                title: '删除成功',\n                icon: 'success'\n              })\n            } catch (error) {\n              console.error('批量删除失败:', error)\n              uni.showToast({\n                title: '删除失败，请重试',\n                icon: 'none'\n              })\n            }\n          }\n        }\n      })\n    },\n\n    formatTime(timeStr) {\n      const date = new Date(timeStr)\n      const now = new Date()\n      const diff = now - date\n\n      const minutes = Math.floor(diff / (1000 * 60))\n      const hours = Math.floor(diff / (1000 * 60 * 60))\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n\n      if (minutes < 60) {\n        return `${minutes}分钟前`\n      } else if (hours < 24) {\n        return `${hours}小时前`\n      } else if (days < 7) {\n        return `${days}天前`\n      } else {\n        return date.toLocaleDateString()\n      }\n    },\n\n    goToHome() {\n      uni.switchTab({\n        url: '/pages/index'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.favorites-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);\n}\n\n.custom-navbar {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  @include glass-effect(0.6);\n  padding-top: var(--status-bar-height);\n\n  .navbar-content {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 24rpx;\n\n    .navbar-left, .navbar-right {\n      width: 60rpx;\n      display: flex;\n      justify-content: center;\n    }\n\n    .navbar-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n\n    .action-btn {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      transition: all 0.3s ease;\n\n      &:active {\n        background: rgba(255, 255, 255, 0.2);\n        transform: scale(0.95);\n      }\n    }\n  }\n}\n\n.favorites-content {\n  height: calc(100vh - 120rpx);\n  padding: 20rpx;\n  padding-bottom: 40rpx;\n}\n\n.stats-card {\n  @include glass-effect(0.7);\n  border-radius: 20rpx;\n  padding: 24rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n\n  .stats-content {\n    display: flex;\n    justify-content: space-around;\n    margin-bottom: 16rpx;\n\n    .stats-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 8rpx;\n\n      .stats-number {\n        font-size: 32rpx;\n        font-weight: 700;\n        color: #f78ca0;\n      }\n\n      .stats-label {\n        font-size: 22rpx;\n        color: $soul-gray-600;\n      }\n    }\n  }\n\n  .batch-actions {\n    border-top: 1rpx solid rgba(0,0,0,0.05);\n    padding-top: 16rpx;\n    display: flex;\n    justify-content: center;\n\n    .batch-btn {\n      display: flex;\n      align-items: center;\n      gap: 8rpx;\n      padding: 12rpx 24rpx;\n      border-radius: 20rpx;\n      transition: all 0.3s ease;\n\n      &.delete {\n        background: rgba(239, 68, 68, 0.1);\n        border: 1rpx solid rgba(239, 68, 68, 0.3);\n\n        .batch-text {\n          color: #ef4444;\n        }\n      }\n\n      .batch-text {\n        font-size: 24rpx;\n        font-weight: 500;\n      }\n\n      &:active {\n        transform: scale(0.95);\n      }\n    }\n  }\n}\n\n.favorites-list {\n  .favorite-item {\n    position: relative;\n    @include glass-effect(0.6);\n    border-radius: 20rpx;\n    margin-bottom: 16rpx;\n    overflow: hidden;\n    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n    transition: all 0.3s ease;\n\n    &.selected {\n      border: 2rpx solid #f78ca0;\n      box-shadow: 0 4rpx 16rpx rgba(247, 140, 160, 0.2);\n    }\n\n    &:active:not(.selected) {\n      transform: scale(0.98);\n    }\n\n    .select-checkbox {\n      position: absolute;\n      top: 16rpx;\n      left: 16rpx;\n      z-index: 10;\n      width: 40rpx;\n      height: 40rpx;\n      background: rgba(255, 255, 255, 0.9);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n    }\n\n    .favorite-time {\n      display: flex;\n      align-items: center;\n      gap: 6rpx;\n      padding: 12rpx 20rpx;\n      background: rgba(0,0,0,0.02);\n      border-top: 1rpx solid rgba(0,0,0,0.05);\n\n      .time-text {\n        font-size: 20rpx;\n        color: $soul-gray-500;\n      }\n    }\n  }\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 40rpx;\n  text-align: center;\n\n  .empty-icon {\n    margin-bottom: 24rpx;\n    opacity: 0.5;\n  }\n\n  .empty-text {\n    font-size: 28rpx;\n    color: $soul-gray-600;\n    margin-bottom: 12rpx;\n    font-weight: 600;\n  }\n\n  .empty-desc {\n    font-size: 24rpx;\n    color: $soul-gray-500;\n    margin-bottom: 32rpx;\n    line-height: 1.4;\n  }\n\n  .empty-action {\n    padding: 16rpx 32rpx;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 24rpx;\n    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);\n\n    .action-text {\n      font-size: 24rpx;\n      color: white;\n      font-weight: 600;\n    }\n  }\n}\n\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60rpx 40rpx;\n  gap: 16rpx;\n\n  .loading-text {\n    font-size: 24rpx;\n    color: $soul-gray-500;\n  }\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.loading-state uni-icons {\n  animation: spin 1s linear infinite;\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./favorites.vue?vue&type=style&index=0&id=c823fa58&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./favorites.vue?vue&type=style&index=0&id=c823fa58&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752120110326\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}