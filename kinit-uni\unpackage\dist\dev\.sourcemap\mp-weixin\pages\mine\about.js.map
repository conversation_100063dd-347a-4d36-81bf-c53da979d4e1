{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/about.vue?5327", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/about.vue?fc87", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/about.vue?4442", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/about.vue?275e", "uni-app:///pages/mine/about.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/about.vue?5c41", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/about.vue?0a3f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "siteName", "onLoad", "methods", "goBack", "uni", "loadSiteConfig", "res", "console", "copyEmail", "success", "title", "icon", "callPhone", "phoneNumber", "showQRCode", "content", "showCancel", "showUserAgreement", "url", "showPrivacyPolicy"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkoB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4ItpB;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACAJ;QACAL;QACAU;UACAL;YACAM;YACAC;UACA;QACA;MACA;IACA;IAEAC;MACAR;QACAS;MACA;IACA;IAEAC;MACAV;QACAM;QACAK;QACAC;MACA;IACA;IAEAC;MACAb;QACAc;MACA;IACA;IAEAC;MACAf;QACAc;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3MA;AAAA;AAAA;AAAA;AAAiuC,CAAgB,8rCAAG,EAAC,C;;;;;;;;;;;ACArvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/about.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/about.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./about.vue?vue&type=template&id=2c1eab8a&scoped=true&\"\nvar renderjs\nimport script from \"./about.vue?vue&type=script&lang=js&\"\nexport * from \"./about.vue?vue&type=script&lang=js&\"\nimport style0 from \"./about.vue?vue&type=style&index=0&id=2c1eab8a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2c1eab8a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/about.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./about.vue?vue&type=template&id=2c1eab8a&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./about.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./about.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"about-page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-content\">\n        <view class=\"navbar-left\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n        </view>\n        <text class=\"navbar-title\">关于我们</text>\n        <view class=\"navbar-right\"></view>\n      </view>\n    </view>\n\n    <scroll-view scroll-y=\"true\" class=\"about-content\">\n      <!-- 应用信息 -->\n      <view class=\"app-info-card\">\n        <view class=\"app-logo\">\n          <uni-icons type=\"ghost\" size=\"80\" color=\"#f78ca0\"></uni-icons>\n        </view>\n        <text class=\"app-name\">{{ siteName }}</text>\n        <text class=\"app-version\">版本 v1.0.0</text>\n        <text class=\"app-desc\">连接志同道合的人，发现生活中的美好搭子</text>\n      </view>\n\n      <!-- 功能介绍 -->\n      <view class=\"feature-section\">\n        <view class=\"section-title\">核心功能</view>\n        <view class=\"feature-list\">\n          <view class=\"feature-item\">\n            <view class=\"feature-icon\">\n              <uni-icons type=\"compose\" size=\"32\" color=\"#f78ca0\"></uni-icons>\n            </view>\n            <view class=\"feature-content\">\n              <text class=\"feature-title\">发布投稿</text>\n              <text class=\"feature-desc\">分享你的兴趣爱好，寻找志同道合的搭子</text>\n            </view>\n          </view>\n\n          <view class=\"feature-item\">\n            <view class=\"feature-icon\">\n              <uni-icons type=\"search\" size=\"32\" color=\"#a6c1ee\"></uni-icons>\n            </view>\n            <view class=\"feature-content\">\n              <text class=\"feature-title\">智能匹配</text>\n              <text class=\"feature-desc\">根据地区、兴趣等条件精准匹配</text>\n            </view>\n          </view>\n\n          <view class=\"feature-item\">\n            <view class=\"feature-icon\">\n              <uni-icons type=\"heart\" size=\"32\" color=\"#feb47b\"></uni-icons>\n            </view>\n            <view class=\"feature-content\">\n              <text class=\"feature-title\">收藏管理</text>\n              <text class=\"feature-desc\">收藏感兴趣的投稿，随时查看联系</text>\n            </view>\n          </view>\n\n          <view class=\"feature-item\">\n            <view class=\"feature-icon\">\n              <uni-icons type=\"locked\" size=\"32\" color=\"#c8a8e9\"></uni-icons>\n            </view>\n            <view class=\"feature-content\">\n              <text class=\"feature-title\">隐私保护</text>\n              <text class=\"feature-desc\">严格保护用户隐私，安全可靠</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 联系方式 -->\n      <view class=\"contact-section\">\n        <view class=\"section-title\">联系我们</view>\n        <view class=\"contact-card\">\n          <view class=\"contact-item\" @click=\"copyEmail\">\n            <view class=\"contact-icon\">\n              <uni-icons type=\"email\" size=\"24\" color=\"#3b82f6\"></uni-icons>\n            </view>\n            <view class=\"contact-content\">\n              <text class=\"contact-title\">邮箱</text>\n              <text class=\"contact-value\"><EMAIL></text>\n            </view>\n            <view class=\"contact-action\">\n              <text class=\"action-text\">复制</text>\n            </view>\n          </view>\n\n          <view class=\"contact-item\" @click=\"callPhone\">\n            <view class=\"contact-icon\">\n              <uni-icons type=\"phone\" size=\"24\" color=\"#10b981\"></uni-icons>\n            </view>\n            <view class=\"contact-content\">\n              <text class=\"contact-title\">客服电话</text>\n              <text class=\"contact-value\">************</text>\n            </view>\n            <view class=\"contact-action\">\n              <text class=\"action-text\">拨打</text>\n            </view>\n          </view>\n\n          <view class=\"contact-item\" @click=\"showQRCode\">\n            <view class=\"contact-icon\">\n              <uni-icons type=\"weixin\" size=\"24\" color=\"#07c160\"></uni-icons>\n            </view>\n            <view class=\"contact-content\">\n              <text class=\"contact-title\">微信客服</text>\n              <text class=\"contact-value\">扫码添加客服</text>\n            </view>\n            <view class=\"contact-action\">\n              <text class=\"action-text\">查看</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 法律信息 -->\n      <view class=\"legal-section\">\n        <view class=\"section-title\">法律信息</view>\n        <view class=\"legal-card\">\n          <view class=\"legal-item\" @click=\"showUserAgreement\">\n            <text class=\"legal-title\">用户协议</text>\n            <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n          </view>\n          <view class=\"legal-item\" @click=\"showPrivacyPolicy\">\n            <text class=\"legal-title\">隐私政策</text>\n            <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n          </view>\n        </view>\n      </view>\n\n      <!-- 版权信息 -->\n      <view class=\"copyright-section\">\n        <text class=\"copyright-text\">© 2024 {{ siteName }}. All rights reserved.</text>\n        <text class=\"copyright-desc\">本应用致力于为用户提供优质的社交体验</text>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      siteName: '搭子星球'\n    }\n  },\n  onLoad() {\n    this.loadSiteConfig()\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n\n    async loadSiteConfig() {\n      try {\n        const res = await this.$store.dispatch('system/getSystemConfig')\n        if (res && res.site_name) {\n          this.siteName = res.site_name\n        }\n      } catch (error) {\n        console.error('加载网站配置失败:', error)\n      }\n    },\n\n    copyEmail() {\n      uni.setClipboardData({\n        data: '<EMAIL>',\n        success: () => {\n          uni.showToast({\n            title: '邮箱已复制',\n            icon: 'success'\n          })\n        }\n      })\n    },\n\n    callPhone() {\n      uni.makePhoneCall({\n        phoneNumber: '************'\n      })\n    },\n\n    showQRCode() {\n      uni.showModal({\n        title: '微信客服',\n        content: '请添加微信号：service123 或扫描小程序码联系客服',\n        showCancel: false\n      })\n    },\n\n    showUserAgreement() {\n      uni.navigateTo({\n        url: '/pages/legal/user-agreement'\n      })\n    },\n\n    showPrivacyPolicy() {\n      uni.navigateTo({\n        url: '/pages/legal/privacy-policy'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.about-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);\n}\n\n.custom-navbar {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  @include glass-effect(0.6);\n  padding-top: var(--status-bar-height);\n\n  .navbar-content {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 24rpx;\n\n    .navbar-left, .navbar-right {\n      width: 60rpx;\n      display: flex;\n      justify-content: center;\n    }\n\n    .navbar-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n  }\n}\n\n.about-content {\n  height: calc(100vh - 120rpx);\n  padding: 20rpx;\n  padding-bottom: 40rpx;\n}\n\n.app-info-card {\n  @include glass-effect(0.7);\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 24rpx;\n  text-align: center;\n  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);\n\n  .app-logo {\n    margin-bottom: 20rpx;\n  }\n\n  .app-name {\n    display: block;\n    font-size: 36rpx;\n    font-weight: 700;\n    color: $soul-gray-800;\n    margin-bottom: 8rpx;\n  }\n\n  .app-version {\n    display: block;\n    font-size: 24rpx;\n    color: $soul-gray-500;\n    margin-bottom: 16rpx;\n  }\n\n  .app-desc {\n    font-size: 26rpx;\n    color: $soul-gray-600;\n    line-height: 1.5;\n  }\n}\n\n.feature-section, .contact-section, .legal-section {\n  margin-bottom: 24rpx;\n\n  .section-title {\n    font-size: 28rpx;\n    font-weight: 600;\n    color: $soul-gray-700;\n    margin-bottom: 16rpx;\n    padding-left: 8rpx;\n  }\n}\n\n.feature-list {\n  .feature-item {\n    @include glass-effect(0.6);\n    border-radius: 16rpx;\n    padding: 24rpx;\n    margin-bottom: 12rpx;\n    display: flex;\n    align-items: center;\n    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n\n    .feature-icon {\n      width: 64rpx;\n      height: 64rpx;\n      background: rgba(255, 255, 255, 0.8);\n      border-radius: 16rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 20rpx;\n    }\n\n    .feature-content {\n      flex: 1;\n\n      .feature-title {\n        display: block;\n        font-size: 26rpx;\n        font-weight: 600;\n        color: $soul-gray-800;\n        margin-bottom: 6rpx;\n      }\n\n      .feature-desc {\n        font-size: 22rpx;\n        color: $soul-gray-600;\n        line-height: 1.4;\n      }\n    }\n  }\n}\n\n.contact-card, .legal-card {\n  @include glass-effect(0.6);\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx;\n  border-bottom: 1rpx solid rgba(0,0,0,0.05);\n  transition: all 0.3s ease;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  &:active {\n    background: rgba(247, 140, 160, 0.05);\n  }\n\n  .contact-icon {\n    width: 48rpx;\n    height: 48rpx;\n    background: rgba(255, 255, 255, 0.8);\n    border-radius: 12rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-right: 16rpx;\n  }\n\n  .contact-content {\n    flex: 1;\n\n    .contact-title {\n      display: block;\n      font-size: 24rpx;\n      color: $soul-gray-600;\n      margin-bottom: 4rpx;\n    }\n\n    .contact-value {\n      font-size: 26rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n  }\n\n  .contact-action {\n    .action-text {\n      font-size: 24rpx;\n      color: #f78ca0;\n      font-weight: 500;\n    }\n  }\n}\n\n.legal-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 24rpx;\n  border-bottom: 1rpx solid rgba(0,0,0,0.05);\n  transition: all 0.3s ease;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  &:active {\n    background: rgba(247, 140, 160, 0.05);\n  }\n\n  .legal-title {\n    font-size: 26rpx;\n    font-weight: 500;\n    color: $soul-gray-800;\n  }\n}\n\n.copyright-section {\n  text-align: center;\n  padding: 40rpx 20rpx;\n\n  .copyright-text {\n    display: block;\n    font-size: 22rpx;\n    color: $soul-gray-500;\n    margin-bottom: 8rpx;\n  }\n\n  .copyright-desc {\n    font-size: 20rpx;\n    color: $soul-gray-400;\n    line-height: 1.4;\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./about.vue?vue&type=style&index=0&id=2c1eab8a&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./about.vue?vue&type=style&index=0&id=2c1eab8a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752119233663\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}