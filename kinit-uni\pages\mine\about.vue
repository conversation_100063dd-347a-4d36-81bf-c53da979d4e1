<template>
  <view class="about-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="left" size="32" color="#FF6B9D"></uni-icons>
        </view>
        <text class="navbar-title">关于我们</text>
        <view class="navbar-right"></view>
      </view>
    </view>

    <scroll-view scroll-y="true" class="about-content">
      <!-- 应用信息 -->
      <view class="app-info-card">
        <view class="app-logo">
          <uni-icons type="ghost" size="80" color="#f78ca0"></uni-icons>
        </view>
        <text class="app-name">{{ siteName }}</text>
        <text class="app-version">版本 v1.0.0</text>
        <text class="app-desc">连接志同道合的人，发现生活中的美好搭子</text>
      </view>

      <!-- 功能介绍 -->
      <view class="feature-section">
        <view class="section-title">核心功能</view>
        <view class="feature-list">
          <view class="feature-item">
            <view class="feature-icon">
              <uni-icons type="compose" size="32" color="#f78ca0"></uni-icons>
            </view>
            <view class="feature-content">
              <text class="feature-title">发布投稿</text>
              <text class="feature-desc">分享你的兴趣爱好，寻找志同道合的搭子</text>
            </view>
          </view>

          <view class="feature-item">
            <view class="feature-icon">
              <uni-icons type="search" size="32" color="#a6c1ee"></uni-icons>
            </view>
            <view class="feature-content">
              <text class="feature-title">智能匹配</text>
              <text class="feature-desc">根据地区、兴趣等条件精准匹配</text>
            </view>
          </view>

          <view class="feature-item">
            <view class="feature-icon">
              <uni-icons type="heart" size="32" color="#feb47b"></uni-icons>
            </view>
            <view class="feature-content">
              <text class="feature-title">收藏管理</text>
              <text class="feature-desc">收藏感兴趣的投稿，随时查看联系</text>
            </view>
          </view>

          <view class="feature-item">
            <view class="feature-icon">
              <uni-icons type="locked" size="32" color="#c8a8e9"></uni-icons>
            </view>
            <view class="feature-content">
              <text class="feature-title">隐私保护</text>
              <text class="feature-desc">严格保护用户隐私，安全可靠</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="contact-section">
        <view class="section-title">联系我们</view>
        <view class="contact-card">
          <view class="contact-item" @click="copyEmail">
            <view class="contact-icon">
              <uni-icons type="email" size="24" color="#3b82f6"></uni-icons>
            </view>
            <view class="contact-content">
              <text class="contact-title">邮箱</text>
              <text class="contact-value"><EMAIL></text>
            </view>
            <view class="contact-action">
              <text class="action-text">复制</text>
            </view>
          </view>

          <view class="contact-item" @click="callPhone">
            <view class="contact-icon">
              <uni-icons type="phone" size="24" color="#10b981"></uni-icons>
            </view>
            <view class="contact-content">
              <text class="contact-title">客服电话</text>
              <text class="contact-value">************</text>
            </view>
            <view class="contact-action">
              <text class="action-text">拨打</text>
            </view>
          </view>

          <view class="contact-item" @click="showQRCode">
            <view class="contact-icon">
              <uni-icons type="weixin" size="24" color="#07c160"></uni-icons>
            </view>
            <view class="contact-content">
              <text class="contact-title">微信客服</text>
              <text class="contact-value">扫码添加客服</text>
            </view>
            <view class="contact-action">
              <text class="action-text">查看</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 法律信息 -->
      <view class="legal-section">
        <view class="section-title">法律信息</view>
        <view class="legal-card">
          <view class="legal-item" @click="showUserAgreement">
            <text class="legal-title">用户协议</text>
            <uni-icons type="right" size="16" color="#999"></uni-icons>
          </view>
          <view class="legal-item" @click="showPrivacyPolicy">
            <text class="legal-title">隐私政策</text>
            <uni-icons type="right" size="16" color="#999"></uni-icons>
          </view>
        </view>
      </view>

      <!-- 版权信息 -->
      <view class="copyright-section">
        <text class="copyright-text">© 2024 {{ siteName }}. All rights reserved.</text>
        <text class="copyright-desc">本应用致力于为用户提供优质的社交体验</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      siteName: '搭子星球'
    }
  },
  onLoad() {
    this.loadSiteConfig()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadSiteConfig() {
      try {
        const res = await this.$store.dispatch('system/getSystemConfig')
        if (res && res.site_name) {
          this.siteName = res.site_name
        }
      } catch (error) {
        console.error('加载网站配置失败:', error)
      }
    },

    copyEmail() {
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱已复制',
            icon: 'success'
          })
        }
      })
    },

    callPhone() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },

    showQRCode() {
      uni.showModal({
        title: '微信客服',
        content: '请添加微信号：service123 或扫描小程序码联系客服',
        showCancel: false
      })
    },

    showUserAgreement() {
      uni.navigateTo({
        url: '/pages/legal/user-agreement'
      })
    },

    showPrivacyPolicy() {
      uni.navigateTo({
        url: '/pages/legal/privacy-policy'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.about-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;

    .navbar-left, .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: center;
    }

    .navbar-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }
  }
}

.about-content {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.app-info-card {
  @include glass-effect(0.7);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);

  .app-logo {
    margin-bottom: 20rpx;
  }

  .app-name {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: $soul-gray-800;
    margin-bottom: 8rpx;
  }

  .app-version {
    display: block;
    font-size: 24rpx;
    color: $soul-gray-500;
    margin-bottom: 16rpx;
  }

  .app-desc {
    font-size: 26rpx;
    color: $soul-gray-600;
    line-height: 1.5;
  }
}

.feature-section, .contact-section, .legal-section {
  margin-bottom: 24rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: $soul-gray-700;
    margin-bottom: 16rpx;
    padding-left: 8rpx;
  }
}

.feature-list {
  .feature-item {
    @include glass-effect(0.6);
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 12rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);

    .feature-icon {
      width: 64rpx;
      height: 64rpx;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
    }

    .feature-content {
      flex: 1;

      .feature-title {
        display: block;
        font-size: 26rpx;
        font-weight: 600;
        color: $soul-gray-800;
        margin-bottom: 6rpx;
      }

      .feature-desc {
        font-size: 22rpx;
        color: $soul-gray-600;
        line-height: 1.4;
      }
    }
  }
}

.contact-card, .legal-card {
  @include glass-effect(0.6);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: rgba(247, 140, 160, 0.05);
  }

  .contact-icon {
    width: 48rpx;
    height: 48rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
  }

  .contact-content {
    flex: 1;

    .contact-title {
      display: block;
      font-size: 24rpx;
      color: $soul-gray-600;
      margin-bottom: 4rpx;
    }

    .contact-value {
      font-size: 26rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }
  }

  .contact-action {
    .action-text {
      font-size: 24rpx;
      color: #f78ca0;
      font-weight: 500;
    }
  }
}

.legal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: rgba(247, 140, 160, 0.05);
  }

  .legal-title {
    font-size: 26rpx;
    font-weight: 500;
    color: $soul-gray-800;
  }
}

.copyright-section {
  text-align: center;
  padding: 40rpx 20rpx;

  .copyright-text {
    display: block;
    font-size: 22rpx;
    color: $soul-gray-500;
    margin-bottom: 8rpx;
  }

  .copyright-desc {
    font-size: 20rpx;
    color: $soul-gray-400;
    line-height: 1.4;
  }
}
</style>
