@charset "UTF-8";
/* uView的全局SCSS主题文件 */
/**
 * Soul风格主题变量
 */
/* Soul风格颜色变量 */
/* 主色调 - 温柔的粉色系 */
/* 辅助色 */
/* 中性色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更圆润的设计 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* Soul风格特有样式 */
/* 毛玻璃效果 */
/* 渐变背景 */
/* 阴影效果 */
/* 卡片样式 */
/* 按钮样式 */
.submissions-page.data-v-608f4c38 {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}
.custom-navbar.data-v-608f4c38 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  padding-top: 25px;
}
.custom-navbar .navbar-content.data-v-608f4c38 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
}
.custom-navbar .navbar-content .navbar-left.data-v-608f4c38, .custom-navbar .navbar-content .navbar-right.data-v-608f4c38 {
  width: 60rpx;
  display: flex;
  justify-content: center;
}
.custom-navbar .navbar-content .navbar-title.data-v-608f4c38 {
  font-size: 28rpx;
  font-weight: 600;
  color: #424242;
}
.custom-navbar .navbar-content .action-btn.data-v-608f4c38 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}
.custom-navbar .navbar-content .action-btn.data-v-608f4c38:active {
  background: rgba(255, 255, 255, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.filter-tabs.data-v-608f4c38 {
  display: flex;
  padding: 16rpx 20rpx;
  gap: 8rpx;
}
.filter-tabs .filter-tab.data-v-608f4c38 {
  position: relative;
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 30rpx;
  transition: all 0.3s ease;
}
.filter-tabs .filter-tab.active.data-v-608f4c38 {
  background: #f78ca0;
}
.filter-tabs .filter-tab.active .tab-text.data-v-608f4c38 {
  color: white;
  font-weight: 600;
}
.filter-tabs .filter-tab.active .tab-badge.data-v-608f4c38 {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}
.filter-tabs .filter-tab .tab-text.data-v-608f4c38 {
  font-size: 24rpx;
  color: #616161;
  transition: all 0.3s ease;
}
.filter-tabs .filter-tab .tab-badge.data-v-608f4c38 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: #f78ca0;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  color: white;
  font-weight: 600;
  padding: 0 8rpx;
}
.submissions-content.data-v-608f4c38 {
  height: calc(100vh - 200rpx);
  padding: 0 20rpx 40rpx;
}
.submissions-list .submission-item.data-v-608f4c38 {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.submissions-list .submission-item.data-v-608f4c38:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.submissions-list .submission-item .item-cover.data-v-608f4c38 {
  position: relative;
  height: 200rpx;
}
.submissions-list .submission-item .item-cover .cover-image.data-v-608f4c38 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.submissions-list .submission-item .item-cover .status-badge.data-v-608f4c38 {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  color: white;
}
.submissions-list .submission-item .item-cover .status-badge.pending.data-v-608f4c38 {
  background: #f59e0b;
}
.submissions-list .submission-item .item-cover .status-badge.approved.data-v-608f4c38 {
  background: #10b981;
}
.submissions-list .submission-item .item-cover .status-badge.rejected.data-v-608f4c38 {
  background: #ef4444;
}
.submissions-list .submission-item .item-info.data-v-608f4c38 {
  padding: 20rpx;
}
.submissions-list .submission-item .item-info .info-header.data-v-608f4c38 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.submissions-list .submission-item .item-info .info-header .submission-title.data-v-608f4c38 {
  font-size: 26rpx;
  font-weight: 600;
  color: #424242;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.submissions-list .submission-item .item-info .info-header .submission-time.data-v-608f4c38 {
  font-size: 20rpx;
  color: #9E9E9E;
  margin-left: 16rpx;
}
.submissions-list .submission-item .item-info .info-content.data-v-608f4c38 {
  margin-bottom: 16rpx;
}
.submissions-list .submission-item .item-info .info-content .info-row.data-v-608f4c38 {
  display: flex;
  gap: 24rpx;
  margin-bottom: 8rpx;
}
.submissions-list .submission-item .item-info .info-content .info-row.data-v-608f4c38:last-child {
  margin-bottom: 0;
}
.submissions-list .submission-item .item-info .info-content .info-row .info-item.data-v-608f4c38 {
  display: flex;
  align-items: center;
  gap: 6rpx;
  flex: 1;
}
.submissions-list .submission-item .item-info .info-content .info-row .info-item .info-text.data-v-608f4c38 {
  font-size: 22rpx;
  color: #757575;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.submissions-list .submission-item .item-info .item-actions.data-v-608f4c38 {
  display: flex;
  gap: 12rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}
.submissions-list .submission-item .item-info .item-actions .action-btn.data-v-608f4c38 {
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.submissions-list .submission-item .item-info .item-actions .action-btn.edit.data-v-608f4c38 {
  background: rgba(247, 140, 160, 0.1);
  border: 1rpx solid rgba(247, 140, 160, 0.3);
}
.submissions-list .submission-item .item-info .item-actions .action-btn.edit .action-text.data-v-608f4c38 {
  color: #f78ca0;
}
.submissions-list .submission-item .item-info .item-actions .action-btn.delete.data-v-608f4c38 {
  background: rgba(239, 68, 68, 0.1);
  border: 1rpx solid rgba(239, 68, 68, 0.3);
}
.submissions-list .submission-item .item-info .item-actions .action-btn.delete .action-text.data-v-608f4c38 {
  color: #ef4444;
}
.submissions-list .submission-item .item-info .item-actions .action-btn .action-text.data-v-608f4c38 {
  font-size: 22rpx;
  font-weight: 500;
}
.submissions-list .submission-item .item-info .item-actions .action-btn.data-v-608f4c38:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.empty-state.data-v-608f4c38 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}
.empty-state .empty-icon.data-v-608f4c38 {
  margin-bottom: 24rpx;
  opacity: 0.5;
}
.empty-state .empty-text.data-v-608f4c38 {
  font-size: 26rpx;
  color: #9E9E9E;
  margin-bottom: 32rpx;
  line-height: 1.4;
}
.empty-state .empty-action.data-v-608f4c38 {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}
.empty-state .empty-action .action-text.data-v-608f4c38 {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}
