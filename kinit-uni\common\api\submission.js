import request from '@/common/request/index.js'
import { mockSubmissions, mockFilterOptions } from '@/common/mock/submission.js'

// 开发模式下使用模拟数据
const isDev = true // 临时使用模拟数据进行测试

/**
 * 投稿相关API
 */
export default {
  /**
   * 获取投稿列表
   * @param {Object} params 查询参数
   */
  getSubmissionList(params = {}) {
    if (isDev) {
      // 模拟API响应
      return new Promise((resolve) => {
        setTimeout(() => {
          const { page = 1, size = 20 } = params
          const start = (page - 1) * size
          const end = start + size
          const items = mockSubmissions.slice(start, end)

          resolve({
            code: 200,
            data: {
              items,
              total: mockSubmissions.length,
              page,
              size,
              pages: Math.ceil(mockSubmissions.length / size)
            }
          })
        }, 500) // 模拟网络延迟
      })
    }
    return request.get('/submission/list', params)
  },

  /**
   * 获取投稿详情
   * @param {Number} id 投稿ID
   */
  getSubmissionDetail(id) {
    if (isDev) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const submission = mockSubmissions.find(item => item.id === parseInt(id))
          if (submission) {
            resolve({
              code: 200,
              data: submission
            })
          } else {
            reject({
              code: 404,
              message: '投稿不存在'
            })
          }
        }, 300)
      })
    }
    return request.get(`/submission/${id}`)
  },

  /**
   * 获取筛选选项配置
   */
  getFilterOptions() {
    if (isDev) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            data: mockFilterOptions
          })
        }, 200)
      })
    }
    return request.get('/submission/config/options')
  },

  /**
   * 创建投稿（管理员功能）
   * @param {Object} data 投稿数据
   */
  createSubmission(data) {
    return request.post('/submission', data)
  },

  /**
   * 更新投稿（管理员功能）
   * @param {Number} id 投稿ID
   * @param {Object} data 更新数据
   */
  updateSubmission(id, data) {
    return request.put(`/submission/${id}`, data)
  },

  /**
   * 删除投稿（管理员功能）
   * @param {Number} id 投稿ID
   */
  deleteSubmission(id) {
    return request.delete(`/submission/${id}`)
  },

  /**
   * 设置投稿置顶状态（管理员功能）
   * @param {Number} id 投稿ID
   * @param {Boolean} isTop 是否置顶
   */
  setSubmissionTop(id, isTop) {
    return request.patch(`/submission/${id}/top`, null, { is_top: isTop })
  },

  /**
   * 解析投稿文本（管理员功能）
   * @param {String} text 投稿文本
   */
  parseSubmissionText(text) {
    return request.post('/submission/parse-text', { text })
  },

  /**
   * 从文本创建投稿（管理员功能）
   * @param {Object} data 投稿数据
   */
  createFromText(data) {
    return request.post('/submission/create-from-text', data, {
      'Content-Type': 'application/x-www-form-urlencoded'
    })
  },

  /**
   * 上传投稿图片（管理员功能）
   * @param {Array} files 图片文件数组
   */
  uploadImages(files) {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })
    return request.post('/submission/upload-images', formData, {
      'Content-Type': 'multipart/form-data'
    })
  },

  /**
   * 设置封面图片（管理员功能）
   * @param {Number} submissionId 投稿ID
   * @param {Number} coverIndex 封面图片索引
   */
  setCoverImage(submissionId, coverIndex) {
    return request.post('/submission/set-cover-image', {
      submission_id: submissionId,
      cover_index: coverIndex
    }, {
      'Content-Type': 'application/x-www-form-urlencoded'
    })
  },

  /**
   * 获取筛选选项配置（支持省份参数）
   * @param {String} province 省份名称
   */
  getFilterOptionsWithProvince(province = null) {
    const params = province ? { province } : {}
    return request.get('/submission/config/options', params)
  },

  /**
   * 收藏/取消收藏投稿
   * @param {Number} submissionId 投稿ID
   */
  toggleFavorite(submissionId) {
    return request.post('/submission/toggle-favorite', { submission_id: submissionId })
  },

  /**
   * 检查投稿是否已收藏
   * @param {Number} submissionId 投稿ID
   */
  checkFavoriteStatus(submissionId) {
    return request.get(`/submission/${submissionId}/favorite-status`)
  },

  /**
   * 获取收藏列表
   * @param {Object} params 查询参数
   */
  getFavoriteList(params = {}) {
    return request.get('/submission/favorites', params)
  }
}
