{"version": 3, "sources": ["webpack:///E:/kaifa/投稿/kinit2/kinit-uni/components/soul-submission-card/soul-submission-card.vue?d73a", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/components/soul-submission-card/soul-submission-card.vue?0d93", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/components/soul-submission-card/soul-submission-card.vue?37e2", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/components/soul-submission-card/soul-submission-card.vue?ce8a", "uni-app:///components/soul-submission-card/soul-submission-card.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/components/soul-submission-card/soul-submission-card.vue?5462", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/components/soul-submission-card/soul-submission-card.vue?4d4d"], "names": ["name", "props", "submission", "type", "required", "data", "imageError", "computed", "genderClass", "genderIcon", "genderColor", "methods", "handleClick", "handleImageError", "console", "formatLocation"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAipB,CAAgB,sqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoErqB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;IACA;IACA;IACAC;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrJA;AAAA;AAAA;AAAA;AAAgvC,CAAgB,6sCAAG,EAAC,C;;;;;;;;;;;ACApwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/soul-submission-card/soul-submission-card.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./soul-submission-card.vue?vue&type=template&id=91d46e38&scoped=true&\"\nvar renderjs\nimport script from \"./soul-submission-card.vue?vue&type=script&lang=js&\"\nexport * from \"./soul-submission-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./soul-submission-card.vue?vue&type=style&index=0&id=91d46e38&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"91d46e38\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/soul-submission-card/soul-submission-card.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./soul-submission-card.vue?vue&type=template&id=91d46e38&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatLocation(_vm.submission)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./soul-submission-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./soul-submission-card.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"submission-card\" @click=\"handleClick\">\n    <!-- 置顶标签 -->\n    <view v-if=\"submission.is_top\" class=\"top-badge\">\n      <text class=\"top-text\">置顶</text>\n    </view>\n    \n    <!-- 封面图片 -->\n    <view class=\"cover-container\">\n      <image\n        v-if=\"submission.cover_image && !imageError\"\n        :src=\"submission.cover_image\"\n        class=\"cover-image\"\n        mode=\"aspectFill\"\n        lazy-load\n        :fade-show=\"true\"\n        @error=\"handleImageError\"\n      />\n      <view v-else class=\"placeholder-image\">\n        <text class=\"placeholder-text\">暂无图片</text>\n      </view>\n\n    </view>\n    \n    <!-- 信息内容 -->\n    <view class=\"info-container\">\n      <view class=\"info-row\">\n        <!-- 所在城市 -->\n        <view class=\"info-item\">\n          <uni-icons type=\"location\" size=\"16\" color=\"#f78ca0\"></uni-icons>\n          <text class=\"info-text\">{{ formatLocation(submission) }}</text>\n        </view>\n\n        <!-- 年龄 -->\n        <view class=\"info-item\">\n          <uni-icons type=\"calendar\" size=\"16\" color=\"#a6c1ee\"></uni-icons>\n          <text class=\"info-text\">{{ submission.age }}岁</text>\n        </view>\n\n        <!-- 性别图标（不显示文字） -->\n        <view class=\"info-item gender-icon-only\">\n          <uni-icons :type=\"genderIcon\" size=\"20\" :color=\"genderColor\"></uni-icons>\n        </view>\n      </view>\n\n      <view class=\"info-row\">\n        <!-- 工作信息 -->\n        <view class=\"info-item\">\n          <uni-icons type=\"gear\" size=\"16\" color=\"#feb47b\"></uni-icons>\n          <text class=\"info-text\">{{ submission.occupation || '未知' }}</text>\n        </view>\n\n        <!-- 是否接受异地 -->\n        <view class=\"info-item\">\n          <uni-icons :type=\"submission.accept_long_distance ? 'closeempty' : 'checkmarkempty'\" size=\"16\" :color=\"submission.accept_long_distance ? '#ef4444' : '#10b981'\"></uni-icons>\n          <text class=\"info-text\" :style=\"{ color: submission.accept_long_distance ? '#ef4444' : '#10b981' }\">\n            {{ submission.accept_long_distance ? '异地' : '同城' }}\n          </text>\n        </view>\n\n        <!-- 空占位，保持布局一致 -->\n        <view class=\"info-item empty-item\"></view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'SoulSubmissionCard',\n  props: {\n    submission: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      imageError: false\n    }\n  },\n  computed: {\n    genderClass() {\n      return {\n        'gender-male': this.submission.gender === '男',\n        'gender-female': this.submission.gender === '女',\n        'gender-other': this.submission.gender === '不限'\n      }\n    },\n    genderIcon() {\n      switch (this.submission.gender) {\n        case '男':\n          return 'person'\n        case '女':\n          return 'person-filled'\n        default:\n          return 'person'\n      }\n    },\n    genderColor() {\n      switch (this.submission.gender) {\n        case '男':\n          return '#87ceeb'\n        case '女':\n          return '#f78ca0'\n        default:\n          return '#c8a8e9'\n      }\n    }\n  },\n  methods: {\n    handleClick() {\n      this.$emit('click', this.submission)\n    },\n    handleImageError() {\n      // 图片加载失败处理\n      this.imageError = true\n      console.log('图片加载失败')\n    },\n    // 格式化地理位置显示\n    formatLocation(item) {\n      if (!item) return ''\n\n      // 直辖市列表\n      const municipalities = ['北京', '上海', '天津', '重庆']\n\n      // 如果有省份和城市信息\n      if (item.province && item.city) {\n        // 如果是直辖市，只显示城市名\n        if (municipalities.includes(item.province)) {\n          return item.city\n        }\n        // 其他情况显示省份-城市\n        return `${item.province}-${item.city}`\n      }\n\n      // 只有城市信息\n      if (item.city) {\n        return item.city\n      }\n\n      // 只有省份信息\n      if (item.province) {\n        return item.province\n      }\n\n      return '未知'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.submission-card {\n  position: relative;\n  width: 100%;\n  height: 400rpx; // 固定总高度\n  @include glass-effect(0.7);\n  border-radius: 40rpx;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);\n  display: flex;\n  flex-direction: column;\n\n  &:hover {\n    transform: translateY(-8rpx) scale(1.02);\n    box-shadow: 0 24rpx 80rpx 0 rgba(100, 100, 135, 0.15);\n  }\n\n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n.top-badge {\n  position: absolute;\n  top: 12rpx;\n  right: 12rpx;\n  z-index: 10;\n  background: linear-gradient(135deg, $soul-accent 0%, #FFE066 100%);\n  border-radius: 16rpx;\n  padding: 6rpx 12rpx; // 缩小内边距\n  @include soul-shadow(1);\n\n  .top-text {\n    font-size: 18rpx; // 缩小字体\n    color: $soul-white;\n    font-weight: 600;\n  }\n}\n\n.cover-container {\n  position: relative;\n  width: 100%;\n  height: 300rpx; // 75% of 400rpx\n  overflow: hidden;\n  background-color: #f0f0f0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-shrink: 0; // 防止被压缩\n\n  .cover-image {\n    width: 100%;\n    height: 100%;\n    object-fit: cover; // 改为cover以填满容器\n    transition: transform 0.4s ease-in-out;\n  }\n\n  .placeholder-image {\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n\n  .placeholder-text {\n    color: #999;\n    font-size: 24rpx;\n  }\n}\n\n.info-container {\n  height: 100rpx; // 25% of 400rpx\n  padding: 8rpx 12rpx;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  flex-shrink: 0; // 防止被压缩\n}\n\n.info-row {\n  display: flex;\n  align-items: center;\n  height: 40rpx;\n\n  .info-item {\n    display: flex;\n    align-items: center;\n    gap: 2rpx;\n    justify-content: flex-start; // 左对齐\n    min-width: 0;\n\n    // 第一列占45%\n    &:nth-child(1) {\n      flex: 0 0 45%; // 固定占45%宽度\n    }\n\n    // 第二列占35%\n    &:nth-child(2) {\n      flex: 0 0 35%; // 固定占35%宽度\n    }\n\n    // 第三列占20%\n    &:nth-child(3) {\n      flex: 0 0 20%; // 固定占20%宽度\n    }\n\n    .info-text {\n      font-size: 20rpx;\n      color: $soul-gray-700;\n      font-weight: 500;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      flex: 1;\n      min-width: 0;\n      text-align: left; // 文字左对齐\n    }\n\n    // 性别图标特殊样式\n    &.gender-icon-only {\n      justify-content: flex-start; // 性别图标也左对齐\n    }\n\n    // 空占位项样式\n    &.empty-item {\n      visibility: hidden; // 隐藏但保持布局空间\n    }\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./soul-submission-card.vue?vue&type=style&index=0&id=91d46e38&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./soul-submission-card.vue?vue&type=style&index=0&id=91d46e38&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752118672175\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}