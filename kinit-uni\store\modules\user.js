import userApi from '@/common/api/user.js'

const state = {
  userInfo: null,
  token: null,
  isLoggedIn: false
}

const mutations = {
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    state.isLoggedIn = !!userInfo
    // 同步到本地存储
    if (userInfo) {
      uni.setStorageSync('userInfo', userInfo)
    } else {
      uni.removeStorageSync('userInfo')
    }
  },

  SET_TOKEN(state, token) {
    state.token = token
    // 同步到本地存储
    if (token) {
      uni.setStorageSync('token', token)
    } else {
      uni.removeStorageSync('token')
    }
  },

  CLEAR_USER_DATA(state) {
    state.userInfo = null
    state.token = null
    state.isLoggedIn = false
    // 清除本地存储
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('token')
  }
}

const actions = {
  // 初始化用户状态（从本地存储恢复）
  initUserState({ commit }) {
    const userInfo = uni.getStorageSync('userInfo')
    const token = uni.getStorageSync('token')
    
    if (userInfo) {
      commit('SET_USER_INFO', userInfo)
    }
    if (token) {
      commit('SET_TOKEN', token)
    }
  },

  // 微信登录
  async wxLogin({ commit }, { code, userInfo }) {
    try {
      const res = await userApi.wxLogin({
        code,
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        gender: userInfo.gender
      })

      if (res.code === 200) {
        commit('SET_TOKEN', res.data.token)
        commit('SET_USER_INFO', res.data.user)
        return res.data
      } else {
        throw new Error(res.message || '登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      throw error
    }
  },

  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      const res = await userApi.getUserInfo()
      if (res.code === 200) {
        commit('SET_USER_INFO', res.data)
        return res.data
      } else {
        throw new Error(res.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },

  // 更新用户信息
  async updateUserInfo({ commit, state }, data) {
    try {
      const res = await userApi.updateUserInfo(data)
      if (res.code === 200) {
        const updatedUserInfo = { ...state.userInfo, ...data }
        commit('SET_USER_INFO', updatedUserInfo)
        return updatedUserInfo
      } else {
        throw new Error(res.message || '更新用户信息失败')
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  },

  // 注销登录
  async logout({ commit }) {
    try {
      await userApi.logout()
    } catch (error) {
      console.error('注销登录失败:', error)
    } finally {
      commit('CLEAR_USER_DATA')
    }
  }
}

const getters = {
  isLoggedIn: state => state.isLoggedIn,
  userInfo: state => state.userInfo,
  token: state => state.token,
  userId: state => state.userInfo?.id,
  userNickname: state => state.userInfo?.nickname || '未设置昵称',
  userAvatar: state => state.userInfo?.avatar || '/static/images/default-avatar.png'
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
