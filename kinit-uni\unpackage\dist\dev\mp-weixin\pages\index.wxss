@charset "UTF-8";
/* uView的全局SCSS主题文件 */
/**
 * Soul风格主题变量
 */
/* Soul风格颜色变量 */
/* 主色调 - 温柔的粉色系 */
/* 辅助色 */
/* 中性色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更圆润的设计 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* Soul风格特有样式 */
/* 毛玻璃效果 */
/* 渐变背景 */
/* 阴影效果 */
/* 卡片样式 */
/* 按钮样式 */
.page-container.data-v-2a183b29 {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
  padding-bottom: 80rpx;
}
.custom-navbar.data-v-2a183b29 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  padding-top: 25px;
}
.custom-navbar .navbar-content.data-v-2a183b29 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 40rpx;
}
.custom-navbar .navbar-content .navbar-left.data-v-2a183b29 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.custom-navbar .navbar-content .navbar-title.data-v-2a183b29 {
  font-size: 48rpx;
  font-weight: 800;
  background: linear-gradient(45deg, #f78ca0, #f9748f, #fe9a8b, #ffc8a9);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}
.custom-navbar .navbar-content .navbar-actions.data-v-2a183b29 {
  display: flex;
  align-items: center;
}
.category-section.data-v-2a183b29 {
  padding: 24rpx 20rpx;
}
.category-section .section-title.data-v-2a183b29 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  gap: 12rpx;
}
.category-section .section-title .title-text.data-v-2a183b29 {
  font-size: 28rpx;
  font-weight: 700;
  color: #616161;
}
.category-section .category-scroll .category-buttons-container.data-v-2a183b29 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 0 12rpx;
}
.category-section .category-scroll .category-buttons-container .category-row.data-v-2a183b29 {
  display: flex;
  gap: 12rpx;
  overflow-x: auto;
  white-space: nowrap;
}
.category-section .category-scroll .category-buttons-container .category-row .category-btn.data-v-2a183b29 {
  background: rgba(255, 255, 255, 0.5);
  -webkit-backdrop-filter: blur(10rpx) saturate(150%);
          backdrop-filter: blur(10rpx) saturate(150%);
  border: 1rpx solid rgba(209, 213, 219, 0.2);
  border-radius: 12rpx;
  padding: 8rpx 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}
.category-section .category-scroll .category-buttons-container .category-row .category-btn.data-v-2a183b29:hover {
  background: rgba(255, 255, 255, 0.8);
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.category-section .category-scroll .category-buttons-container .category-row .category-btn.active.data-v-2a183b29 {
  background: linear-gradient(to right, #fbc2eb, #a6c1ee);
  color: white;
  box-shadow: 0 8rpx 30rpx rgba(172, 191, 233, 0.4);
}
.category-section .category-scroll .category-buttons-container .category-row .category-btn.active .category-text.data-v-2a183b29 {
  color: white;
  font-weight: 700;
}
.category-section .category-scroll .category-buttons-container .category-row .category-btn .category-text.data-v-2a183b29 {
  font-size: 22rpx;
  font-weight: 600;
  color: #4b5563;
  white-space: nowrap;
}
.posts-section .section-title.data-v-2a183b29 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}
.posts-section .section-title .title-left.data-v-2a183b29 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.posts-section .section-title .title-left .title-text.data-v-2a183b29 {
  font-size: 28rpx;
  font-weight: 700;
  color: #616161;
}
.posts-section .section-title .filter-btn-container.data-v-2a183b29 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid #f78ca0;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  transition: all 0.3s ease;
}
.posts-section .section-title .filter-btn-container.data-v-2a183b29:hover {
  background: rgba(247, 140, 160, 0.1);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.posts-section .section-title .filter-btn-container .filter-btn-text.data-v-2a183b29 {
  font-size: 22rpx;
  color: #f78ca0;
  font-weight: 600;
}
.posts-section .advanced-filter-section.data-v-2a183b29 {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  margin: 0 20rpx 20rpx;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}
.posts-section .advanced-filter-section .filter-grid.data-v-2a183b29 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.posts-section .advanced-filter-section .filter-item .filter-label.data-v-2a183b29 {
  display: block;
  font-weight: 600;
  color: #718096;
  margin-bottom: 12rpx;
  font-size: 24rpx;
}
.posts-section .advanced-filter-section .filter-item .filter-input.data-v-2a183b29 {
  width: 100%;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(209, 213, 219, 0.5);
  background-color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  min-height: 80rpx;
  box-sizing: border-box;
  transition: all 0.2s ease;
}
.posts-section .advanced-filter-section .filter-item .filter-input.data-v-2a183b29:focus {
  border-color: #a6c1ee;
  box-shadow: 0 0 0 4rpx rgba(166, 193, 238, 0.3);
}
.posts-section .advanced-filter-section .filter-item .gender-options.data-v-2a183b29 {
  display: flex;
  gap: 16rpx;
}
.posts-section .advanced-filter-section .filter-item .gender-options .gender-option.data-v-2a183b29 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(209, 213, 219, 0.5);
  background-color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
  cursor: pointer;
}
.posts-section .advanced-filter-section .filter-item .gender-options .gender-option.active.data-v-2a183b29 {
  border-color: #f78ca0;
  background-color: rgba(247, 140, 160, 0.1);
  box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.2);
}
.posts-section .advanced-filter-section .filter-item .gender-options .gender-option .gender-text.data-v-2a183b29 {
  font-size: 24rpx;
  color: #757575;
  font-weight: 500;
}
.posts-section .advanced-filter-section .filter-item .gender-options .gender-option .gender-text.active.data-v-2a183b29 {
  color: #f78ca0;
  font-weight: 600;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-display.data-v-2a183b29 {
  text-align: center;
  margin-bottom: 24rpx;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-display .age-text.data-v-2a183b29 {
  font-size: 32rpx;
  color: #616161;
  font-weight: 600;
  background: linear-gradient(135deg, #f78ca0, #a6c1ee);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-quick-select.data-v-2a183b29 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-quick-select .age-option.data-v-2a183b29 {
  flex: 1;
  min-width: 120rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid #e5e7eb;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-quick-select .age-option.active.data-v-2a183b29 {
  background: linear-gradient(135deg, #f78ca0, #a6c1ee);
  border-color: transparent;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(247, 140, 160, 0.3);
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-quick-select .age-option.active .age-option-text.data-v-2a183b29 {
  color: #fff;
  font-weight: 600;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-quick-select .age-option .age-option-text.data-v-2a183b29 {
  font-size: 24rpx;
  color: #616161;
  font-weight: 500;
  transition: all 0.3s ease;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .custom-age-range.data-v-2a183b29 {
  margin-top: 24rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual.data-v-2a183b29 {
  margin: 32rpx 0;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual .age-line.data-v-2a183b29 {
  position: relative;
  height: 8rpx;
  margin: 40rpx 0 32rpx;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual .age-line .age-line-bg.data-v-2a183b29 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual .age-line .age-line-active.data-v-2a183b29 {
  position: absolute;
  top: 0;
  height: 8rpx;
  background: linear-gradient(135deg, #f78ca0, #a6c1ee);
  border-radius: 4rpx;
  transition: all 0.3s ease;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual .age-line .age-thumb.data-v-2a183b29 {
  position: absolute;
  top: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 3rpx solid #f78ca0;
  border-radius: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual .age-line .age-thumb.age-thumb-max.data-v-2a183b29 {
  border-color: #a6c1ee;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual .age-line .age-thumb .age-thumb-text.data-v-2a183b29 {
  position: absolute;
  top: -36rpx;
  font-size: 20rpx;
  color: #616161;
  font-weight: 600;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual .age-scale.data-v-2a183b29 {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .age-range-visual .age-scale .scale-text.data-v-2a183b29 {
  font-size: 20rpx;
  color: #9E9E9E;
  font-weight: 500;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .slider-container.data-v-2a183b29 {
  margin: 20rpx 0;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .slider-container .slider-label.data-v-2a183b29 {
  display: block;
  font-size: 22rpx;
  color: #757575;
  margin-bottom: 12rpx;
  font-weight: 500;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .slider-container .age-slider.data-v-2a183b29 {
  width: 100%;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .slider-container .no-limit-option.data-v-2a183b29 {
  margin-top: 16rpx;
  text-align: center;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .slider-container .no-limit-option .no-limit-text.data-v-2a183b29 {
  font-size: 24rpx;
  color: #757575;
  padding: 12rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}
.posts-section .advanced-filter-section .filter-item .age-range-container .slider-container .no-limit-option .no-limit-text.active.data-v-2a183b29 {
  background: linear-gradient(135deg, #f78ca0, #a6c1ee);
  border-color: transparent;
  color: #fff;
  font-weight: 600;
}
.posts-section .advanced-filter-section .filter-actions.data-v-2a183b29 {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  margin-top: 12rpx;
}
.posts-section .advanced-filter-section .filter-actions .filter-btn.data-v-2a183b29 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-weight: 600;
  font-size: 22rpx;
  transition: all 0.2s ease;
}
.posts-section .advanced-filter-section .filter-actions .filter-btn.reset.data-v-2a183b29 {
  background: linear-gradient(to right, #868f96 0%, #596164 100%);
  color: white;
}
.posts-section .advanced-filter-section .filter-actions .filter-btn.apply.data-v-2a183b29 {
  background: linear-gradient(to right, #fbc2eb, #a6c1ee);
  color: white;
}
.posts-section .advanced-filter-section .filter-actions .filter-btn.data-v-2a183b29:hover {
  opacity: 0.85;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.submission-list.data-v-2a183b29 {
  flex: 1;
  height: calc(100vh - 500rpx);
}
.list-container.data-v-2a183b29 {
  padding: 0 20rpx 140rpx 20rpx;
}
.submission-grid.data-v-2a183b29 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.loading-container.data-v-2a183b29 {
  grid-column: 1/-1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  color: #f78ca0;
}
.loading-container .loading-icon.data-v-2a183b29 {
  -webkit-animation: spin-data-v-2a183b29 2s linear infinite;
          animation: spin-data-v-2a183b29 2s linear infinite;
  margin-bottom: 24rpx;
}
.loading-container .loading-text.data-v-2a183b29 {
  font-size: 28rpx;
  font-weight: 600;
}
.empty-container.data-v-2a183b29 {
  grid-column: 1/-1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}
.empty-container .empty-icon.data-v-2a183b29 {
  margin-bottom: 24rpx;
}
.empty-container .empty-title.data-v-2a183b29 {
  font-size: 32rpx;
  color: #616161;
  font-weight: 600;
  margin-bottom: 12rpx;
}
.empty-container .empty-subtitle.data-v-2a183b29 {
  font-size: 26rpx;
  color: #9E9E9E;
  margin-bottom: 40rpx;
  line-height: 1.5;
}
.empty-container .search-suggestions.data-v-2a183b29 {
  width: 100%;
  margin-bottom: 40rpx;
}
.empty-container .search-suggestions .suggestions-title.data-v-2a183b29 {
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 20rpx;
  display: block;
}
.empty-container .search-suggestions .suggestions-tags.data-v-2a183b29 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: center;
}
.empty-container .search-suggestions .suggestions-tags .suggestion-tag.data-v-2a183b29 {
  padding: 12rpx 24rpx;
  background: rgba(247, 140, 160, 0.1);
  border: 2rpx solid rgba(247, 140, 160, 0.3);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.empty-container .search-suggestions .suggestions-tags .suggestion-tag.data-v-2a183b29:active {
  background: rgba(247, 140, 160, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.empty-container .search-suggestions .suggestions-tags .suggestion-tag .tag-text.data-v-2a183b29 {
  font-size: 24rpx;
  color: #f78ca0;
  font-weight: 500;
}
.empty-container .reset-filters-btn.data-v-2a183b29 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid #f78ca0;
  border-radius: 25rpx;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.empty-container .reset-filters-btn.data-v-2a183b29:active {
  background: rgba(247, 140, 160, 0.1);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.empty-container .reset-filters-btn .reset-text.data-v-2a183b29 {
  font-size: 26rpx;
  color: #f78ca0;
  font-weight: 500;
}
.bottom-tabbar.data-v-2a183b29 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 16rpx 0;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid rgba(209, 213, 219, 0.2);
  z-index: 1000;
  height: 120rpx;
}
.bottom-tabbar .tab-item.data-v-2a183b29 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  transition: all 0.3s ease;
  min-width: 80rpx;
}
.bottom-tabbar .tab-item.active .tab-text.data-v-2a183b29 {
  color: #f78ca0;
  font-weight: 600;
}
.bottom-tabbar .tab-item .add-btn.data-v-2a183b29 {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #f78ca0, #fbc2eb);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(247, 140, 160, 0.4);
  -webkit-transform: translateY(-8rpx);
          transform: translateY(-8rpx);
}
.bottom-tabbar .tab-item .tab-text.data-v-2a183b29 {
  font-size: 24rpx;
  color: #9E9E9E;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}
.bottom-tabbar .tab-item .tab-text.active.data-v-2a183b29 {
  color: #f78ca0;
  font-weight: 600;
}
.search-container.data-v-2a183b29 {
  padding: 20rpx;
  padding-top: calc(25px + 20rpx);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-bottom: 1rpx solid #EEEEEE;
}
.search-container .search-bar.data-v-2a183b29 {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  padding: 20rpx 24rpx;
}
.search-container .search-bar .search-input.data-v-2a183b29 {
  flex: 1;
  margin: 0 16rpx;
  font-size: 28rpx;
  color: #424242;
}
.search-container .search-bar .cancel-btn.data-v-2a183b29 {
  font-size: 28rpx;
  color: #f78ca0;
  padding: 8rpx 16rpx;
}
@-webkit-keyframes spin-data-v-2a183b29 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-2a183b29 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
