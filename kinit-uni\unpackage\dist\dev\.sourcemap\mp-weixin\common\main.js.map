{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/App.vue?2e34", "uni-app:///App.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/App.vue?d305", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/App.vue?8ad9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "uView", "router", "plugins", "mixin", "systemMixin", "uni", "$u", "setConfig", "config", "unit", "props", "radio", "size", "labelSize", "checkbox", "iconSize", "button", "loadingSize", "text", "color", "productionTip", "prototype", "$store", "store", "App", "mpType", "app", "$mount", "onLaunch", "methods", "initApp"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AACA;AACA;AACA;AAAuD;AAAA;AAPvD;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAMF;;AAExDC,YAAG,CAACC,GAAG,CAACC,gBAAK,CAAC;AACdF,YAAG,CAACC,GAAG,CAACE,kBAAM,CAAC;AACfH,YAAG,CAACC,GAAG,CAACG,gBAAO,CAAC;;AAEhB;AACAJ,YAAG,CAACK,KAAK,CAACC,mBAAW,CAAC;;AAEtB;AACA;AACA;AACA;AACAC,GAAG,CAACC,EAAE,CAACC,SAAS,CAAC;EACf;EACAC,MAAM,EAAE;IACN;IACAC,IAAI,EAAE;EACR,CAAC;EACD;EACAC,KAAK,EAAE;IACL;IACAC,KAAK,EAAE;MACLC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE;IACb,CAAC;IACHC,QAAQ,EAAE;MACRF,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE,EAAE;MACdE,QAAQ,EAAE;IACX,CAAC;IACCC,MAAM,EAAE;MACNC,WAAW,EAAE;IACf,CAAC;IACDC,IAAI,EAAE;MACJN,IAAI,EAAE,EAAE;MACRO,KAAK,EAAE;IACT;IACA;IACA;EACF;AACF,CAAC,CAAC;;AAEFrB,YAAG,CAACU,MAAM,CAACY,aAAa,GAAG,KAAK;AAChCtB,YAAG,CAACuB,SAAS,CAACC,MAAM,GAAGC,cAAK;AAE5BC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElB,IAAMC,GAAG,GAAG,IAAI5B,YAAG,mBACd0B,YAAG,EACN;;AAEF;;AAMA,UAAAE,GAAG,EAACC,MAAM,EAAE,EAAC,uB;;;;;;;;;;;;;AClEb;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAgoB,CAAgB,qpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;eCCppB;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAAusC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACA3tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\r\nimport App from './App'\r\nimport store from './store' // store\r\nimport plugins from './plugins' // plugins\r\nimport { router, RouterMount } from './permission.js' // 路由拦截\r\nimport uView from 'uview-ui'\r\nimport { systemMixin } from './common/mixins/system.js' // 系统配置混入\r\n\r\nVue.use(uView)\r\nVue.use(router)\r\nVue.use(plugins)\r\n\r\n// 注册全局系统配置混入\r\nVue.mixin(systemMixin)\r\n\r\n// 调用setConfig方法，方法内部会进行对象属性深度合并，可以放心嵌套配置\r\n// 文档：https://www.uviewui.com/components/setting.html\r\n// 配置后，很多组件的默认尺寸就变了，需要手动调整，不熟悉不建议开启\r\n// 需要在Vue.use(uView)之后执行\r\nuni.$u.setConfig({\r\n  // 修改$u.config对象的属性\r\n  config: {\r\n    // 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'\r\n    unit: 'rpx'\r\n  },\r\n  // 修改$u.props对象的属性\r\n  props: {\r\n    // 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30\r\n    radio: {\r\n      size: 33,\r\n      labelSize: 30\r\n    },\r\n\t\tcheckbox: {\r\n\t\t  size: 33,\r\n\t\t  labelSize: 30,\r\n\t\t\ticonSize: 20\r\n\t\t},\r\n    button: {\r\n      loadingSize: 28\r\n    },\r\n    text: {\r\n      size: 30,\r\n      color: '#000'\r\n    }\r\n    // 其他组件属性配置\r\n    // ......\r\n  }\r\n})\r\n\r\nVue.config.productionTip = false\r\nVue.prototype.$store = store\r\n\r\nApp.mpType = 'app'\r\n\r\nconst app = new Vue({\r\n  ...App\r\n})\r\n\r\n//v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式\r\n\r\n\r\n\r\n\r\n\r\napp.$mount() //为了兼容小程序及app端必须这样写才有效果", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\nexport default {\r\n  onLaunch: function () {\r\n    this.initApp()\r\n  },\r\n  methods: {\r\n    // 初始化应用\r\n    async initApp() {\r\n      // 初始化应用配置\r\n      this.$store.dispatch('app/InitConfig')\r\n      // 初始化系统配置\r\n      await this.$store.dispatch('system/getSystemConfig')\r\n      // 初始化用户状态\r\n      this.$store.dispatch('user/initUserState')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import '@/static/scss/index.scss';\r\n</style>\r\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752119233942\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}