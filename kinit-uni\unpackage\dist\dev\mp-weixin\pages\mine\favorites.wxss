@charset "UTF-8";
/* uView的全局SCSS主题文件 */
/**
 * Soul风格主题变量
 */
/* Soul风格颜色变量 */
/* 主色调 - 温柔的粉色系 */
/* 辅助色 */
/* 中性色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更圆润的设计 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* Soul风格特有样式 */
/* 毛玻璃效果 */
/* 渐变背景 */
/* 阴影效果 */
/* 卡片样式 */
/* 按钮样式 */
.favorites-page.data-v-c823fa58 {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}
.custom-navbar.data-v-c823fa58 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  padding-top: 25px;
}
.custom-navbar .navbar-content.data-v-c823fa58 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
}
.custom-navbar .navbar-content .navbar-left.data-v-c823fa58, .custom-navbar .navbar-content .navbar-right.data-v-c823fa58 {
  width: 60rpx;
  display: flex;
  justify-content: center;
}
.custom-navbar .navbar-content .navbar-title.data-v-c823fa58 {
  font-size: 28rpx;
  font-weight: 600;
  color: #424242;
}
.custom-navbar .navbar-content .action-btn.data-v-c823fa58 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}
.custom-navbar .navbar-content .action-btn.data-v-c823fa58:active {
  background: rgba(255, 255, 255, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.favorites-content.data-v-c823fa58 {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
  padding-bottom: 40rpx;
}
.stats-card.data-v-c823fa58 {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.stats-card .stats-content.data-v-c823fa58 {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16rpx;
}
.stats-card .stats-content .stats-item.data-v-c823fa58 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.stats-card .stats-content .stats-item .stats-number.data-v-c823fa58 {
  font-size: 32rpx;
  font-weight: 700;
  color: #f78ca0;
}
.stats-card .stats-content .stats-item .stats-label.data-v-c823fa58 {
  font-size: 22rpx;
  color: #757575;
}
.stats-card .batch-actions.data-v-c823fa58 {
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-top: 16rpx;
  display: flex;
  justify-content: center;
}
.stats-card .batch-actions .batch-btn.data-v-c823fa58 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.stats-card .batch-actions .batch-btn.delete.data-v-c823fa58 {
  background: rgba(239, 68, 68, 0.1);
  border: 1rpx solid rgba(239, 68, 68, 0.3);
}
.stats-card .batch-actions .batch-btn.delete .batch-text.data-v-c823fa58 {
  color: #ef4444;
}
.stats-card .batch-actions .batch-btn .batch-text.data-v-c823fa58 {
  font-size: 24rpx;
  font-weight: 500;
}
.stats-card .batch-actions .batch-btn.data-v-c823fa58:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.favorites-list .favorite-item.data-v-c823fa58 {
  position: relative;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
.favorites-list .favorite-item.selected.data-v-c823fa58 {
  border: 2rpx solid #f78ca0;
  box-shadow: 0 4rpx 16rpx rgba(247, 140, 160, 0.2);
}
.favorites-list .favorite-item.data-v-c823fa58:active:not(.selected) {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.favorites-list .favorite-item .select-checkbox.data-v-c823fa58 {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  z-index: 10;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.favorites-list .favorite-item .favorite-time.data-v-c823fa58 {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 12rpx 20rpx;
  background: rgba(0, 0, 0, 0.02);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}
.favorites-list .favorite-item .favorite-time .time-text.data-v-c823fa58 {
  font-size: 20rpx;
  color: #9E9E9E;
}
.empty-state.data-v-c823fa58 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}
.empty-state .empty-icon.data-v-c823fa58 {
  margin-bottom: 24rpx;
  opacity: 0.5;
}
.empty-state .empty-text.data-v-c823fa58 {
  font-size: 28rpx;
  color: #757575;
  margin-bottom: 12rpx;
  font-weight: 600;
}
.empty-state .empty-desc.data-v-c823fa58 {
  font-size: 24rpx;
  color: #9E9E9E;
  margin-bottom: 32rpx;
  line-height: 1.4;
}
.empty-state .empty-action.data-v-c823fa58 {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}
.empty-state .empty-action .action-text.data-v-c823fa58 {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}
.loading-state.data-v-c823fa58 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  gap: 16rpx;
}
.loading-state .loading-text.data-v-c823fa58 {
  font-size: 24rpx;
  color: #9E9E9E;
}
@-webkit-keyframes spin-data-v-c823fa58 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-c823fa58 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.loading-state uni-icons.data-v-c823fa58 {
  -webkit-animation: spin-data-v-c823fa58 1s linear infinite;
          animation: spin-data-v-c823fa58 1s linear infinite;
}
