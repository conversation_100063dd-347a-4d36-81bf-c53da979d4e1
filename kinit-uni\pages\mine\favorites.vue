<template>
  <view class="favorites-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="left" size="32" color="#FF6B9D"></uni-icons>
        </view>
        <text class="navbar-title">我的收藏</text>
        <view class="navbar-right">
          <view class="action-btn" @click="toggleEditMode">
            <uni-icons :type="editMode ? 'checkmarkempty' : 'compose'" size="24" color="#f78ca0"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <scroll-view scroll-y="true" class="favorites-content">
      <!-- 收藏统计 -->
      <view class="stats-card">
        <view class="stats-content">
          <view class="stats-item">
            <text class="stats-number">{{ favoritesList.length }}</text>
            <text class="stats-label">收藏总数</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{ selectedItems.length }}</text>
            <text class="stats-label">已选择</text>
          </view>
        </view>
        
        <view v-if="editMode && selectedItems.length > 0" class="batch-actions">
          <view class="batch-btn delete" @click="batchDelete">
            <uni-icons type="trash" size="16" color="#ef4444"></uni-icons>
            <text class="batch-text">删除选中</text>
          </view>
        </view>
      </view>

      <!-- 收藏列表 -->
      <view class="favorites-list">
        <view 
          v-for="(item, index) in favoritesList" 
          :key="index"
          class="favorite-item"
          :class="{ selected: selectedItems.includes(item.id) }"
          @click="handleItemClick(item)"
        >
          <!-- 选择框 -->
          <view v-if="editMode" class="select-checkbox" @click.stop="toggleSelect(item.id)">
            <uni-icons 
              :type="selectedItems.includes(item.id) ? 'checkmarkempty' : 'circle'" 
              size="20" 
              :color="selectedItems.includes(item.id) ? '#f78ca0' : '#d1d5db'"
            ></uni-icons>
          </view>

          <!-- 投稿卡片 -->
          <soul-submission-card 
            :submission="item" 
            :show-favorite="false"
            @click="goToDetail(item)"
          />

          <!-- 收藏时间 -->
          <view class="favorite-time">
            <uni-icons type="calendar" size="14" color="#999"></uni-icons>
            <text class="time-text">收藏于 {{ formatTime(item.favorite_time) }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="favoritesList.length === 0 && !loading" class="empty-state">
        <view class="empty-icon">
          <uni-icons type="heart" size="80" color="#d1d5db"></uni-icons>
        </view>
        <text class="empty-text">还没有收藏任何投稿</text>
        <text class="empty-desc">去首页看看有没有喜欢的投稿吧～</text>
        <view class="empty-action" @click="goToHome">
          <text class="action-text">去首页看看</text>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <uni-icons type="spinner-cycle" size="32" color="#f78ca0"></uni-icons>
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import submissionApi from '@/common/api/submission.js'
import SoulSubmissionCard from '@/components/soul-submission-card/soul-submission-card.vue'

export default {
  components: {
    SoulSubmissionCard
  },
  data() {
    return {
      favoritesList: [],
      loading: true,
      editMode: false,
      selectedItems: [],
      pagination: {
        page: 1,
        size: 20,
        total: 0
      }
    }
  },
  onLoad() {
    this.loadFavorites()
  },
  onShow() {
    // 页面显示时刷新数据
    this.loadFavorites()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadFavorites() {
      if (!this.$store.getters['user/isLoggedIn']) {
        this.showLoginModal()
        return
      }

      this.loading = true
      
      try {
        const res = await submissionApi.getFavoriteList({
          page: this.pagination.page,
          size: this.pagination.size
        })
        
        if (res.code === 200) {
          this.favoritesList = res.data.items || []
          this.pagination.total = res.data.total || 0
        } else {
          throw new Error(res.message || '加载失败')
        }
      } catch (error) {
        console.error('加载收藏列表失败:', error)
        // 使用模拟数据
        this.favoritesList = this.getMockFavorites()
      } finally {
        this.loading = false
      }
    },

    getMockFavorites() {
      return [
        {
          id: 1,
          title: '寻找游戏搭子',
          submission_code: 'zhiyu001',
          city: '杭州',
          province: '浙江',
          age: 22,
          gender: '女',
          occupation: '学生',
          cover_image: '/static/images/demo1.jpg',
          favorite_time: '2024-01-15 14:30:00'
        },
        {
          id: 2,
          title: '一起看电影',
          submission_code: 'zhiyu002',
          city: '上海',
          province: '上海',
          age: 25,
          gender: '女',
          occupation: '设计师',
          cover_image: '/static/images/demo2.jpg',
          favorite_time: '2024-01-16 09:15:00'
        }
      ]
    },

    showLoginModal() {
      uni.showModal({
        title: '登录提示',
        content: '需要登录后才能查看收藏',
        confirmText: '立即登录',
        cancelText: '稍后再说',
        success: (res) => {
          if (res.confirm) {
            this.performWxLogin()
          } else {
            uni.navigateBack()
          }
        }
      })
    },

    async performWxLogin() {
      try {
        const loginRes = await uni.login({ provider: 'weixin' })
        const userInfoRes = await uni.getUserProfile({
          desc: '用于完善用户资料'
        })
        
        await this.$store.dispatch('user/wxLogin', {
          code: loginRes.code,
          userInfo: userInfoRes.userInfo
        })
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        this.loadFavorites()
        
      } catch (error) {
        console.error('微信登录失败:', error)
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
        uni.navigateBack()
      }
    },

    toggleEditMode() {
      this.editMode = !this.editMode
      if (!this.editMode) {
        this.selectedItems = []
      }
    },

    toggleSelect(itemId) {
      const index = this.selectedItems.indexOf(itemId)
      if (index > -1) {
        this.selectedItems.splice(index, 1)
      } else {
        this.selectedItems.push(itemId)
      }
    },

    handleItemClick(item) {
      if (this.editMode) {
        this.toggleSelect(item.id)
      } else {
        this.goToDetail(item)
      }
    },

    goToDetail(item) {
      if (this.editMode) return
      
      uni.navigateTo({
        url: `/pages/submission/detail?id=${item.id}`
      })
    },

    async batchDelete() {
      if (this.selectedItems.length === 0) return

      uni.showModal({
        title: '确认删除',
        content: `确定要删除选中的 ${this.selectedItems.length} 个收藏吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              // 这里应该调用批量删除API
              // await submissionApi.batchDeleteFavorites(this.selectedItems)
              
              // 模拟删除
              this.favoritesList = this.favoritesList.filter(
                item => !this.selectedItems.includes(item.id)
              )
              
              this.selectedItems = []
              this.editMode = false
              
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('批量删除失败:', error)
              uni.showToast({
                title: '删除失败，请重试',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    formatTime(timeStr) {
      const date = new Date(timeStr)
      const now = new Date()
      const diff = now - date

      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else if (days < 7) {
        return `${days}天前`
      } else {
        return date.toLocaleDateString()
      }
    },

    goToHome() {
      uni.switchTab({
        url: '/pages/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.favorites-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;

    .navbar-left, .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: center;
    }

    .navbar-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }

    .action-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(0.95);
      }
    }
  }
}

.favorites-content {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.stats-card {
  @include glass-effect(0.7);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);

  .stats-content {
    display: flex;
    justify-content: space-around;
    margin-bottom: 16rpx;

    .stats-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;

      .stats-number {
        font-size: 32rpx;
        font-weight: 700;
        color: #f78ca0;
      }

      .stats-label {
        font-size: 22rpx;
        color: $soul-gray-600;
      }
    }
  }

  .batch-actions {
    border-top: 1rpx solid rgba(0,0,0,0.05);
    padding-top: 16rpx;
    display: flex;
    justify-content: center;

    .batch-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 24rpx;
      border-radius: 20rpx;
      transition: all 0.3s ease;

      &.delete {
        background: rgba(239, 68, 68, 0.1);
        border: 1rpx solid rgba(239, 68, 68, 0.3);

        .batch-text {
          color: #ef4444;
        }
      }

      .batch-text {
        font-size: 24rpx;
        font-weight: 500;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

.favorites-list {
  .favorite-item {
    position: relative;
    @include glass-effect(0.6);
    border-radius: 20rpx;
    margin-bottom: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
    transition: all 0.3s ease;

    &.selected {
      border: 2rpx solid #f78ca0;
      box-shadow: 0 4rpx 16rpx rgba(247, 140, 160, 0.2);
    }

    &:active:not(.selected) {
      transform: scale(0.98);
    }

    .select-checkbox {
      position: absolute;
      top: 16rpx;
      left: 16rpx;
      z-index: 10;
      width: 40rpx;
      height: 40rpx;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
    }

    .favorite-time {
      display: flex;
      align-items: center;
      gap: 6rpx;
      padding: 12rpx 20rpx;
      background: rgba(0,0,0,0.02);
      border-top: 1rpx solid rgba(0,0,0,0.05);

      .time-text {
        font-size: 20rpx;
        color: $soul-gray-500;
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-icon {
    margin-bottom: 24rpx;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 28rpx;
    color: $soul-gray-600;
    margin-bottom: 12rpx;
    font-weight: 600;
  }

  .empty-desc {
    font-size: 24rpx;
    color: $soul-gray-500;
    margin-bottom: 32rpx;
    line-height: 1.4;
  }

  .empty-action {
    padding: 16rpx 32rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);

    .action-text {
      font-size: 24rpx;
      color: white;
      font-weight: 600;
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  gap: 16rpx;

  .loading-text {
    font-size: 24rpx;
    color: $soul-gray-500;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-state uni-icons {
  animation: spin 1s linear infinite;
}
</style>
