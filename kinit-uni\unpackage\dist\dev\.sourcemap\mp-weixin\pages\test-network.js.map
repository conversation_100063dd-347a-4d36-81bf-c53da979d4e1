{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/test-network.vue?90ab", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/test-network.vue?1ab8", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/test-network.vue?bfd2", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/test-network.vue?0439", "uni-app:///pages/test-network.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/test-network.vue?458d", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/test-network.vue?70f2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "testing", "logs", "backendStatus", "text", "class", "api<PERSON><PERSON>us", "submissionStatus", "onLoad", "methods", "addLog", "time", "message", "type", "testConnection", "testBasicConnection", "uni", "url", "method", "timeout", "success", "resolve", "fail", "reject", "result", "testApiConnection", "testSubmissionApi", "header", "switchToMock", "title", "icon", "duration", "switchToReal"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyoB,CAAgB,8pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkD7pB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACAF;QACAC;MACA;MACAE;QACAH;QACAC;MACA;IACA;EACA;EAEAG;IACA;IACA;EACA;EAEAC;IACAC;MAAA;MACA;MACA;MAEA;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;;gBAEA;gBACA;kBAAAV;kBAAAC;gBAAA;gBACA;kBAAAD;kBAAAC;gBAAA;gBACA;kBAAAD;kBAAAC;gBAAA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OAEA;kBACAC;oBACAC;oBACAC;oBACAC;oBACAC;sBACA;sBACA;wBAAAhB;wBAAAC;sBAAA;sBACAgB;oBACA;oBACAC;sBACA;sBACA;wBAAAlB;wBAAAC;sBAAA;sBACAkB;oBACA;kBACA;gBACA;cAAA;gBAhBAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAmBA;gBACA;kBAAApB;kBAAAC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAoB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OAEA;kBACAT;oBACAC;oBACAC;oBACAC;oBACAC;sBACA;sBACA;wBAAAhB;wBAAAC;sBAAA;sBACAgB;oBACA;oBACAC;sBACA;sBACA;wBAAAlB;wBAAAC;sBAAA;sBACAkB;oBACA;kBACA;gBACA;cAAA;gBAhBAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAmBA;gBACA;kBAAApB;kBAAAC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OAEA;kBACAV;oBACAC;oBACAC;oBACAC;oBACAQ;sBACA;oBACA;oBACAP;sBACA;sBACA;sBACA;wBAAAhB;wBAAAC;sBAAA;sBACAgB;oBACA;oBACAC;sBACA;sBACA;wBAAAlB;wBAAAC;sBAAA;sBACAkB;oBACA;kBACA;gBACA;cAAA;gBApBAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAuBA;gBACA;kBAAApB;kBAAAC;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuB;MACA;MACAZ;QACAa;QACAC;QACAC;MACA;IACA;IAEAC;MACA;MACAhB;QACAa;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtOA;AAAA;AAAA;AAAA;AAAwuC,CAAgB,qsCAAG,EAAC,C;;;;;;;;;;;ACA5vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/test-network.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/test-network.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./test-network.vue?vue&type=template&id=42b1b36c&scoped=true&\"\nvar renderjs\nimport script from \"./test-network.vue?vue&type=script&lang=js&\"\nexport * from \"./test-network.vue?vue&type=script&lang=js&\"\nimport style0 from \"./test-network.vue?vue&type=style&index=0&id=42b1b36c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"42b1b36c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/test-network.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-network.vue?vue&type=template&id=42b1b36c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-network.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-network.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"test-network\">\n    <view class=\"header\">\n      <text class=\"title\">网络连接测试</text>\n    </view>\n    \n    <view class=\"test-section\">\n      <view class=\"test-item\">\n        <text class=\"test-label\">后端服务状态:</text>\n        <text :class=\"['test-status', backendStatus.class]\">{{ backendStatus.text }}</text>\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">API连接测试:</text>\n        <text :class=\"['test-status', apiStatus.class]\">{{ apiStatus.text }}</text>\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"test-label\">投稿接口测试:</text>\n        <text :class=\"['test-status', submissionStatus.class]\">{{ submissionStatus.text }}</text>\n      </view>\n    </view>\n    \n    <view class=\"button-section\">\n      <button @click=\"testConnection\" :disabled=\"testing\" class=\"test-button\">\n        {{ testing ? '测试中...' : '开始测试' }}\n      </button>\n      \n      <button @click=\"switchToMock\" class=\"mock-button\">\n        切换到模拟数据模式\n      </button>\n      \n      <button @click=\"switchToReal\" class=\"real-button\">\n        切换到真实API模式\n      </button>\n    </view>\n    \n    <view class=\"log-section\">\n      <text class=\"log-title\">测试日志:</text>\n      <scroll-view class=\"log-content\" scroll-y>\n        <view v-for=\"(log, index) in logs\" :key=\"index\" class=\"log-item\">\n          <text class=\"log-time\">{{ log.time }}</text>\n          <text :class=\"['log-message', log.type]\">{{ log.message }}</text>\n        </view>\n      </scroll-view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport config from '@/config.js'\nimport submissionApi from '@/common/api/submission.js'\n\nexport default {\n  data() {\n    return {\n      testing: false,\n      logs: [],\n      backendStatus: {\n        text: '未测试',\n        class: 'pending'\n      },\n      apiStatus: {\n        text: '未测试',\n        class: 'pending'\n      },\n      submissionStatus: {\n        text: '未测试',\n        class: 'pending'\n      }\n    }\n  },\n  \n  onLoad() {\n    this.addLog('页面加载完成', 'info')\n    this.addLog(`后端地址: ${config.baseUrl}`, 'info')\n  },\n  \n  methods: {\n    addLog(message, type = 'info') {\n      const now = new Date()\n      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`\n      \n      this.logs.unshift({\n        time,\n        message,\n        type\n      })\n      \n      // 限制日志数量\n      if (this.logs.length > 50) {\n        this.logs = this.logs.slice(0, 50)\n      }\n    },\n    \n    async testConnection() {\n      if (this.testing) return\n      \n      this.testing = true\n      this.addLog('开始网络连接测试...', 'info')\n      \n      // 重置状态\n      this.backendStatus = { text: '测试中...', class: 'testing' }\n      this.apiStatus = { text: '测试中...', class: 'testing' }\n      this.submissionStatus = { text: '测试中...', class: 'testing' }\n      \n      try {\n        // 测试1: 基础连接测试\n        await this.testBasicConnection()\n        \n        // 测试2: API接口测试\n        await this.testApiConnection()\n        \n        // 测试3: 投稿接口测试\n        await this.testSubmissionApi()\n        \n      } catch (error) {\n        this.addLog(`测试过程出错: ${error.message}`, 'error')\n      } finally {\n        this.testing = false\n        this.addLog('测试完成', 'info')\n      }\n    },\n    \n    async testBasicConnection() {\n      try {\n        this.addLog('测试基础连接...', 'info')\n        \n        const result = await new Promise((resolve, reject) => {\n          uni.request({\n            url: config.baseUrl,\n            method: 'GET',\n            timeout: 5000,\n            success: (res) => {\n              this.addLog(`基础连接成功，状态码: ${res.statusCode}`, 'success')\n              this.backendStatus = { text: '连接成功', class: 'success' }\n              resolve(res)\n            },\n            fail: (err) => {\n              this.addLog(`基础连接失败: ${err.errMsg}`, 'error')\n              this.backendStatus = { text: '连接失败', class: 'error' }\n              reject(err)\n            }\n          })\n        })\n        \n      } catch (error) {\n        this.addLog(`基础连接异常: ${error.errMsg || error.message}`, 'error')\n        this.backendStatus = { text: '连接异常', class: 'error' }\n      }\n    },\n    \n    async testApiConnection() {\n      try {\n        this.addLog('测试API接口...', 'info')\n        \n        const result = await new Promise((resolve, reject) => {\n          uni.request({\n            url: `${config.baseUrl}/docs`,\n            method: 'GET',\n            timeout: 5000,\n            success: (res) => {\n              this.addLog(`API接口测试成功，状态码: ${res.statusCode}`, 'success')\n              this.apiStatus = { text: '接口正常', class: 'success' }\n              resolve(res)\n            },\n            fail: (err) => {\n              this.addLog(`API接口测试失败: ${err.errMsg}`, 'error')\n              this.apiStatus = { text: '接口异常', class: 'error' }\n              reject(err)\n            }\n          })\n        })\n        \n      } catch (error) {\n        this.addLog(`API接口异常: ${error.errMsg || error.message}`, 'error')\n        this.apiStatus = { text: '接口异常', class: 'error' }\n      }\n    },\n    \n    async testSubmissionApi() {\n      try {\n        this.addLog('测试投稿接口...', 'info')\n        \n        const result = await new Promise((resolve, reject) => {\n          uni.request({\n            url: `${config.baseUrl}/submission/config/options`,\n            method: 'GET',\n            timeout: 5000,\n            header: {\n              'Content-Type': 'application/json'\n            },\n            success: (res) => {\n              this.addLog(`投稿接口测试成功，状态码: ${res.statusCode}`, 'success')\n              this.addLog(`返回数据: ${JSON.stringify(res.data).substring(0, 100)}...`, 'info')\n              this.submissionStatus = { text: '接口正常', class: 'success' }\n              resolve(res)\n            },\n            fail: (err) => {\n              this.addLog(`投稿接口测试失败: ${err.errMsg}`, 'error')\n              this.submissionStatus = { text: '接口异常', class: 'error' }\n              reject(err)\n            }\n          })\n        })\n        \n      } catch (error) {\n        this.addLog(`投稿接口异常: ${error.errMsg || error.message}`, 'error')\n        this.submissionStatus = { text: '接口异常', class: 'error' }\n      }\n    },\n    \n    switchToMock() {\n      this.addLog('切换到模拟数据模式...', 'info')\n      uni.showToast({\n        title: '请手动修改 common/api/submission.js 中的 isDev 为 true',\n        icon: 'none',\n        duration: 3000\n      })\n    },\n    \n    switchToReal() {\n      this.addLog('切换到真实API模式...', 'info')\n      uni.showToast({\n        title: '请手动修改 common/api/submission.js 中的 isDev 为 false',\n        icon: 'none',\n        duration: 3000\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.test-network {\n  padding: 20rpx;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n  \n  .title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n  }\n}\n\n.test-section {\n  background: white;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  \n  .test-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 20rpx 0;\n    border-bottom: 1px solid #eee;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .test-label {\n      font-size: 28rpx;\n      color: #666;\n    }\n    \n    .test-status {\n      font-size: 28rpx;\n      font-weight: bold;\n      \n      &.pending {\n        color: #999;\n      }\n      \n      &.testing {\n        color: #409eff;\n      }\n      \n      &.success {\n        color: #67c23a;\n      }\n      \n      &.error {\n        color: #f56c6c;\n      }\n    }\n  }\n}\n\n.button-section {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n  margin-bottom: 30rpx;\n  \n  button {\n    padding: 24rpx;\n    border-radius: 12rpx;\n    font-size: 28rpx;\n    border: none;\n    \n    &.test-button {\n      background: #409eff;\n      color: white;\n      \n      &:disabled {\n        background: #c0c4cc;\n      }\n    }\n    \n    &.mock-button {\n      background: #e6a23c;\n      color: white;\n    }\n    \n    &.real-button {\n      background: #67c23a;\n      color: white;\n    }\n  }\n}\n\n.log-section {\n  background: white;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  \n  .log-title {\n    font-size: 28rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n  }\n  \n  .log-content {\n    height: 400rpx;\n    \n    .log-item {\n      display: flex;\n      margin-bottom: 10rpx;\n      font-size: 24rpx;\n      \n      .log-time {\n        color: #999;\n        margin-right: 20rpx;\n        min-width: 120rpx;\n      }\n      \n      .log-message {\n        flex: 1;\n        \n        &.info {\n          color: #333;\n        }\n        \n        &.success {\n          color: #67c23a;\n        }\n        \n        &.error {\n          color: #f56c6c;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-network.vue?vue&type=style&index=0&id=42b1b36c&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test-network.vue?vue&type=style&index=0&id=42b1b36c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752118672164\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}