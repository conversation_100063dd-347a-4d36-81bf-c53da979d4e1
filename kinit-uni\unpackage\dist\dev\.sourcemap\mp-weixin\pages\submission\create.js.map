{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?02d2", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?4470", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?c529", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?420c", "uni-app:///pages/submission/create.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?c836", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?f667"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "province", "city", "age", "gender", "height", "occupation", "self_intro", "partner_requirements", "accept_long_distance", "wechat_id", "category", "imageList", "wechatQRCode", "submitting", "genderOptions", "label", "value", "icon", "provinces", "name", "cities", "picker<PERSON><PERSON><PERSON>", "selectedProvinceIndex", "selectedCityIndex", "computed", "selectedCity", "currentCities", "methods", "goBack", "uni", "selectGender", "showCityPicker", "hideCityPicker", "onPickerChange", "confirmCity", "title", "chooseImage", "count", "sizeType", "sourceType", "success", "removeImage", "previewImage", "urls", "current", "chooseQRCode", "removeQRCode", "previewQRCode", "validateForm", "submitForm", "imageUrls", "wechatQRCodeUrl", "submitData", "cover_image", "images", "wechat_qrcode", "status", "is_visible", "submissionApi", "res", "setTimeout", "console", "uploadImages", "uploadPromises", "url", "filePath", "header", "resolve", "reject", "fail", "Promise", "uploadSingleImage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,wpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACmRvpB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MAAA;MACAC;MACAC,gBACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACA;MACAC,YACA;QAAAC;QAAAC;UAAAD;QAAA;MAAA,GACA;QAAAA;QAAAC;UAAAD;QAAA;MAAA,GACA;QAAAA;QAAAC;UAAAD;QAAA;MAAA,GACA;QAAAA;QAAAC;UAAAD;QAAA;MAAA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;MACA,GACA;QAAAA;QAAAC,SACA;UAAAD;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA;UAAAA;QAAA,GACA;UAAAA;QAAA;UAAAA;QAAA;MACA,EACA;MACAE;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MAEA;QACAL;UACAM;UACAlB;QACA;QACA;MACA;MAEA;MACA;MACA;IACA;IAEAmB;MAAA;MACA;MACAP;QACAQ;QACAC;QACAC;QACAC;UAAA;UACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACAb;QACAc;QACAC;MACA;IACA;IAEAC;MAAA;MACAhB;QACAQ;QACAC;QACAC;QACAC;UACA;QACA;MACA;IACA;IAEAM;MACA;IACA;IAEAC;MACAlB;QACAc;QACAC;MACA;IACA;IAEAI;MACA;QACAnB;UAAAM;UAAAlB;QAAA;QACA;MACA;MAEA;QACAY;UAAAM;UAAAlB;QAAA;QACA;MACA;MAEA;QACAY;UAAAM;UAAAlB;QAAA;QACA;MACA;MAEA;QACAY;UAAAM;UAAAlB;QAAA;QACA;MACA;MAEA;QACAY;UAAAM;UAAAlB;QAAA;QACA;MACA;MAEA;QACAY;UAAAM;UAAAlB;QAAA;QACA;MACA;MAEA;QACAY;UAAAM;UAAAlB;QAAA;QACA;MACA;MAEA;QACAY;UAAAM;UAAAlB;QAAA;QACA;MACA;MAEA;IACA;IAEAgC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAC;gBAEA;gBACAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAA;cAAA;gBAGA;gBACAC,6CACA;kBACAC;kBAAA;kBACAC;kBAAA;kBACAC;kBAAA;kBACArD;kBACAE;kBACAoD;kBAAA;kBACAC;gBAAA,IAGA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA9B;kBACAM;kBACAlB;gBACA;gBAEA2C;kBACA/B;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAgC;gBACAhC;kBACAM;kBACAlB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA6C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACA;oBACAlC;sBACAmC;sBACAC;sBACA9C;sBACA+C;wBACA;sBACA;sBACA1B;wBACA;0BACA;0BACA;4BACA2B;0BACA;4BACAC;0BACA;wBACA;0BACAA;wBACA;sBACA;sBACAC;wBACAD;sBACA;oBACA;kBACA;gBACA;gBAAA,kCAEAE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACA1C;oBACAmC;oBACAC;oBACA9C;oBACA+C;sBACA;oBACA;oBACA1B;sBACA;wBACA;wBACA;0BACA2B;wBACA;0BACAC;wBACA;sBACA;wBACAA;sBACA;oBACA;oBACAC;sBACAD;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACruBA;AAAA;AAAA;AAAA;AAAkuC,CAAgB,+rCAAG,EAAC,C;;;;;;;;;;;ACAtvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/submission/create.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/submission/create.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./create.vue?vue&type=template&id=3d953676&scoped=true&\"\nvar renderjs\nimport script from \"./create.vue?vue&type=script&lang=js&\"\nexport * from \"./create.vue?vue&type=script&lang=js&\"\nimport style0 from \"./create.vue?vue&type=style&index=0&id=3d953676&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3d953676\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/submission/create.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./create.vue?vue&type=template&id=3d953676&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.self_intro.length\n  var g1 = _vm.formData.partner_requirements.length\n  var g2 = _vm.imageList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.formData.accept_long_distance = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.formData.accept_long_distance = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./create.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./create.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"create-page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-content\">\n        <view class=\"navbar-left\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n        </view>\n        <text class=\"navbar-title\">发布投稿</text>\n        <view class=\"navbar-right\"></view>\n      </view>\n    </view>\n\n    <scroll-view scroll-y=\"true\" class=\"form-content\">\n      <view class=\"form-container\">\n        <!-- 基本信息卡片 -->\n        <view class=\"form-card\">\n          <view class=\"card-header\">\n            <uni-icons type=\"person-filled\" size=\"24\" color=\"#f78ca0\"></uni-icons>\n            <text class=\"card-title\">基本信息</text>\n          </view>\n\n          <!-- 所在城市 -->\n          <view class=\"form-item\">\n            <view class=\"form-label-wrapper\">\n              <text class=\"form-label\">所在城市</text>\n              <text class=\"required-mark\">*</text>\n            </view>\n            <view class=\"city-selector\" @click=\"showCityPicker\">\n              <text class=\"city-text\" :class=\"{ placeholder: !selectedCity }\">\n                {{ selectedCity || '请选择所在城市' }}\n              </text>\n              <uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n            </view>\n          </view>\n\n          <!-- 年龄 -->\n          <view class=\"form-item\">\n            <view class=\"form-label-wrapper\">\n              <text class=\"form-label\">年龄</text>\n              <text class=\"required-mark\">*</text>\n            </view>\n            <input \n              v-model=\"formData.age\" \n              type=\"number\" \n              placeholder=\"请输入年龄（16-60岁）\"\n              class=\"form-input\"\n              maxlength=\"2\"\n            />\n          </view>\n\n          <!-- 性别 -->\n          <view class=\"form-item\">\n            <text class=\"form-label\">性别 *</text>\n            <view class=\"gender-options\">\n              <view \n                v-for=\"(option, index) in genderOptions\" \n                :key=\"index\"\n                class=\"gender-option\"\n                :class=\"{ active: formData.gender === option.value }\"\n                @click=\"selectGender(option.value)\"\n              >\n                <uni-icons :type=\"option.icon\" size=\"20\" :color=\"formData.gender === option.value ? '#f78ca0' : '#999'\"></uni-icons>\n                <text class=\"gender-text\">{{ option.label }}</text>\n              </view>\n            </view>\n          </view>\n\n          <!-- 身高 -->\n          <view class=\"form-item\">\n            <text class=\"form-label\">身高 *</text>\n            <input \n              v-model=\"formData.height\" \n              type=\"number\" \n              placeholder=\"请输入身高（cm）\"\n              class=\"form-input\"\n              maxlength=\"3\"\n            />\n          </view>\n\n          <!-- 职业 -->\n          <view class=\"form-item\">\n            <text class=\"form-label\">职业/工作状态 *</text>\n            <input \n              v-model=\"formData.occupation\" \n              type=\"text\" \n              placeholder=\"如：学生、程序员、设计师等\"\n              class=\"form-input\"\n              maxlength=\"20\"\n            />\n          </view>\n\n          <!-- 是否接受异地 -->\n          <view class=\"form-item\">\n            <text class=\"form-label\">是否接受异地 *</text>\n            <view class=\"distance-options\">\n              <view \n                class=\"distance-option\"\n                :class=\"{ active: formData.accept_long_distance === true }\"\n                @click=\"formData.accept_long_distance = true\"\n              >\n                <uni-icons type=\"checkmarkempty\" size=\"16\" :color=\"formData.accept_long_distance === true ? '#10b981' : '#999'\"></uni-icons>\n                <text class=\"distance-text\">接受异地</text>\n              </view>\n              <view \n                class=\"distance-option\"\n                :class=\"{ active: formData.accept_long_distance === false }\"\n                @click=\"formData.accept_long_distance = false\"\n              >\n                <uni-icons type=\"closeempty\" size=\"16\" :color=\"formData.accept_long_distance === false ? '#ef4444' : '#999'\"></uni-icons>\n                <text class=\"distance-text\">仅限同城</text>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 详细信息卡片 -->\n        <view class=\"form-card\">\n          <view class=\"card-header\">\n            <uni-icons type=\"chat\" size=\"24\" color=\"#a6c1ee\"></uni-icons>\n            <text class=\"card-title\">详细信息</text>\n          </view>\n\n          <!-- 自我介绍 -->\n          <view class=\"form-item\">\n            <text class=\"form-label\">自我介绍 *</text>\n            <textarea \n              v-model=\"formData.self_intro\" \n              placeholder=\"简单介绍一下自己，让大家更了解你～\"\n              class=\"form-textarea\"\n              maxlength=\"500\"\n              :show-confirm-bar=\"false\"\n            />\n            <view class=\"char-count\">{{ formData.self_intro.length }}/500</view>\n          </view>\n\n          <!-- 搭子要求 -->\n          <view class=\"form-item\">\n            <text class=\"form-label\">搭子要求 *</text>\n            <textarea \n              v-model=\"formData.partner_requirements\" \n              placeholder=\"描述一下你希望找到什么样的搭子～\"\n              class=\"form-textarea\"\n              maxlength=\"500\"\n              :show-confirm-bar=\"false\"\n            />\n            <view class=\"char-count\">{{ formData.partner_requirements.length }}/500</view>\n          </view>\n        </view>\n\n        <!-- 图片上传卡片 -->\n        <view class=\"form-card\">\n          <view class=\"card-header\">\n            <uni-icons type=\"image\" size=\"24\" color=\"#feb47b\"></uni-icons>\n            <text class=\"card-title\">上传照片</text>\n            <text class=\"card-subtitle\">（1-8张，第一张为主图）</text>\n          </view>\n\n          <view class=\"image-upload-container\">\n            <view class=\"image-grid\">\n              <view \n                v-for=\"(image, index) in imageList\" \n                :key=\"index\"\n                class=\"image-item\"\n                @click=\"previewImage(index)\"\n              >\n                <image :src=\"image\" class=\"upload-image\" mode=\"aspectFill\" />\n                <view class=\"image-actions\">\n                  <view v-if=\"index === 0\" class=\"main-badge\">主图</view>\n                  <view class=\"delete-btn\" @click.stop=\"removeImage(index)\">\n                    <uni-icons type=\"close\" size=\"16\" color=\"#fff\"></uni-icons>\n                  </view>\n                </view>\n              </view>\n              \n              <view \n                v-if=\"imageList.length < 8\" \n                class=\"upload-btn\"\n                @click=\"chooseImage\"\n              >\n                <uni-icons type=\"plus\" size=\"32\" color=\"#999\"></uni-icons>\n                <text class=\"upload-text\">添加照片</text>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 联系方式卡片 -->\n        <view class=\"form-card\">\n          <view class=\"card-header\">\n            <uni-icons type=\"chat\" size=\"24\" color=\"#c8a8e9\"></uni-icons>\n            <text class=\"card-title\">联系方式</text>\n            <text class=\"card-subtitle\">（选填）</text>\n          </view>\n\n          <!-- 微信号 -->\n          <view class=\"form-item\">\n            <text class=\"form-label\">微信号</text>\n            <input\n              v-model=\"formData.wechat_id\"\n              type=\"text\"\n              placeholder=\"请输入微信号\"\n              class=\"form-input\"\n              maxlength=\"50\"\n            />\n          </view>\n\n          <!-- 微信二维码 -->\n          <view class=\"form-item\">\n            <text class=\"form-label\">微信二维码</text>\n            <view class=\"qrcode-upload-container\">\n              <view v-if=\"wechatQRCode\" class=\"qrcode-preview\" @click=\"previewQRCode\">\n                <image :src=\"wechatQRCode\" class=\"qrcode-image\" mode=\"aspectFit\" />\n                <view class=\"qrcode-actions\">\n                  <view class=\"qrcode-delete\" @click.stop=\"removeQRCode\">\n                    <uni-icons type=\"close\" size=\"16\" color=\"#fff\"></uni-icons>\n                  </view>\n                </view>\n              </view>\n\n              <view v-else class=\"qrcode-upload-btn\" @click=\"chooseQRCode\">\n                <uni-icons type=\"plus\" size=\"32\" color=\"#999\"></uni-icons>\n                <text class=\"upload-text\">上传微信二维码</text>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 提交按钮 -->\n        <view class=\"submit-section\">\n          <view class=\"submit-btn\" @click=\"submitForm\" :class=\"{ disabled: submitting }\">\n            <view v-if=\"submitting\" class=\"loading-icon\">\n              <uni-icons type=\"spinner-cycle\" size=\"24\" color=\"#fff\"></uni-icons>\n            </view>\n            <text class=\"submit-text\">{{ submitting ? '提交中...' : '发布投稿' }}</text>\n          </view>\n          \n          <view class=\"tips-text\">\n            <text>投稿将在审核通过后展示，请确保信息真实有效</text>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 城市选择器 -->\n    <uni-popup ref=\"cityPopup\" type=\"bottom\" background-color=\"#ffffff\">\n      <view class=\"city-picker\">\n        <view class=\"picker-header\">\n          <text class=\"picker-title\">选择所在城市</text>\n          <view class=\"picker-close\" @click=\"hideCityPicker\">\n            <uni-icons type=\"close\" size=\"24\" color=\"#999\"></uni-icons>\n          </view>\n        </view>\n        <picker-view class=\"picker-view\" :value=\"pickerValue\" @change=\"onPickerChange\">\n          <picker-view-column>\n            <view v-for=\"(province, index) in provinces\" :key=\"index\" class=\"picker-item\">\n              {{ province.name }}\n            </view>\n          </picker-view-column>\n          <picker-view-column>\n            <view v-for=\"(city, index) in currentCities\" :key=\"index\" class=\"picker-item\">\n              {{ city.name }}\n            </view>\n          </picker-view-column>\n        </picker-view>\n        <view class=\"picker-actions\">\n          <view class=\"picker-btn cancel\" @click=\"hideCityPicker\">取消</view>\n          <view class=\"picker-btn confirm\" @click=\"confirmCity\">确定</view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport submissionApi from '@/common/api/submission.js'\n\nexport default {\n  data() {\n    return {\n      formData: {\n        province: '',\n        city: '',\n        age: '',\n        gender: '女', // 默认选择女\n        height: '',\n        occupation: '',\n        self_intro: '',\n        partner_requirements: '',\n        accept_long_distance: null,\n        wechat_id: '',\n        category: '交友'\n      },\n      imageList: [],\n      wechatQRCode: '', // 微信二维码\n      submitting: false,\n      genderOptions: [\n        { label: '女', value: '女', icon: 'person-filled' },\n        { label: '男', value: '男', icon: 'person' }\n      ],\n      // 城市数据\n      provinces: [\n        { name: '北京', cities: [{ name: '北京' }] },\n        { name: '上海', cities: [{ name: '上海' }] },\n        { name: '天津', cities: [{ name: '天津' }] },\n        { name: '重庆', cities: [{ name: '重庆' }] },\n        { name: '河北', cities: [\n          { name: '石家庄' }, { name: '唐山' }, { name: '秦皇岛' }, { name: '邯郸' },\n          { name: '邢台' }, { name: '保定' }, { name: '张家口' }, { name: '承德' },\n          { name: '沧州' }, { name: '廊坊' }, { name: '衡水' }\n        ]},\n        { name: '山西', cities: [\n          { name: '太原' }, { name: '大同' }, { name: '阳泉' }, { name: '长治' },\n          { name: '晋城' }, { name: '朔州' }, { name: '晋中' }, { name: '运城' },\n          { name: '忻州' }, { name: '临汾' }, { name: '吕梁' }\n        ]},\n        { name: '内蒙古', cities: [\n          { name: '呼和浩特' }, { name: '包头' }, { name: '乌海' }, { name: '赤峰' },\n          { name: '通辽' }, { name: '鄂尔多斯' }, { name: '呼伦贝尔' }, { name: '巴彦淖尔' },\n          { name: '乌兰察布' }, { name: '兴安盟' }, { name: '锡林郭勒盟' }, { name: '阿拉善盟' }\n        ]},\n        { name: '辽宁', cities: [\n          { name: '沈阳' }, { name: '大连' }, { name: '鞍山' }, { name: '抚顺' },\n          { name: '本溪' }, { name: '丹东' }, { name: '锦州' }, { name: '营口' },\n          { name: '阜新' }, { name: '辽阳' }, { name: '盘锦' }, { name: '铁岭' },\n          { name: '朝阳' }, { name: '葫芦岛' }\n        ]},\n        { name: '吉林', cities: [\n          { name: '长春' }, { name: '吉林' }, { name: '四平' }, { name: '辽源' },\n          { name: '通化' }, { name: '白山' }, { name: '松原' }, { name: '白城' },\n          { name: '延边朝鲜族自治州' }\n        ]},\n        { name: '黑龙江', cities: [\n          { name: '哈尔滨' }, { name: '齐齐哈尔' }, { name: '鸡西' }, { name: '鹤岗' },\n          { name: '双鸭山' }, { name: '大庆' }, { name: '伊春' }, { name: '佳木斯' },\n          { name: '七台河' }, { name: '牡丹江' }, { name: '黑河' }, { name: '绥化' },\n          { name: '大兴安岭地区' }\n        ]},\n        { name: '江苏', cities: [\n          { name: '南京' }, { name: '无锡' }, { name: '徐州' }, { name: '常州' },\n          { name: '苏州' }, { name: '南通' }, { name: '连云港' }, { name: '淮安' },\n          { name: '盐城' }, { name: '扬州' }, { name: '镇江' }, { name: '泰州' }, { name: '宿迁' }\n        ]},\n        { name: '浙江', cities: [\n          { name: '杭州' }, { name: '宁波' }, { name: '温州' }, { name: '嘉兴' },\n          { name: '湖州' }, { name: '绍兴' }, { name: '金华' }, { name: '衢州' },\n          { name: '舟山' }, { name: '台州' }, { name: '丽水' }\n        ]},\n        { name: '安徽', cities: [\n          { name: '合肥' }, { name: '芜湖' }, { name: '蚌埠' }, { name: '淮南' },\n          { name: '马鞍山' }, { name: '淮北' }, { name: '铜陵' }, { name: '安庆' },\n          { name: '黄山' }, { name: '滁州' }, { name: '阜阳' }, { name: '宿州' },\n          { name: '六安' }, { name: '亳州' }, { name: '池州' }, { name: '宣城' }\n        ]},\n        { name: '福建', cities: [\n          { name: '福州' }, { name: '厦门' }, { name: '莆田' }, { name: '三明' },\n          { name: '泉州' }, { name: '漳州' }, { name: '南平' }, { name: '龙岩' }, { name: '宁德' }\n        ]},\n        { name: '江西', cities: [\n          { name: '南昌' }, { name: '景德镇' }, { name: '萍乡' }, { name: '九江' },\n          { name: '新余' }, { name: '鹰潭' }, { name: '赣州' }, { name: '吉安' },\n          { name: '宜春' }, { name: '抚州' }, { name: '上饶' }\n        ]},\n        { name: '山东', cities: [\n          { name: '济南' }, { name: '青岛' }, { name: '淄博' }, { name: '枣庄' },\n          { name: '东营' }, { name: '烟台' }, { name: '潍坊' }, { name: '济宁' },\n          { name: '泰安' }, { name: '威海' }, { name: '日照' }, { name: '临沂' },\n          { name: '德州' }, { name: '聊城' }, { name: '滨州' }, { name: '菏泽' }\n        ]},\n        { name: '河南', cities: [\n          { name: '郑州' }, { name: '开封' }, { name: '洛阳' }, { name: '平顶山' },\n          { name: '安阳' }, { name: '鹤壁' }, { name: '新乡' }, { name: '焦作' },\n          { name: '濮阳' }, { name: '许昌' }, { name: '漯河' }, { name: '三门峡' },\n          { name: '南阳' }, { name: '商丘' }, { name: '信阳' }, { name: '周口' },\n          { name: '驻马店' }, { name: '济源' }\n        ]},\n        { name: '湖北', cities: [\n          { name: '武汉' }, { name: '黄石' }, { name: '十堰' }, { name: '宜昌' },\n          { name: '襄阳' }, { name: '鄂州' }, { name: '荆门' }, { name: '孝感' },\n          { name: '荆州' }, { name: '黄冈' }, { name: '咸宁' }, { name: '随州' },\n          { name: '恩施土家族苗族自治州' }, { name: '仙桃' }, { name: '潜江' }, { name: '天门' }, { name: '神农架林区' }\n        ]},\n        { name: '湖南', cities: [\n          { name: '长沙' }, { name: '株洲' }, { name: '湘潭' }, { name: '衡阳' },\n          { name: '邵阳' }, { name: '岳阳' }, { name: '常德' }, { name: '张家界' },\n          { name: '益阳' }, { name: '郴州' }, { name: '永州' }, { name: '怀化' },\n          { name: '娄底' }, { name: '湘西土家族苗族自治州' }\n        ]},\n        { name: '广东', cities: [\n          { name: '广州' }, { name: '韶关' }, { name: '深圳' }, { name: '珠海' },\n          { name: '汕头' }, { name: '佛山' }, { name: '江门' }, { name: '湛江' },\n          { name: '茂名' }, { name: '肇庆' }, { name: '惠州' }, { name: '梅州' },\n          { name: '汕尾' }, { name: '河源' }, { name: '阳江' }, { name: '清远' },\n          { name: '东莞' }, { name: '中山' }, { name: '潮州' }, { name: '揭阳' }, { name: '云浮' }\n        ]},\n        { name: '广西', cities: [\n          { name: '南宁' }, { name: '柳州' }, { name: '桂林' }, { name: '梧州' },\n          { name: '北海' }, { name: '防城港' }, { name: '钦州' }, { name: '贵港' },\n          { name: '玉林' }, { name: '百色' }, { name: '贺州' }, { name: '河池' },\n          { name: '来宾' }, { name: '崇左' }\n        ]},\n        { name: '海南', cities: [\n          { name: '海口' }, { name: '三亚' }, { name: '三沙' }, { name: '儋州' },\n          { name: '五指山' }, { name: '琼海' }, { name: '文昌' }, { name: '万宁' },\n          { name: '东方' }, { name: '定安' }, { name: '屯昌' }, { name: '澄迈' },\n          { name: '临高' }, { name: '白沙黎族自治县' }, { name: '昌江黎族自治县' },\n          { name: '乐东黎族自治县' }, { name: '陵水黎族自治县' }, { name: '保亭黎族苗族自治县' }, { name: '琼中黎族苗族自治县' }\n        ]},\n        { name: '四川', cities: [\n          { name: '成都' }, { name: '自贡' }, { name: '攀枝花' }, { name: '泸州' },\n          { name: '德阳' }, { name: '绵阳' }, { name: '广元' }, { name: '遂宁' },\n          { name: '内江' }, { name: '乐山' }, { name: '南充' }, { name: '眉山' },\n          { name: '宜宾' }, { name: '广安' }, { name: '达州' }, { name: '雅安' },\n          { name: '巴中' }, { name: '资阳' }, { name: '阿坝藏族羌族自治州' },\n          { name: '甘孜藏族自治州' }, { name: '凉山彝族自治州' }\n        ]},\n        { name: '贵州', cities: [\n          { name: '贵阳' }, { name: '六盘水' }, { name: '遵义' }, { name: '安顺' },\n          { name: '毕节' }, { name: '铜仁' }, { name: '黔西南布依族苗族自治州' },\n          { name: '黔东南苗族侗族自治州' }, { name: '黔南布依族苗族自治州' }\n        ]},\n        { name: '云南', cities: [\n          { name: '昆明' }, { name: '曲靖' }, { name: '玉溪' }, { name: '保山' },\n          { name: '昭通' }, { name: '丽江' }, { name: '普洱' }, { name: '临沧' },\n          { name: '楚雄彝族自治州' }, { name: '红河哈尼族彝族自治州' },\n          { name: '文山壮族苗族自治州' }, { name: '西双版纳傣族自治州' },\n          { name: '大理白族自治州' }, { name: '德宏傣族景颇族自治州' },\n          { name: '怒江傈僳族自治州' }, { name: '迪庆藏族自治州' }\n        ]},\n        { name: '西藏', cities: [\n          { name: '拉萨' }, { name: '日喀则' }, { name: '昌都' }, { name: '林芝' },\n          { name: '山南' }, { name: '那曲' }, { name: '阿里地区' }\n        ]},\n        { name: '陕西', cities: [\n          { name: '西安' }, { name: '铜川' }, { name: '宝鸡' }, { name: '咸阳' },\n          { name: '渭南' }, { name: '延安' }, { name: '汉中' }, { name: '榆林' },\n          { name: '安康' }, { name: '商洛' }\n        ]},\n        { name: '甘肃', cities: [\n          { name: '兰州' }, { name: '嘉峪关' }, { name: '金昌' }, { name: '白银' },\n          { name: '天水' }, { name: '武威' }, { name: '张掖' }, { name: '平凉' },\n          { name: '酒泉' }, { name: '庆阳' }, { name: '定西' }, { name: '陇南' },\n          { name: '临夏回族自治州' }, { name: '甘南藏族自治州' }\n        ]},\n        { name: '青海', cities: [\n          { name: '西宁' }, { name: '海东' }, { name: '海北藏族自治州' },\n          { name: '黄南藏族自治州' }, { name: '海南藏族自治州' },\n          { name: '果洛藏族自治州' }, { name: '玉树藏族自治州' }, { name: '海西蒙古族藏族自治州' }\n        ]},\n        { name: '宁夏', cities: [\n          { name: '银川' }, { name: '石嘴山' }, { name: '吴忠' }, { name: '固原' }, { name: '中卫' }\n        ]},\n        { name: '新疆', cities: [\n          { name: '乌鲁木齐' }, { name: '克拉玛依' }, { name: '吐鲁番' }, { name: '哈密' },\n          { name: '昌吉回族自治州' }, { name: '博尔塔拉蒙古自治州' },\n          { name: '巴音郭楞蒙古自治州' }, { name: '阿克苏地区' }, { name: '克孜勒苏柯尔克孜自治州' },\n          { name: '喀什地区' }, { name: '和田地区' }, { name: '伊犁哈萨克自治州' },\n          { name: '塔城地区' }, { name: '阿勒泰地区' }, { name: '石河子' }, { name: '阿拉尔' },\n          { name: '图木舒克' }, { name: '五家渠' }, { name: '北屯' }, { name: '铁门关' },\n          { name: '双河' }, { name: '可克达拉' }, { name: '昆玉' }, { name: '胡杨河' }\n        ]},\n        { name: '中国香港', cities: [\n          { name: '中西区' }, { name: '湾仔区' }, { name: '东区' }, { name: '南区' },\n          { name: '油尖旺区' }, { name: '深水埗区' }, { name: '九龙城区' }, { name: '黄大仙区' },\n          { name: '观塘区' }, { name: '荃湾区' }, { name: '屯门区' }, { name: '元朗区' },\n          { name: '北区' }, { name: '大埔区' }, { name: '沙田区' }, { name: '西贡区' },\n          { name: '葵青区' }, { name: '离岛区' }\n        ]},\n        { name: '中国澳门', cities: [\n          { name: '澳门半岛' }, { name: '氹仔' }, { name: '路环' }\n        ]},\n        { name: '中国台湾', cities: [\n          { name: '台北市' }, { name: '新北市' }, { name: '桃园市' }, { name: '台中市' },\n          { name: '台南市' }, { name: '高雄市' }, { name: '基隆市' }, { name: '新竹市' },\n          { name: '嘉义市' }, { name: '新竹县' }, { name: '苗栗县' }, { name: '彰化县' },\n          { name: '南投县' }, { name: '云林县' }, { name: '嘉义县' }, { name: '屏东县' },\n          { name: '宜兰县' }, { name: '花莲县' }, { name: '台东县' }, { name: '澎湖县' },\n          { name: '金门县' }, { name: '连江县' }\n        ]}\n      ],\n      pickerValue: [0, 0],\n      selectedProvinceIndex: 0,\n      selectedCityIndex: 0\n    }\n  },\n  computed: {\n    selectedCity() {\n      if (this.formData.province && this.formData.city) {\n        return `${this.formData.province}-${this.formData.city}`\n      }\n      return ''\n    },\n    currentCities() {\n      return this.provinces[this.selectedProvinceIndex]?.cities || []\n    }\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n    \n    selectGender(value) {\n      this.formData.gender = value\n    },\n    \n    showCityPicker() {\n      this.$refs.cityPopup.open()\n    },\n    \n    hideCityPicker() {\n      this.$refs.cityPopup.close()\n    },\n    \n    onPickerChange(e) {\n      this.pickerValue = e.detail.value\n      this.selectedProvinceIndex = e.detail.value[0]\n      this.selectedCityIndex = e.detail.value[1]\n    },\n    \n    confirmCity() {\n      const province = this.provinces[this.selectedProvinceIndex]\n      const city = this.currentCities[this.selectedCityIndex]\n\n      if (!province || !city) {\n        uni.showToast({\n          title: '请选择有效的城市',\n          icon: 'none'\n        })\n        return\n      }\n\n      this.formData.province = province.name\n      this.formData.city = city.name\n      this.hideCityPicker()\n    },\n    \n    chooseImage() {\n      const remainingCount = 8 - this.imageList.length\n      uni.chooseImage({\n        count: remainingCount,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          this.imageList.push(...res.tempFilePaths)\n        }\n      })\n    },\n    \n    removeImage(index) {\n      this.imageList.splice(index, 1)\n    },\n    \n    previewImage(index) {\n      uni.previewImage({\n        urls: this.imageList,\n        current: index\n      })\n    },\n\n    chooseQRCode() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          this.wechatQRCode = res.tempFilePaths[0]\n        }\n      })\n    },\n\n    removeQRCode() {\n      this.wechatQRCode = ''\n    },\n\n    previewQRCode() {\n      uni.previewImage({\n        urls: [this.wechatQRCode],\n        current: 0\n      })\n    },\n    \n    validateForm() {\n      if (!this.formData.province || !this.formData.city) {\n        uni.showToast({ title: '请选择所在城市', icon: 'none' })\n        return false\n      }\n      \n      if (!this.formData.age || this.formData.age < 16 || this.formData.age > 60) {\n        uni.showToast({ title: '请输入正确的年龄（16-60岁）', icon: 'none' })\n        return false\n      }\n      \n      if (!this.formData.height || this.formData.height < 140 || this.formData.height > 220) {\n        uni.showToast({ title: '请输入正确的身高（140-220cm）', icon: 'none' })\n        return false\n      }\n      \n      if (!this.formData.occupation.trim()) {\n        uni.showToast({ title: '请填写职业/工作状态', icon: 'none' })\n        return false\n      }\n      \n      if (!this.formData.self_intro.trim()) {\n        uni.showToast({ title: '请填写自我介绍', icon: 'none' })\n        return false\n      }\n      \n      if (!this.formData.partner_requirements.trim()) {\n        uni.showToast({ title: '请填写搭子要求', icon: 'none' })\n        return false\n      }\n      \n      if (this.formData.accept_long_distance === null || this.formData.accept_long_distance === undefined) {\n        uni.showToast({ title: '请选择是否接受异地', icon: 'none' })\n        return false\n      }\n      \n      if (this.imageList.length === 0) {\n        uni.showToast({ title: '请至少上传一张照片', icon: 'none' })\n        return false\n      }\n      \n      return true\n    },\n    \n    async submitForm() {\n      if (this.submitting) return\n      \n      if (!this.validateForm()) return\n      \n      this.submitting = true\n      \n      try {\n        // 上传图片\n        const imageUrls = await this.uploadImages()\n\n        // 上传微信二维码\n        let wechatQRCodeUrl = ''\n        if (this.wechatQRCode) {\n          wechatQRCodeUrl = await this.uploadSingleImage(this.wechatQRCode)\n        }\n\n        // 准备提交数据\n        const submitData = {\n          ...this.formData,\n          cover_image: imageUrls[0], // 第一张作为主图\n          images: imageUrls.slice(1).join(','), // 其余图片\n          wechat_qrcode: wechatQRCodeUrl, // 微信二维码\n          age: parseInt(this.formData.age),\n          height: parseInt(this.formData.height),\n          status: 'pending', // 用户投稿默认为待审核状态\n          is_visible: false // 用户投稿默认不可见，需要管理员审核\n        }\n        \n        // 提交表单\n        const res = await submissionApi.createSubmission(submitData)\n        \n        if (res.code === 200) {\n          uni.showToast({\n            title: '投稿提交成功，等待审核',\n            icon: 'success'\n          })\n          \n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        } else {\n          throw new Error(res.message || '提交失败')\n        }\n        \n      } catch (error) {\n        console.error('提交投稿失败:', error)\n        uni.showToast({\n          title: error.message || '提交失败，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.submitting = false\n      }\n    },\n    \n    async uploadImages() {\n      const uploadPromises = this.imageList.map(imagePath => {\n        return new Promise((resolve, reject) => {\n          uni.uploadFile({\n            url: `${require('@/config.js').baseUrl}/upload/image`,\n            filePath: imagePath,\n            name: 'file',\n            header: {\n              'Authorization': `Bearer ${uni.getStorageSync('token')}`\n            },\n            success: (res) => {\n              try {\n                const data = JSON.parse(res.data)\n                if (data.code === 200) {\n                  resolve(data.data.url)\n                } else {\n                  reject(new Error(data.message || '图片上传失败'))\n                }\n              } catch (e) {\n                reject(new Error('图片上传响应解析失败'))\n              }\n            },\n            fail: (error) => {\n              reject(error)\n            }\n          })\n        })\n      })\n      \n      return Promise.all(uploadPromises)\n    },\n\n    async uploadSingleImage(imagePath) {\n      return new Promise((resolve, reject) => {\n        uni.uploadFile({\n          url: `${require('@/config.js').baseUrl}/upload/image`,\n          filePath: imagePath,\n          name: 'file',\n          header: {\n            'Authorization': `Bearer ${uni.getStorageSync('token')}`\n          },\n          success: (res) => {\n            try {\n              const data = JSON.parse(res.data)\n              if (data.code === 200) {\n                resolve(data.data.url)\n              } else {\n                reject(new Error(data.message || '图片上传失败'))\n              }\n            } catch (e) {\n              reject(new Error('图片上传响应解析失败'))\n            }\n          },\n          fail: (error) => {\n            reject(error)\n          }\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.create-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);\n}\n\n.custom-navbar {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  @include glass-effect(0.6);\n  padding-top: var(--status-bar-height);\n\n  .navbar-content {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 24rpx;\n\n    .navbar-left, .navbar-right {\n      width: 60rpx;\n      display: flex;\n      justify-content: center;\n    }\n\n    .navbar-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n  }\n}\n\n.form-content {\n  height: calc(100vh - 120rpx);\n}\n\n.form-container {\n  padding: 20rpx;\n  padding-bottom: 40rpx;\n}\n\n.form-card {\n  @include glass-effect(0.6);\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n\n  .card-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24rpx;\n    gap: 8rpx;\n\n    .card-title {\n      font-size: 24rpx;\n      font-weight: 700;\n      color: $soul-gray-700;\n    }\n\n    .card-subtitle {\n      font-size: 20rpx;\n      color: $soul-gray-500;\n      margin-left: auto;\n    }\n  }\n}\n\n.form-item {\n  margin-bottom: 24rpx;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n\n  .form-label-wrapper {\n    display: flex;\n    align-items: center;\n    margin-bottom: 12rpx;\n    gap: 4rpx;\n  }\n\n  .form-label {\n    font-size: 24rpx;\n    font-weight: 600;\n    color: $soul-gray-700;\n  }\n\n  .required-mark {\n    font-size: 24rpx;\n    color: #ef4444;\n    font-weight: 600;\n  }\n\n  .form-input {\n    width: 100%;\n    height: 80rpx;\n    padding: 0 20rpx;\n    border: 2rpx solid #e5e7eb;\n    border-radius: 12rpx;\n    background: rgba(255, 255, 255, 0.8);\n    font-size: 26rpx;\n    color: $soul-gray-800;\n    box-sizing: border-box;\n\n    &:focus {\n      border-color: #f78ca0;\n      box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.1);\n    }\n  }\n\n  .form-textarea {\n    width: 100%;\n    min-height: 160rpx;\n    padding: 20rpx;\n    border: 2rpx solid #e5e7eb;\n    border-radius: 12rpx;\n    background: rgba(255, 255, 255, 0.8);\n    font-size: 26rpx;\n    color: $soul-gray-800;\n    box-sizing: border-box;\n    resize: none;\n\n    &:focus {\n      border-color: #f78ca0;\n      box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.1);\n    }\n  }\n\n  .char-count {\n    text-align: right;\n    font-size: 20rpx;\n    color: $soul-gray-500;\n    margin-top: 8rpx;\n  }\n}\n\n.city-selector {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 80rpx;\n  padding: 0 20rpx;\n  border: 2rpx solid #e5e7eb;\n  border-radius: 12rpx;\n  background: rgba(255, 255, 255, 0.8);\n\n  .city-text {\n    font-size: 26rpx;\n    color: $soul-gray-800;\n\n    &.placeholder {\n      color: #999;\n    }\n  }\n}\n\n.gender-options {\n  display: flex;\n  gap: 16rpx;\n\n  .gender-option {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 8rpx;\n    padding: 20rpx;\n    border: 2rpx solid #e5e7eb;\n    border-radius: 12rpx;\n    background: rgba(255, 255, 255, 0.8);\n    transition: all 0.3s ease;\n\n    &.active {\n      border-color: #f78ca0;\n      background: rgba(247, 140, 160, 0.1);\n    }\n\n    .gender-text {\n      font-size: 24rpx;\n      color: $soul-gray-700;\n      font-weight: 500;\n    }\n  }\n}\n\n.distance-options {\n  display: flex;\n  gap: 16rpx;\n\n  .distance-option {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    gap: 8rpx;\n    padding: 20rpx;\n    border: 2rpx solid #e5e7eb;\n    border-radius: 12rpx;\n    background: rgba(255, 255, 255, 0.8);\n    transition: all 0.3s ease;\n\n    &.active {\n      border-color: #10b981;\n      background: rgba(16, 185, 129, 0.1);\n    }\n\n    .distance-text {\n      font-size: 24rpx;\n      color: $soul-gray-700;\n      font-weight: 500;\n    }\n  }\n}\n\n.image-upload-container {\n  .image-grid {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: 16rpx;\n  }\n\n  .image-item {\n    position: relative;\n    width: 100%;\n    height: 200rpx;\n    border-radius: 12rpx;\n    overflow: hidden;\n\n    .upload-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .image-actions {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.3);\n      display: flex;\n      align-items: flex-start;\n      justify-content: space-between;\n      padding: 8rpx;\n\n      .main-badge {\n        background: #f78ca0;\n        color: white;\n        font-size: 18rpx;\n        padding: 4rpx 8rpx;\n        border-radius: 8rpx;\n      }\n\n      .delete-btn {\n        width: 32rpx;\n        height: 32rpx;\n        background: rgba(0, 0, 0, 0.5);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n  }\n\n  .upload-btn {\n    width: 100%;\n    height: 200rpx;\n    border: 2rpx dashed #d1d5db;\n    border-radius: 12rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n    background: rgba(255, 255, 255, 0.5);\n\n    .upload-text {\n      font-size: 22rpx;\n      color: #999;\n    }\n  }\n}\n\n.submit-section {\n  margin-top: 40rpx;\n  text-align: center;\n\n  .submit-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12rpx;\n    height: 88rpx;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 44rpx;\n    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);\n    transition: all 0.3s ease;\n\n    &:active:not(.disabled) {\n      transform: translateY(2rpx) scale(0.98);\n    }\n\n    &.disabled {\n      opacity: 0.6;\n    }\n\n    .loading-icon {\n      animation: spin 1s linear infinite;\n    }\n\n    .submit-text {\n      font-size: 30rpx;\n      color: white;\n      font-weight: 700;\n    }\n  }\n\n  .tips-text {\n    margin-top: 16rpx;\n    font-size: 22rpx;\n    color: $soul-gray-500;\n    line-height: 1.4;\n  }\n}\n\n.city-picker {\n  background: white;\n  border-radius: 20rpx 20rpx 0 0;\n\n  .picker-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 24rpx;\n    border-bottom: 1rpx solid #f0f0f0;\n\n    .picker-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n  }\n\n  .picker-view {\n    height: 400rpx;\n  }\n\n  .picker-item {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 80rpx;\n    font-size: 26rpx;\n    color: $soul-gray-700;\n  }\n\n  .picker-actions {\n    display: flex;\n    border-top: 1rpx solid #f0f0f0;\n\n    .picker-btn {\n      flex: 1;\n      height: 88rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 28rpx;\n      font-weight: 600;\n\n      &.cancel {\n        color: $soul-gray-600;\n        border-right: 1rpx solid #f0f0f0;\n      }\n\n      &.confirm {\n        color: #f78ca0;\n      }\n    }\n  }\n}\n\n.qrcode-upload-container {\n  .qrcode-preview {\n    position: relative;\n    width: 200rpx;\n    height: 200rpx;\n    border-radius: 12rpx;\n    overflow: hidden;\n\n    .qrcode-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .qrcode-actions {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.3);\n      display: flex;\n      align-items: flex-start;\n      justify-content: flex-end;\n      padding: 8rpx;\n\n      .qrcode-delete {\n        width: 32rpx;\n        height: 32rpx;\n        background: rgba(0, 0, 0, 0.5);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n  }\n\n  .qrcode-upload-btn {\n    width: 200rpx;\n    height: 200rpx;\n    border: 2rpx dashed #d1d5db;\n    border-radius: 12rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n    background: rgba(255, 255, 255, 0.5);\n\n    .upload-text {\n      font-size: 22rpx;\n      color: #999;\n    }\n  }\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./create.vue?vue&type=style&index=0&id=3d953676&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./create.vue?vue&type=style&index=0&id=3d953676&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752120110333\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}