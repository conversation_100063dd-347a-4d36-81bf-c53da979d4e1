<view class="detail-page data-v-a398096a"><view class="custom-navbar data-v-a398096a"><view class="navbar-content data-v-a398096a"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left data-v-a398096a" bindtap="__e"><uni-icons vue-id="184bef1b-1" type="left" size="32" color="#FF6B9D" class="data-v-a398096a" bind:__l="__l"></uni-icons></view><text class="navbar-title data-v-a398096a">投稿详情</text><view class="navbar-right data-v-a398096a"><view data-event-opts="{{[['tap',[['toggleFavorite',['$event']]]]]}}" class="favorite-btn data-v-a398096a" bindtap="__e"><uni-icons vue-id="184bef1b-2" type="{{isFavorited?'heart-filled':'heart'}}" size="28" color="{{isFavorited?'#ff4757':'#FF6B9D'}}" class="data-v-a398096a" bind:__l="__l"></uni-icons></view></view></view></view><block wx:if="{{submission}}"><scroll-view class="detail-content data-v-a398096a" scroll-y="true"><view class="image-section data-v-a398096a"><swiper class="image-swiper data-v-a398096a" indicator-dots="{{$root.g0>1}}" indicator-color="rgba(255, 255, 255, 0.5)" indicator-active-color="#f78ca0" autoplay="{{false}}" circular="{{true}}"><block wx:for="{{imageList}}" wx:for-item="image" wx:for-index="index" wx:key="index"><swiper-item class="data-v-a398096a"><image class="detail-image data-v-a398096a" src="{{image}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" bindtap="__e"></image></swiper-item></block></swiper><view class="blur-background data-v-a398096a"><image class="blur-image data-v-a398096a" src="{{imageList[0]}}" mode="aspectFill"></image></view><block wx:if="{{submission&&submission.is_top}}"><view class="pinned-badge data-v-a398096a"><uni-icons vue-id="184bef1b-3" type="star-filled" size="16" color="#FFFFFF" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="pinned-text data-v-a398096a">精选</text></view></block></view><view class="info-card data-v-a398096a"><view class="card-header data-v-a398096a"><uni-icons vue-id="184bef1b-4" type="person-filled" size="24" color="#f78ca0" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="card-title data-v-a398096a">基本信息</text></view><view class="info-grid data-v-a398096a"><view class="info-item data-v-a398096a"><uni-icons vue-id="184bef1b-5" type="location" size="16" color="#f78ca0" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="info-value data-v-a398096a">{{$root.m0}}</text></view><view class="info-item data-v-a398096a"><uni-icons vue-id="184bef1b-6" type="calendar" size="16" color="#a6c1ee" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="info-value data-v-a398096a">{{submission.age+"岁"}}</text></view><view class="info-item data-v-a398096a"><uni-icons vue-id="184bef1b-7" type="heart" size="16" color="{{genderColor}}" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="info-value data-v-a398096a" style="{{'color:'+(genderColor)+';'}}">{{submission.gender}}</text></view><view class="info-item data-v-a398096a"><uni-icons vue-id="184bef1b-8" type="person" size="16" color="#feb47b" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="info-value data-v-a398096a">{{submission.height+"cm"}}</text></view><view class="info-item data-v-a398096a"><uni-icons vue-id="184bef1b-9" type="briefcase" size="16" color="#c8a8e9" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="info-value data-v-a398096a">{{submission.occupation}}</text></view><view class="info-item data-v-a398096a"><uni-icons vue-id="184bef1b-10" type="map" size="16" color="#10b981" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="{{['info-value','data-v-a398096a',(submission.accept_long_distance)?'accept-distance':'']}}">{{''+(submission.accept_long_distance?'接受异地':'不接受异地')+''}}</text></view><view class="info-item data-v-a398096a"><uni-icons vue-id="184bef1b-11" type="paperplane" size="16" color="#9E9E9E" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="info-value data-v-a398096a">{{submission.submission_code||submission.id}}</text></view></view></view><block wx:if="{{submission.self_intro}}"><view class="content-card data-v-a398096a"><view class="card-header data-v-a398096a"><uni-icons vue-id="184bef1b-12" type="chat" size="24" color="#a6c1ee" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="card-title data-v-a398096a">自我介绍</text></view><view class="content-text data-v-a398096a"><text class="description-text data-v-a398096a">{{submission.self_intro}}</text></view></view></block><block wx:if="{{submission.partner_requirements}}"><view class="content-card data-v-a398096a"><view class="card-header data-v-a398096a"><uni-icons vue-id="184bef1b-13" type="heart" size="24" color="#feb47b" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="card-title data-v-a398096a">搭子要求</text></view><view class="content-text data-v-a398096a"><text class="description-text data-v-a398096a">{{submission.partner_requirements}}</text></view></view></block><view class="related-submissions data-v-a398096a"><view class="section-header data-v-a398096a"><uni-icons vue-id="184bef1b-14" type="star" size="24" color="#f78ca0" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="section-title data-v-a398096a">更多搭子</text></view><scroll-view class="submissions-scroll data-v-a398096a" scroll-x="true"><view class="submissions-container data-v-a398096a"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['goToSubmission',['$0'],[[['relatedSubmissions','id',item.$orig.id,'id']]]]]]]}}" class="mini-submission-card data-v-a398096a" bindtap="__e"><block wx:if="{{item.$orig.is_top}}"><view class="mini-top-badge data-v-a398096a"><text class="mini-top-text data-v-a398096a">置顶</text></view></block><view class="mini-cover-container data-v-a398096a"><image class="mini-cover data-v-a398096a" src="{{item.$orig.cover_image}}" mode="aspectFill"></image></view><view class="mini-info-container data-v-a398096a"><view class="mini-info-row data-v-a398096a"><view class="mini-info-item data-v-a398096a"><uni-icons vue-id="{{'184bef1b-15-'+__i0__}}" type="location" size="12" color="#f78ca0" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="mini-info-text data-v-a398096a">{{item.m1}}</text></view><view class="mini-info-item data-v-a398096a"><uni-icons vue-id="{{'184bef1b-16-'+__i0__}}" type="calendar" size="12" color="#a6c1ee" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="mini-info-text data-v-a398096a">{{item.$orig.age+"岁"}}</text></view></view><view class="mini-info-row data-v-a398096a"><view class="mini-info-item data-v-a398096a"><uni-icons vue-id="{{'184bef1b-17-'+__i0__}}" type="{{item.m2}}" size="12" color="{{item.m3}}" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="mini-info-text data-v-a398096a" style="{{'color:'+(item.m4)+';'}}">{{item.$orig.gender}}</text></view><view class="mini-info-item data-v-a398096a"><uni-icons vue-id="{{'184bef1b-18-'+__i0__}}" type="person" size="12" color="#feb47b" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="mini-info-text data-v-a398096a">{{item.$orig.height+"cm"}}</text></view></view></view></view></block></view></scroll-view></view><view class="time-card data-v-a398096a"><view class="time-info data-v-a398096a"><uni-icons vue-id="184bef1b-19" type="clock" size="20" color="#9E9E9E" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="time-text data-v-a398096a">{{"发布于 "+(submission&&submission.create_datetime?$root.m5:'未知时间')}}</text></view></view><view class="contact-section data-v-a398096a"><view data-event-opts="{{[['tap',[['showContactInfo',['$event']]]]]}}" class="contact-btn data-v-a398096a" bindtap="__e"><view class="btn-icon data-v-a398096a"><uni-icons vue-id="184bef1b-20" type="chat" size="24" color="#FFFFFF" class="data-v-a398096a" bind:__l="__l"></uni-icons></view><text class="btn-text data-v-a398096a">获取联系方式</text><view class="btn-sparkle data-v-a398096a">✨</view></view></view></scroll-view></block><uni-popup vue-id="184bef1b-21" type="center" background-color="rgba(0,0,0,0.4)" data-ref="contactPopup" class="data-v-a398096a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="contact-modal data-v-a398096a"><view class="modal-bg-decoration data-v-a398096a"><view class="bg-circle bg-circle-1 data-v-a398096a"></view><view class="bg-circle bg-circle-2 data-v-a398096a"></view><view class="bg-circle bg-circle-3 data-v-a398096a"></view></view><view data-event-opts="{{[['tap',[['hideContactInfo',['$event']]]]]}}" class="modal-close data-v-a398096a" bindtap="__e"><uni-icons vue-id="{{('184bef1b-22')+','+('184bef1b-21')}}" type="close" size="40" color="#ffffff" class="data-v-a398096a" bind:__l="__l"></uni-icons></view><view class="modal-header data-v-a398096a"><view class="header-icon data-v-a398096a"><uni-icons vue-id="{{('184bef1b-23')+','+('184bef1b-21')}}" type="heart-filled" size="48" color="#FF6B9D" class="data-v-a398096a" bind:__l="__l"></uni-icons></view><text class="modal-title data-v-a398096a">获取联系方式</text><text class="modal-subtitle data-v-a398096a">让美好的相遇开始吧～</text></view><view class="modal-content data-v-a398096a"><view class="steps-guide data-v-a398096a"><view class="step-item data-v-a398096a"><view class="step-number data-v-a398096a">1</view><text class="step-text data-v-a398096a">复制投稿ID</text></view><view class="step-arrow data-v-a398096a"><uni-icons vue-id="{{('184bef1b-24')+','+('184bef1b-21')}}" type="right" size="24" color="#FF6B9D" class="data-v-a398096a" bind:__l="__l"></uni-icons></view><view class="step-item data-v-a398096a"><view class="step-number data-v-a398096a">2</view><text class="step-text data-v-a398096a">扫码添加客服</text></view><view class="step-arrow data-v-a398096a"><uni-icons vue-id="{{('184bef1b-25')+','+('184bef1b-21')}}" type="right" size="24" color="#FF6B9D" class="data-v-a398096a" bind:__l="__l"></uni-icons></view><view class="step-item data-v-a398096a"><view class="step-number data-v-a398096a">3</view><text class="step-text data-v-a398096a">发送ID获取联系</text></view></view><view class="contact-id-section data-v-a398096a"><view class="section-header data-v-a398096a"><uni-icons vue-id="{{('184bef1b-26')+','+('184bef1b-21')}}" type="paperplane" size="32" color="#FF6B9D" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="section-title data-v-a398096a">投稿ID</text></view><view class="contact-id-card data-v-a398096a"><view class="id-display data-v-a398096a"><text class="contact-id data-v-a398096a">{{submission&&submission.submission_code||submission&&submission.id||'暂无'}}</text></view><view data-event-opts="{{[['tap',[['copyContactId',['$event']]]]]}}" class="copy-btn data-v-a398096a" bindtap="__e"><uni-icons vue-id="{{('184bef1b-27')+','+('184bef1b-21')}}" type="copy" size="28" color="#ffffff" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="copy-text data-v-a398096a">复制</text></view></view></view><view class="service-qr-section data-v-a398096a"><view class="section-header data-v-a398096a"><uni-icons vue-id="{{('184bef1b-28')+','+('184bef1b-21')}}" type="chat" size="32" color="#FF6B9D" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="section-title data-v-a398096a">客服微信</text></view><view class="qr-container data-v-a398096a"><view class="qr-frame data-v-a398096a"><image class="service-qr data-v-a398096a" src="{{customerServiceQr||'/static/images/service-qr.png'}}" mode="aspectFit"></image><view class="qr-corners data-v-a398096a"><view class="corner corner-tl data-v-a398096a"></view><view class="corner corner-tr data-v-a398096a"></view><view class="corner corner-bl data-v-a398096a"></view><view class="corner corner-br data-v-a398096a"></view></view></view><text class="service-tip data-v-a398096a">{{contactPopupContent}}</text></view></view><view class="warm-tips data-v-a398096a"><view class="tips-header data-v-a398096a"><uni-icons vue-id="{{('184bef1b-29')+','+('184bef1b-21')}}" type="info" size="28" color="#FFA726" class="data-v-a398096a" bind:__l="__l"></uni-icons><text class="tips-title data-v-a398096a">温馨提示</text></view><text class="tips-content data-v-a398096a">为了保护用户隐私，联系方式需通过客服获取。请耐心等待客服回复哦～</text></view></view></view></uni-popup><block wx:if="{{loading}}"><view class="loading-overlay data-v-a398096a"><uni-load-more vue-id="184bef1b-30" status="loading" class="data-v-a398096a" bind:__l="__l"></uni-load-more></view></block></view>