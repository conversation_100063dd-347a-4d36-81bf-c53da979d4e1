<view class="create-page data-v-3d953676"><view class="custom-navbar data-v-3d953676"><view class="navbar-content data-v-3d953676"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left data-v-3d953676" bindtap="__e"><uni-icons vue-id="75121950-1" type="left" size="32" color="#FF6B9D" class="data-v-3d953676" bind:__l="__l"></uni-icons></view><text class="navbar-title data-v-3d953676">发布投稿</text><view class="navbar-right data-v-3d953676"></view></view></view><scroll-view class="form-content data-v-3d953676" scroll-y="true"><view class="form-container data-v-3d953676"><view class="form-card data-v-3d953676"><view class="card-header data-v-3d953676"><uni-icons vue-id="75121950-2" type="person-filled" size="24" color="#f78ca0" class="data-v-3d953676" bind:__l="__l"></uni-icons><text class="card-title data-v-3d953676">基本信息</text></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">所在城市 *</text><view data-event-opts="{{[['tap',[['showCityPicker',['$event']]]]]}}" class="city-selector data-v-3d953676" bindtap="__e"><text class="{{['city-text','data-v-3d953676',(!selectedCity)?'placeholder':'']}}">{{''+(selectedCity||'请选择所在城市')+''}}</text><uni-icons vue-id="75121950-3" type="right" size="16" color="#999" class="data-v-3d953676" bind:__l="__l"></uni-icons></view></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">年龄 *</text><input class="form-input data-v-3d953676" type="number" placeholder="请输入年龄（16-60岁）" maxlength="2" data-event-opts="{{[['input',[['__set_model',['$0','age','$event',[]],['formData']]]]]}}" value="{{formData.age}}" bindinput="__e"/></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">性别 *</text><view class="gender-options data-v-3d953676"><block wx:for="{{genderOptions}}" wx:for-item="option" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectGender',['$0'],[[['genderOptions','',index,'value']]]]]]]}}" class="{{['gender-option','data-v-3d953676',(formData.gender===option.value)?'active':'']}}" bindtap="__e"><uni-icons vue-id="{{'75121950-4-'+index}}" type="{{option.icon}}" size="20" color="{{formData.gender===option.value?'#f78ca0':'#999'}}" class="data-v-3d953676" bind:__l="__l"></uni-icons><text class="gender-text data-v-3d953676">{{option.label}}</text></view></block></view></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">身高 *</text><input class="form-input data-v-3d953676" type="number" placeholder="请输入身高（cm）" maxlength="3" data-event-opts="{{[['input',[['__set_model',['$0','height','$event',[]],['formData']]]]]}}" value="{{formData.height}}" bindinput="__e"/></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">职业/工作状态 *</text><input class="form-input data-v-3d953676" type="text" placeholder="如：学生、程序员、设计师等" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','occupation','$event',[]],['formData']]]]]}}" value="{{formData.occupation}}" bindinput="__e"/></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">是否接受异地 *</text><view class="distance-options data-v-3d953676"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['distance-option','data-v-3d953676',(formData.accept_long_distance===true)?'active':'']}}" bindtap="__e"><uni-icons vue-id="75121950-5" type="checkmarkempty" size="16" color="{{formData.accept_long_distance===true?'#10b981':'#999'}}" class="data-v-3d953676" bind:__l="__l"></uni-icons><text class="distance-text data-v-3d953676">接受异地</text></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['distance-option','data-v-3d953676',(formData.accept_long_distance===false)?'active':'']}}" bindtap="__e"><uni-icons vue-id="75121950-6" type="closeempty" size="16" color="{{formData.accept_long_distance===false?'#ef4444':'#999'}}" class="data-v-3d953676" bind:__l="__l"></uni-icons><text class="distance-text data-v-3d953676">仅限同城</text></view></view></view></view><view class="form-card data-v-3d953676"><view class="card-header data-v-3d953676"><uni-icons vue-id="75121950-7" type="chat" size="24" color="#a6c1ee" class="data-v-3d953676" bind:__l="__l"></uni-icons><text class="card-title data-v-3d953676">详细信息</text></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">自我介绍 *</text><textarea class="form-textarea data-v-3d953676" placeholder="简单介绍一下自己，让大家更了解你～" maxlength="500" show-confirm-bar="{{false}}" data-event-opts="{{[['input',[['__set_model',['$0','self_intro','$event',[]],['formData']]]]]}}" value="{{formData.self_intro}}" bindinput="__e"></textarea><view class="char-count data-v-3d953676">{{$root.g0+"/500"}}</view></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">搭子要求 *</text><textarea class="form-textarea data-v-3d953676" placeholder="描述一下你希望找到什么样的搭子～" maxlength="500" show-confirm-bar="{{false}}" data-event-opts="{{[['input',[['__set_model',['$0','partner_requirements','$event',[]],['formData']]]]]}}" value="{{formData.partner_requirements}}" bindinput="__e"></textarea><view class="char-count data-v-3d953676">{{$root.g1+"/500"}}</view></view></view><view class="form-card data-v-3d953676"><view class="card-header data-v-3d953676"><uni-icons vue-id="75121950-8" type="image" size="24" color="#feb47b" class="data-v-3d953676" bind:__l="__l"></uni-icons><text class="card-title data-v-3d953676">上传照片</text><text class="card-subtitle data-v-3d953676">（1-8张，第一张为主图）</text></view><view class="image-upload-container data-v-3d953676"><view class="image-grid data-v-3d953676"><block wx:for="{{imageList}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" class="image-item data-v-3d953676" bindtap="__e"><image class="upload-image data-v-3d953676" src="{{image}}" mode="aspectFill"></image><view class="image-actions data-v-3d953676"><block wx:if="{{index===0}}"><view class="main-badge data-v-3d953676">主图</view></block><view data-event-opts="{{[['tap',[['removeImage',[index]]]]]}}" class="delete-btn data-v-3d953676" catchtap="__e"><uni-icons vue-id="{{'75121950-9-'+index}}" type="close" size="16" color="#fff" class="data-v-3d953676" bind:__l="__l"></uni-icons></view></view></view></block><block wx:if="{{$root.g2<8}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-btn data-v-3d953676" bindtap="__e"><uni-icons vue-id="75121950-10" type="plus" size="32" color="#999" class="data-v-3d953676" bind:__l="__l"></uni-icons><text class="upload-text data-v-3d953676">添加照片</text></view></block></view></view></view><view class="form-card data-v-3d953676"><view class="card-header data-v-3d953676"><uni-icons vue-id="75121950-11" type="chat" size="24" color="#c8a8e9" class="data-v-3d953676" bind:__l="__l"></uni-icons><text class="card-title data-v-3d953676">联系方式</text><text class="card-subtitle data-v-3d953676">（选填）</text></view><view class="form-item data-v-3d953676"><text class="form-label data-v-3d953676">微信号</text><input class="form-input data-v-3d953676" type="text" placeholder="请输入微信号" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','wechat_id','$event',[]],['formData']]]]]}}" value="{{formData.wechat_id}}" bindinput="__e"/></view></view><view class="submit-section data-v-3d953676"><view data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" class="{{['submit-btn','data-v-3d953676',(submitting)?'disabled':'']}}" bindtap="__e"><block wx:if="{{submitting}}"><view class="loading-icon data-v-3d953676"><uni-icons vue-id="75121950-12" type="spinner-cycle" size="24" color="#fff" class="data-v-3d953676" bind:__l="__l"></uni-icons></view></block><text class="submit-text data-v-3d953676">{{submitting?'提交中...':'发布投稿'}}</text></view><view class="tips-text data-v-3d953676"><text class="data-v-3d953676">投稿将在审核通过后展示，请确保信息真实有效</text></view></view></view></scroll-view><uni-popup vue-id="75121950-13" type="bottom" background-color="#ffffff" data-ref="cityPopup" class="data-v-3d953676 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="city-picker data-v-3d953676"><view class="picker-header data-v-3d953676"><text class="picker-title data-v-3d953676">选择所在城市</text><view data-event-opts="{{[['tap',[['hideCityPicker',['$event']]]]]}}" class="picker-close data-v-3d953676" bindtap="__e"><uni-icons vue-id="{{('75121950-14')+','+('75121950-13')}}" type="close" size="24" color="#999" class="data-v-3d953676" bind:__l="__l"></uni-icons></view></view><picker-view class="picker-view data-v-3d953676" value="{{pickerValue}}" data-event-opts="{{[['change',[['onPickerChange',['$event']]]]]}}" bindchange="__e"><picker-view-column class="data-v-3d953676"><block wx:for="{{provinces}}" wx:for-item="province" wx:for-index="index" wx:key="index"><view class="picker-item data-v-3d953676">{{''+province.name+''}}</view></block></picker-view-column><picker-view-column class="data-v-3d953676"><block wx:for="{{currentCities}}" wx:for-item="city" wx:for-index="index" wx:key="index"><view class="picker-item data-v-3d953676">{{''+city.name+''}}</view></block></picker-view-column></picker-view><view class="picker-actions data-v-3d953676"><view data-event-opts="{{[['tap',[['hideCityPicker',['$event']]]]]}}" class="picker-btn cancel data-v-3d953676" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmCity',['$event']]]]]}}" class="picker-btn confirm data-v-3d953676" bindtap="__e">确定</view></view></view></uni-popup></view>