<template>
  <view class="mine-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="left" size="32" color="#FF6B9D"></uni-icons>
        </view>
        <text class="navbar-title">个人中心</text>
        <view class="navbar-right">
          <view class="action-btn" @click="goToSettings">
            <uni-icons type="gear" size="24" color="#f78ca0"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <scroll-view scroll-y="true" class="mine-content">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="user-avatar">
          <image
            :src="displayUserInfo.avatar"
            class="avatar-image"
            mode="aspectFill"
            @click="chooseAvatar"
          />
          <view class="avatar-edit">
            <uni-icons type="camera" size="16" color="#fff"></uni-icons>
          </view>
        </view>

        <view class="user-info">
          <text class="user-name">{{ displayUserInfo.nickname }}</text>
          <text class="user-desc">{{ displayUserInfo.desc }}</text>
        </view>
        
        <view class="user-stats">
          <view class="stat-item">
            <text class="stat-number">{{ userStats.submissions }}</text>
            <text class="stat-label">投稿</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userStats.favorites }}</text>
            <text class="stat-label">收藏</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userStats.views }}</text>
            <text class="stat-label">浏览</text>
          </view>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="menu-section">
        <view class="menu-card">
          <view class="menu-item" @click="goToMySubmissions">
            <view class="menu-icon">
              <uni-icons type="compose" size="24" color="#f78ca0"></uni-icons>
            </view>
            <view class="menu-content">
              <text class="menu-title">我的投稿</text>
              <text class="menu-desc">管理我发布的投稿</text>
            </view>
            <view class="menu-badge" v-if="userStats.pendingSubmissions > 0">
              <text class="badge-text">{{ userStats.pendingSubmissions }}</text>
            </view>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <view class="menu-item" @click="goToMyFavorites">
            <view class="menu-icon">
              <uni-icons type="heart" size="24" color="#feb47b"></uni-icons>
            </view>
            <view class="menu-content">
              <text class="menu-title">我的收藏</text>
              <text class="menu-desc">查看收藏的投稿</text>
            </view>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <view class="menu-item" @click="goToProfile">
            <view class="menu-icon">
              <uni-icons type="person" size="24" color="#a6c1ee"></uni-icons>
            </view>
            <view class="menu-content">
              <text class="menu-title">个人资料</text>
              <text class="menu-desc">编辑个人信息</text>
            </view>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>
        </view>

        <view class="menu-card">
          <view class="menu-item" @click="goToHelp">
            <view class="menu-icon">
              <uni-icons type="help" size="24" color="#c8a8e9"></uni-icons>
            </view>
            <view class="menu-content">
              <text class="menu-title">帮助与反馈</text>
              <text class="menu-desc">使用帮助和意见反馈</text>
            </view>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <view class="menu-item" @click="goToAbout">
            <view class="menu-icon">
              <uni-icons type="info" size="24" color="#10b981"></uni-icons>
            </view>
            <view class="menu-content">
              <text class="menu-title">关于我们</text>
              <text class="menu-desc">了解更多信息</text>
            </view>
            <view class="menu-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 快捷操作 -->
      <view class="quick-actions">
        <view class="action-card" @click="goToCreate">
          <view class="action-icon">
            <uni-icons type="plus" size="32" color="#fff"></uni-icons>
          </view>
          <text class="action-text">发布投稿</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import userApi from '@/common/api/user.js'

export default {
  data() {
    return {
      userStats: {
        submissions: 0,
        favorites: 0,
        views: 0,
        pendingSubmissions: 0
      }
    }
  },
  computed: {
    ...mapGetters('user', ['userInfo', 'isLoggedIn', 'userNickname', 'userAvatar']),

    displayUserInfo() {
      return {
        nickname: this.userNickname,
        avatar: this.userAvatar,
        desc: this.userInfo?.desc || '这个人很懒，什么都没留下～'
      }
    }
  },
  onLoad() {
    this.checkLoginStatus()
    this.loadUserStats()
  },
  onShow() {
    // 页面显示时刷新数据
    this.loadUserStats()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },

    async checkLoginStatus() {
      if (!this.isLoggedIn) {
        // 未登录，显示登录提示
        this.showLoginModal()
      } else {
        // 已登录，获取最新用户信息
        try {
          await this.$store.dispatch('user/getUserInfo')
        } catch (error) {
          console.error('获取用户信息失败:', error)
        }
      }
    },

    showLoginModal() {
      uni.showModal({
        title: '登录提示',
        content: '需要登录后才能使用个人中心功能',
        confirmText: '立即登录',
        cancelText: '稍后再说',
        success: (res) => {
          if (res.confirm) {
            this.performWxLogin()
          } else {
            uni.navigateBack()
          }
        }
      })
    },

    async performWxLogin() {
      try {
        // 获取微信登录code
        const loginRes = await uni.login({ provider: 'weixin' })

        // 获取用户信息
        const userInfoRes = await uni.getUserProfile({
          desc: '用于完善用户资料'
        })

        // 调用登录接口
        await this.$store.dispatch('user/wxLogin', {
          code: loginRes.code,
          userInfo: userInfoRes.userInfo
        })

        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })

      } catch (error) {
        console.error('微信登录失败:', error)
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
        uni.navigateBack()
      }
    },

    async loadUserStats() {
      if (!this.isLoggedIn) {
        this.userStats = {
          submissions: 0,
          favorites: 0,
          views: 0,
          pendingSubmissions: 0
        }
        return
      }

      try {
        const res = await userApi.getUserStats()
        if (res.code === 200) {
          this.userStats = res.data
        }
      } catch (error) {
        console.error('获取用户统计失败:', error)
        // 使用模拟数据
        this.userStats = {
          submissions: 3,
          favorites: 12,
          views: 156,
          pendingSubmissions: 1
        }
      }
    },

    chooseAvatar() {
      if (!this.isLoggedIn) {
        this.showLoginModal()
        return
      }

      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadAvatar(res.tempFilePaths[0])
        }
      })
    },

    async uploadAvatar(filePath) {
      try {
        uni.showLoading({ title: '上传中...' })

        const res = await userApi.uploadAvatar(filePath)
        if (res.code === 200) {
          // 更新用户信息
          await this.$store.dispatch('user/updateUserInfo', {
            avatar: res.data.avatar_url
          })

          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('头像上传失败:', error)
        uni.showToast({
          title: '头像上传失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    goToMySubmissions() {
      uni.navigateTo({
        url: '/pages/mine/submissions'
      })
    },

    goToMyFavorites() {
      if (!this.isLoggedIn) {
        this.showLoginModal()
        return
      }

      uni.navigateTo({
        url: '/pages/mine/favorites'
      })
    },

    goToProfile() {
      uni.navigateTo({
        url: '/pages/mine/profile'
      })
    },

    goToSettings() {
      uni.navigateTo({
        url: '/pages/mine/settings'
      })
    },

    goToHelp() {
      uni.navigateTo({
        url: '/pages/mine/help'
      })
    },

    goToAbout() {
      uni.navigateTo({
        url: '/pages/mine/about'
      })
    },

    goToCreate() {
      uni.navigateTo({
        url: '/pages/submission/create'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mine-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;

    .navbar-left, .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: center;
    }

    .navbar-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }

    .action-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(0.95);
      }
    }
  }
}

.mine-content {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.user-card {
  @include glass-effect(0.7);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);

  .user-avatar {
    position: relative;
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 24rpx;

    .avatar-image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 4rpx solid rgba(247, 140, 160, 0.2);
    }

    .avatar-edit {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 36rpx;
      height: 36rpx;
      background: #f78ca0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 3rpx solid white;
    }
  }

  .user-info {
    margin-bottom: 32rpx;

    .user-name {
      display: block;
      font-size: 32rpx;
      font-weight: 700;
      color: $soul-gray-800;
      margin-bottom: 8rpx;
    }

    .user-desc {
      font-size: 24rpx;
      color: $soul-gray-500;
      line-height: 1.4;
    }
  }

  .user-stats {
    display: flex;
    justify-content: space-around;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;

      .stat-number {
        font-size: 36rpx;
        font-weight: 700;
        color: #f78ca0;
      }

      .stat-label {
        font-size: 22rpx;
        color: $soul-gray-600;
      }
    }
  }
}

.menu-section {
  .menu-card {
    @include glass-effect(0.6);
    border-radius: 20rpx;
    margin-bottom: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);

    .menu-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      border-bottom: 1rpx solid rgba(0,0,0,0.05);
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: rgba(247, 140, 160, 0.05);
      }

      .menu-icon {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12rpx;
        margin-right: 16rpx;
      }

      .menu-content {
        flex: 1;

        .menu-title {
          display: block;
          font-size: 26rpx;
          font-weight: 600;
          color: $soul-gray-800;
          margin-bottom: 4rpx;
        }

        .menu-desc {
          font-size: 22rpx;
          color: $soul-gray-500;
        }
      }

      .menu-badge {
        background: #f78ca0;
        border-radius: 12rpx;
        padding: 4rpx 8rpx;
        margin-right: 12rpx;

        .badge-text {
          font-size: 18rpx;
          color: white;
          font-weight: 600;
        }
      }

      .menu-arrow {
        opacity: 0.6;
      }
    }
  }
}

.quick-actions {
  margin-top: 20rpx;

  .action-card {
    @include glass-effect(0.7);
    border-radius: 20rpx;
    padding: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx) scale(0.98);
    }

    .action-icon {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .action-text {
      font-size: 28rpx;
      color: white;
      font-weight: 600;
    }
  }
}
</style>
