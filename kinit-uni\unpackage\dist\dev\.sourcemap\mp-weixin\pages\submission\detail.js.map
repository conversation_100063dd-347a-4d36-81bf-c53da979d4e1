{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/detail.vue?3ab4", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/detail.vue?9b1f", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/detail.vue?62d7", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/detail.vue?672c", "uni-app:///pages/submission/detail.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/detail.vue?0333", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/detail.vue?80c4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "submissionId", "submission", "loading", "imageList", "relatedSubmissions", "systemConfig", "isFavorited", "favoriteLoading", "computed", "genderColor", "safeSubmission", "customerServiceQr", "contactPopupContent", "onLoad", "methods", "loadSystemConfig", "systemApi", "res", "console", "loadSubmissionDetail", "submissionApi", "uni", "title", "icon", "processImages", "previewImage", "urls", "current", "showContactInfo", "hideContactInfo", "copyContactId", "success", "toggleFavorite", "content", "confirmText", "performWxLogin", "provider", "loginRes", "desc", "userInfoRes", "code", "userInfo", "mockLogin", "mockUserInfo", "id", "nickname", "avatar", "gender", "mockToken", "checkFavoriteStatus", "goBack", "url", "loadRelatedSubmissions", "page", "size", "filtered", "shuffled", "goToSubmission", "getGenderIcon", "getGenderColor", "formatTime", "formatLocation"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAmoB,CAAgB,wpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6TvpB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;MACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACAC;gBACA;kBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAH;gBAEA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAI;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;gBACAG;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;MAEA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QAAA;QACA;UAAA;QAAA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEAC;MACAJ;QACAK;QACAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QACAT;UACAC;UACAC;QACA;QACA;MACA;MAEA;MAEAF;QACAtB;QACAgC;UACAV;YACAC;YACAC;UACA;QACA;MACA;IACA;IAIAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAX;kBACAC;kBACAW;kBACAC;kBACAH;oBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAAA;gBAAA,OAGAX;cAAA;gBAAAH;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAI;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAL;gBACAG;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAUAd;kBAAAe;gBAAA;cAAA;gBAAAC;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAIAhB;kBACAiB;gBACA;cAAA;gBAFAC;gBAAA,IAIAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAIA;kBACAC;kBACAC;gBACA;cAAA;gBAEApB;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAL;;gBAEA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAG;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAT;gBACA,GAEA;gBACAU,wCAEA;gBACA;gBACA;gBAEA3B;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA0B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA7B;cAAA;gBAAAH;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgC;MACA;MACA7B;QACA8B;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAhC;kBACAiC;kBACAC;gBACA;cAAA;gBAHArC;gBAKA;kBACA;kBACAsC;oBAAA;kBAAA;kBACAC;oBAAA;kBAAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAtC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuC;MACApC;QACA8B;MACA;IACA;IAEAO;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEAC;MACA;MACA;MACA;MAEA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACptBA;AAAA;AAAA;AAAA;AAAkuC,CAAgB,+rCAAG,EAAC,C;;;;;;;;;;;ACAtvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/submission/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/submission/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=a398096a&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=a398096a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a398096a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/submission/detail.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=a398096a&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-load-more/components/uni-load-more/uni-load-more\" */ \"@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.submission ? _vm.imageList.length : null\n  var g1 = _vm.submission ? _vm.imageList.length : null\n  var m0 = _vm.submission ? _vm.formatLocation(_vm.submission) : null\n  var l0 = _vm.submission\n    ? _vm.__map(_vm.relatedSubmissions, function (item, __i0__) {\n        var $orig = _vm.__get_orig(item)\n        var m1 = _vm.formatLocation(item)\n        var m2 = _vm.getGenderIcon(item.gender)\n        var m3 = _vm.getGenderColor(item.gender)\n        var m4 = _vm.getGenderColor(item.gender)\n        return {\n          $orig: $orig,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n        }\n      })\n    : null\n  var m5 =\n    _vm.submission && _vm.submission && _vm.submission.create_datetime\n      ? _vm.formatTime(_vm.submission.create_datetime)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        l0: l0,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"detail-page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-content\">\n        <view class=\"navbar-left\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n        </view>\n        <text class=\"navbar-title\">投稿详情</text>\n        <view class=\"navbar-right\">\n          <view class=\"favorite-btn\" @click=\"toggleFavorite\">\n            <uni-icons\n              :type=\"isFavorited ? 'heart-filled' : 'heart'\"\n              size=\"28\"\n              :color=\"isFavorited ? '#ff4757' : '#FF6B9D'\"\n            ></uni-icons>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <scroll-view scroll-y=\"true\" class=\"detail-content\" v-if=\"submission\">\n      <!-- 图片轮播 -->\n      <view class=\"image-section\">\n        <swiper\n          class=\"image-swiper\"\n          :indicator-dots=\"imageList.length > 1\"\n          indicator-color=\"rgba(255, 255, 255, 0.5)\"\n          indicator-active-color=\"#f78ca0\"\n          :autoplay=\"false\"\n          :circular=\"true\"\n        >\n          <swiper-item v-for=\"(image, index) in imageList\" :key=\"index\">\n            <image\n              :src=\"image\"\n              class=\"detail-image\"\n              mode=\"aspectFit\"\n              @click=\"previewImage(index)\"\n            />\n          </swiper-item>\n        </swiper>\n\n        <!-- 模糊背景填充 -->\n        <view class=\"blur-background\" v-if=\"imageList.length > 0\">\n          <image\n            :src=\"imageList[0]\"\n            class=\"blur-image\"\n            mode=\"aspectFill\"\n          />\n        </view>\n\n        <!-- 置顶标签 -->\n        <view v-if=\"submission && submission.is_top\" class=\"pinned-badge\">\n          <uni-icons type=\"star-filled\" size=\"16\" color=\"#FFFFFF\"></uni-icons>\n          <text class=\"pinned-text\">精选</text>\n        </view>\n      </view>\n\n      <!-- 基本信息卡片 -->\n      <view class=\"info-card\">\n        <view class=\"card-header\">\n          <uni-icons type=\"person-filled\" size=\"24\" color=\"#f78ca0\"></uni-icons>\n          <text class=\"card-title\">基本信息</text>\n        </view>\n\n        <view class=\"info-grid\">\n          <!-- 城市（省份-城市） -->\n          <view class=\"info-item\">\n            <uni-icons type=\"location\" size=\"16\" color=\"#f78ca0\"></uni-icons>\n            <text class=\"info-value\">{{ formatLocation(submission) }}</text>\n          </view>\n\n          <!-- 年龄 -->\n          <view class=\"info-item\">\n            <uni-icons type=\"calendar\" size=\"16\" color=\"#a6c1ee\"></uni-icons>\n            <text class=\"info-value\">{{ submission.age }}岁</text>\n          </view>\n\n          <!-- 性别 -->\n          <view class=\"info-item\">\n            <uni-icons type=\"heart\" size=\"16\" :color=\"genderColor\"></uni-icons>\n            <text class=\"info-value\" :style=\"{ color: genderColor }\">{{ submission.gender }}</text>\n          </view>\n\n          <!-- 身高 -->\n          <view class=\"info-item\">\n            <uni-icons type=\"person\" size=\"16\" color=\"#feb47b\"></uni-icons>\n            <text class=\"info-value\">{{ submission.height }}cm</text>\n          </view>\n\n          <!-- 工作状态 -->\n          <view class=\"info-item\">\n            <uni-icons type=\"briefcase\" size=\"16\" color=\"#c8a8e9\"></uni-icons>\n            <text class=\"info-value\">{{ submission.occupation }}</text>\n          </view>\n\n          <!-- 是否接受异地 -->\n          <view class=\"info-item\">\n            <uni-icons type=\"map\" size=\"16\" color=\"#10b981\"></uni-icons>\n            <text class=\"info-value\" :class=\"{ 'accept-distance': submission.accept_long_distance }\">\n              {{ submission.accept_long_distance ? '接受异地' : '不接受异地' }}\n            </text>\n          </view>\n\n          <!-- 投稿编号 -->\n          <view class=\"info-item\">\n            <uni-icons type=\"paperplane\" size=\"16\" color=\"#9E9E9E\"></uni-icons>\n            <text class=\"info-value\">{{ submission.submission_code || submission.id }}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 自我介绍卡片 -->\n      <view v-if=\"submission.self_intro\" class=\"content-card\">\n        <view class=\"card-header\">\n          <uni-icons type=\"chat\" size=\"24\" color=\"#a6c1ee\"></uni-icons>\n          <text class=\"card-title\">自我介绍</text>\n        </view>\n        <view class=\"content-text\">\n          <text class=\"description-text\">{{ submission.self_intro }}</text>\n        </view>\n      </view>\n\n      <!-- 搭子要求卡片 -->\n      <view v-if=\"submission.partner_requirements\" class=\"content-card\">\n        <view class=\"card-header\">\n          <uni-icons type=\"heart\" size=\"24\" color=\"#feb47b\"></uni-icons>\n          <text class=\"card-title\">搭子要求</text>\n        </view>\n        <view class=\"content-text\">\n          <text class=\"description-text\">{{ submission.partner_requirements }}</text>\n        </view>\n      </view>\n\n      <!-- 其他投稿推荐 -->\n      <view class=\"related-submissions\">\n        <view class=\"section-header\">\n          <uni-icons type=\"star\" size=\"24\" color=\"#f78ca0\"></uni-icons>\n          <text class=\"section-title\">更多搭子</text>\n        </view>\n        <scroll-view scroll-x=\"true\" class=\"submissions-scroll\">\n          <view class=\"submissions-container\">\n            <view\n              v-for=\"item in relatedSubmissions\"\n              :key=\"item.id\"\n              class=\"mini-submission-card\"\n              @click=\"goToSubmission(item.id)\"\n            >\n              <!-- 置顶标签 -->\n              <view v-if=\"item.is_top\" class=\"mini-top-badge\">\n                <text class=\"mini-top-text\">置顶</text>\n              </view>\n\n              <!-- 封面图片 -->\n              <view class=\"mini-cover-container\">\n                <image :src=\"item.cover_image\" class=\"mini-cover\" mode=\"aspectFill\" />\n              </view>\n\n              <!-- 信息内容 -->\n              <view class=\"mini-info-container\">\n                <view class=\"mini-info-row\">\n                  <!-- 城市（省份-城市） -->\n                  <view class=\"mini-info-item\">\n                    <uni-icons type=\"location\" size=\"12\" color=\"#f78ca0\"></uni-icons>\n                    <text class=\"mini-info-text\">{{ formatLocation(item) }}</text>\n                  </view>\n\n                  <!-- 年龄 -->\n                  <view class=\"mini-info-item\">\n                    <uni-icons type=\"calendar\" size=\"12\" color=\"#a6c1ee\"></uni-icons>\n                    <text class=\"mini-info-text\">{{ item.age }}岁</text>\n                  </view>\n                </view>\n\n                <view class=\"mini-info-row\">\n                  <!-- 性别 -->\n                  <view class=\"mini-info-item\">\n                    <uni-icons :type=\"getGenderIcon(item.gender)\" size=\"12\" :color=\"getGenderColor(item.gender)\"></uni-icons>\n                    <text class=\"mini-info-text\" :style=\"{ color: getGenderColor(item.gender) }\">{{ item.gender }}</text>\n                  </view>\n\n                  <!-- 身高 -->\n                  <view class=\"mini-info-item\">\n                    <uni-icons type=\"person\" size=\"12\" color=\"#feb47b\"></uni-icons>\n                    <text class=\"mini-info-text\">{{ item.height }}cm</text>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <!-- 发布时间 -->\n      <view class=\"time-card\">\n        <view class=\"time-info\">\n          <uni-icons type=\"clock\" size=\"20\" color=\"#9E9E9E\"></uni-icons>\n          <text class=\"time-text\">发布于 {{ submission && submission.create_datetime ? formatTime(submission.create_datetime) : '未知时间' }}</text>\n        </view>\n      </view>\n\n      <!-- 获取联系方式按钮 -->\n      <view class=\"contact-section\">\n        <view class=\"contact-btn\" @click=\"showContactInfo\">\n          <view class=\"btn-icon\">\n            <uni-icons type=\"chat\" size=\"24\" color=\"#FFFFFF\"></uni-icons>\n          </view>\n          <text class=\"btn-text\">获取联系方式</text>\n          <view class=\"btn-sparkle\">✨</view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 联系方式弹窗 -->\n    <uni-popup ref=\"contactPopup\" type=\"center\" background-color=\"rgba(0,0,0,0.4)\">\n      <view class=\"contact-modal\">\n        <!-- 装饰性背景 -->\n        <view class=\"modal-bg-decoration\">\n          <view class=\"bg-circle bg-circle-1\"></view>\n          <view class=\"bg-circle bg-circle-2\"></view>\n          <view class=\"bg-circle bg-circle-3\"></view>\n        </view>\n\n        <!-- 关闭按钮 -->\n        <view class=\"modal-close\" @click=\"hideContactInfo\">\n          <uni-icons type=\"close\" size=\"40\" color=\"#ffffff\"></uni-icons>\n        </view>\n\n        <!-- 标题区域 -->\n        <view class=\"modal-header\">\n          <view class=\"header-icon\">\n            <uni-icons type=\"heart-filled\" size=\"48\" color=\"#FF6B9D\"></uni-icons>\n          </view>\n          <text class=\"modal-title\">获取联系方式</text>\n          <text class=\"modal-subtitle\">让美好的相遇开始吧～</text>\n        </view>\n\n        <view class=\"modal-content\">\n          <!-- 步骤指引 -->\n          <view class=\"steps-guide\">\n            <view class=\"step-item\">\n              <view class=\"step-number\">1</view>\n              <text class=\"step-text\">复制投稿ID</text>\n            </view>\n            <view class=\"step-arrow\">\n              <uni-icons type=\"right\" size=\"24\" color=\"#FF6B9D\"></uni-icons>\n            </view>\n            <view class=\"step-item\">\n              <view class=\"step-number\">2</view>\n              <text class=\"step-text\">扫码添加客服</text>\n            </view>\n            <view class=\"step-arrow\">\n              <uni-icons type=\"right\" size=\"24\" color=\"#FF6B9D\"></uni-icons>\n            </view>\n            <view class=\"step-item\">\n              <view class=\"step-number\">3</view>\n              <text class=\"step-text\">发送ID获取联系</text>\n            </view>\n          </view>\n\n          <!-- 投稿ID区域 -->\n          <view class=\"contact-id-section\">\n            <view class=\"section-header\">\n              <uni-icons type=\"paperplane\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n              <text class=\"section-title\">投稿ID</text>\n            </view>\n            <view class=\"contact-id-card\">\n              <view class=\"id-display\">\n                <text class=\"contact-id\">{{ submission && submission.submission_code || (submission && submission.id) || '暂无' }}</text>\n              </view>\n              <view class=\"copy-btn\" @click=\"copyContactId\">\n                <uni-icons type=\"copy\" size=\"28\" color=\"#ffffff\"></uni-icons>\n                <text class=\"copy-text\">复制</text>\n              </view>\n            </view>\n          </view>\n\n          <!-- 客服二维码区域 -->\n          <view class=\"service-qr-section\">\n            <view class=\"section-header\">\n              <uni-icons type=\"chat\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n              <text class=\"section-title\">客服微信</text>\n            </view>\n            <view class=\"qr-container\">\n              <view class=\"qr-frame\">\n                <image :src=\"customerServiceQr || '/static/images/service-qr.png'\" class=\"service-qr\" mode=\"aspectFit\"></image>\n                <view class=\"qr-corners\">\n                  <view class=\"corner corner-tl\"></view>\n                  <view class=\"corner corner-tr\"></view>\n                  <view class=\"corner corner-bl\"></view>\n                  <view class=\"corner corner-br\"></view>\n                </view>\n              </view>\n              <text class=\"service-tip\">{{ contactPopupContent }}</text>\n            </view>\n          </view>\n\n          <!-- 温馨提示 -->\n          <view class=\"warm-tips\">\n            <view class=\"tips-header\">\n              <uni-icons type=\"info\" size=\"28\" color=\"#FFA726\"></uni-icons>\n              <text class=\"tips-title\">温馨提示</text>\n            </view>\n            <text class=\"tips-content\">为了保护用户隐私，联系方式需通过客服获取。请耐心等待客服回复哦～</text>\n          </view>\n        </view>\n      </view>\n    </uni-popup>\n    \n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-overlay\">\n      <uni-load-more status=\"loading\"></uni-load-more>\n    </view>\n  </view>\n</template>\n\n<script>\nimport submissionApi from '@/common/api/submission.js'\nimport systemApi from '@/common/api/system.js'\n\nexport default {\n  data() {\n    return {\n      submissionId: null,\n      submission: null,\n      loading: true,\n      imageList: [],\n      relatedSubmissions: [],\n      systemConfig: null,\n      isFavorited: false,\n      favoriteLoading: false\n    }\n  },\n  computed: {\n    genderColor() {\n      switch (this.submission && this.submission.gender) {\n        case '男':\n          return '#87CEEB'\n        case '女':\n          return '#FF6B9D'\n        default:\n          return '#C8A8E9'\n      }\n    },\n\n    // 安全的submission数据访问\n    safeSubmission() {\n      return this.submission || {}\n    },\n\n    // 客服设置\n    customerServiceQr() {\n      const qrPath = this.systemConfig?.customer_service_qr\n      if (qrPath && !qrPath.startsWith('http')) {\n        // 如果是相对路径，添加API基础URL\n        const config = require('@/config.js')\n        return config.baseUrl + qrPath\n      }\n      return qrPath || '/static/images/service-qr.png'\n    },\n\n    contactPopupContent() {\n      return this.systemConfig?.contact_popup_content || '扫码添加客服微信，发送投稿ID获取联系方式'\n    }\n  },\n  onLoad(options) {\n    if (options.id) {\n      this.submissionId = parseInt(options.id)\n      this.loadSystemConfig()\n      this.loadSubmissionDetail()\n      this.loadRelatedSubmissions()\n    }\n  },\n\n  methods: {\n    async loadSystemConfig() {\n      try {\n        const res = await systemApi.getSystemConfig()\n        console.log('系统配置API响应:', res)\n        if (res.code === 200) {\n          this.systemConfig = res.data\n          console.log('客服二维码配置:', this.systemConfig.customer_service_qr)\n        }\n      } catch (error) {\n        console.error('加载系统配置失败:', error)\n      }\n    },\n\n    async loadSubmissionDetail() {\n      this.loading = true\n      \n      try {\n        const res = await submissionApi.getSubmissionDetail(this.submissionId)\n        \n        if (res.code === 200) {\n          this.submission = res.data\n          this.processImages()\n          // 检查收藏状态\n          this.checkFavoriteStatus()\n        } else {\n          uni.showToast({\n            title: '加载失败',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('加载投稿详情失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    processImages() {\n      if (!this.submission) return\n\n      this.imageList = []\n\n      // 添加封面图片\n      if (this.submission.cover_image) {\n        this.imageList.push(this.submission.cover_image)\n      }\n\n      // 添加其他图片\n      if (this.submission.images) {\n        const additionalImages = this.submission.images.split(',').filter(img => img.trim())\n        this.imageList.push(...additionalImages)\n      }\n\n      // 如果没有图片，添加默认占位图\n      if (this.imageList.length === 0) {\n        this.imageList.push('/static/images/placeholder.jpg')\n      }\n    },\n    \n    previewImage(index) {\n      uni.previewImage({\n        urls: this.imageList,\n        current: index\n      })\n    },\n    \n    showContactInfo() {\n      this.$refs.contactPopup.open()\n    },\n    \n    hideContactInfo() {\n      this.$refs.contactPopup.close()\n    },\n    \n    copyContactId() {\n      if (!this.submission) {\n        uni.showToast({\n          title: '数据加载中，请稍后',\n          icon: 'none'\n        })\n        return\n      }\n\n      const submissionId = this.submission.submission_code || (this.submission.id && this.submission.id.toString()) || '暂无'\n\n      uni.setClipboardData({\n        data: submissionId,\n        success: () => {\n          uni.showToast({\n            title: '投稿ID已复制到剪贴板',\n            icon: 'success'\n          })\n        }\n      })\n    },\n    \n\n    \n    async toggleFavorite() {\n      // 检查登录状态\n      if (!this.$store.getters['user/isLoggedIn']) {\n        uni.showModal({\n          title: '登录提示',\n          content: '需要登录后才能收藏投稿',\n          confirmText: '立即登录',\n          success: (res) => {\n            if (res.confirm) {\n              this.performWxLogin()\n            }\n          }\n        })\n        return\n      }\n\n      if (this.favoriteLoading) return\n\n      this.favoriteLoading = true\n\n      try {\n        const res = await submissionApi.toggleFavorite(this.submissionId)\n        if (res.code === 200) {\n          this.isFavorited = !this.isFavorited\n          uni.showToast({\n            title: this.isFavorited ? '收藏成功' : '取消收藏',\n            icon: 'success'\n          })\n        } else {\n          throw new Error(res.message || '操作失败')\n        }\n      } catch (error) {\n        console.error('收藏操作失败:', error)\n        uni.showToast({\n          title: error.message || '操作失败，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.favoriteLoading = false\n      }\n    },\n\n    async performWxLogin() {\n      try {\n        // 检查是否在开发环境\n        // #ifdef H5\n        // 开发环境使用模拟登录\n        await this.mockLogin()\n        return\n        // #endif\n\n        // 获取微信登录code\n        const loginRes = await uni.login({ provider: 'weixin' })\n\n        if (!loginRes.code) {\n          throw new Error('获取微信登录code失败')\n        }\n\n        // 获取用户信息\n        const userInfoRes = await uni.getUserProfile({\n          desc: '用于完善用户资料'\n        })\n\n        if (!userInfoRes.userInfo) {\n          throw new Error('获取用户信息失败')\n        }\n\n        // 调用登录接口\n        await this.$store.dispatch('user/wxLogin', {\n          code: loginRes.code,\n          userInfo: userInfoRes.userInfo\n        })\n\n        uni.showToast({\n          title: '登录成功',\n          icon: 'success'\n        })\n\n        // 登录成功后检查收藏状态\n        this.checkFavoriteStatus()\n\n      } catch (error) {\n        console.error('微信登录失败:', error)\n\n        // 如果微信登录失败，尝试模拟登录\n        try {\n          await this.mockLogin()\n        } catch (mockError) {\n          console.error('模拟登录也失败:', mockError)\n          uni.showToast({\n            title: '登录失败，请重试',\n            icon: 'none'\n          })\n        }\n      }\n    },\n\n    // 模拟登录（开发环境使用）\n    async mockLogin() {\n      try {\n        // 模拟用户信息\n        const mockUserInfo = {\n          id: 1,\n          nickname: '测试用户',\n          avatar: '/static/images/default-avatar.png',\n          gender: 1,\n          desc: '这是一个测试用户'\n        }\n\n        // 模拟token\n        const mockToken = 'mock_token_' + Date.now()\n\n        // 直接设置到store\n        this.$store.commit('user/SET_TOKEN', mockToken)\n        this.$store.commit('user/SET_USER_INFO', mockUserInfo)\n\n        uni.showToast({\n          title: '模拟登录成功',\n          icon: 'success'\n        })\n\n        // 登录成功后检查收藏状态\n        this.checkFavoriteStatus()\n\n      } catch (error) {\n        throw new Error('模拟登录失败: ' + error.message)\n      }\n    },\n\n    async checkFavoriteStatus() {\n      if (!this.$store.getters['user/isLoggedIn'] || !this.submissionId) {\n        return\n      }\n\n      try {\n        const res = await submissionApi.checkFavoriteStatus(this.submissionId)\n        if (res.code === 200) {\n          this.isFavorited = res.data.is_favorited\n        }\n      } catch (error) {\n        console.error('检查收藏状态失败:', error)\n      }\n    },\n    \n    goBack() {\n      // 点击主页按钮跳转并重置所有操作\n      uni.navigateTo({\n        url: '/pages/index'\n      })\n    },\n    \n    async loadRelatedSubmissions() {\n      try {\n        const res = await submissionApi.getSubmissionList({\n          page: 1,\n          size: 10\n        })\n\n        if (res.code === 200) {\n          // 过滤掉当前投稿，随机选择5个\n          const filtered = res.data.items.filter(item => item.id !== this.submissionId)\n          const shuffled = filtered.sort(() => 0.5 - Math.random())\n          this.relatedSubmissions = shuffled.slice(0, 5)\n        }\n      } catch (error) {\n        console.error('加载相关投稿失败:', error)\n      }\n    },\n\n    goToSubmission(id) {\n      uni.navigateTo({\n        url: `/pages/submission/detail?id=${id}`\n      })\n    },\n\n    getGenderIcon(gender) {\n      switch (gender) {\n        case '男':\n          return 'person'\n        case '女':\n          return 'person-filled'\n        default:\n          return 'person'\n      }\n    },\n\n    getGenderColor(gender) {\n      switch (gender) {\n        case '男':\n          return '#87ceeb'\n        case '女':\n          return '#f78ca0'\n        default:\n          return '#c8a8e9'\n      }\n    },\n\n    formatTime(timeStr) {\n      const date = new Date(timeStr)\n      const now = new Date()\n      const diff = now - date\n\n      const minutes = Math.floor(diff / (1000 * 60))\n      const hours = Math.floor(diff / (1000 * 60 * 60))\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n\n      if (minutes < 60) {\n        return `${minutes}分钟前`\n      } else if (hours < 24) {\n        return `${hours}小时前`\n      } else if (days < 7) {\n        return `${days}天前`\n      } else {\n        return date.toLocaleDateString()\n      }\n    },\n\n    // 格式化地理位置显示\n    formatLocation(item) {\n      if (!item) return ''\n\n      // 直辖市列表\n      const municipalities = ['北京', '上海', '天津', '重庆']\n\n      // 如果有省份和城市信息\n      if (item.province && item.city) {\n        // 如果是直辖市，只显示城市名\n        if (municipalities.includes(item.province)) {\n          return item.city\n        }\n        // 其他情况显示省份-城市\n        return `${item.province}-${item.city}`\n      }\n\n      // 只有城市信息\n      if (item.city) {\n        return item.city\n      }\n\n      // 只有省份信息\n      if (item.province) {\n        return item.province\n      }\n\n      return '未知'\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.detail-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);\n}\n\n.custom-navbar {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  @include glass-effect(0.6);\n  padding-top: var(--status-bar-height);\n\n  .navbar-content {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 24rpx;\n\n    .navbar-left, .navbar-right {\n      width: 60rpx;\n      display: flex;\n      justify-content: center;\n    }\n\n    .navbar-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n\n    .favorite-btn {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      transition: all 0.3s ease;\n\n      &:active {\n        background: rgba(255, 255, 255, 0.2);\n        transform: scale(0.95);\n      }\n    }\n  }\n}\n\n.detail-content {\n  min-height: calc(100vh - 200rpx);\n  padding-bottom: 40rpx;\n}\n\n.image-section {\n  position: relative;\n  height: 400rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  border-radius: 0 0 24rpx 24rpx;\n\n  .blur-background {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 1;\n\n    .blur-image {\n      width: 100%;\n      height: 100%;\n      filter: blur(20rpx);\n      opacity: 0.3;\n    }\n  }\n\n  .image-swiper {\n    position: relative;\n    height: 100%;\n    z-index: 2;\n\n    .detail-image {\n      width: 100%;\n      height: 100%;\n      object-fit: contain;\n    }\n  }\n\n  .pinned-badge {\n    position: absolute;\n    top: 16rpx;\n    right: 16rpx;\n    display: flex;\n    align-items: center;\n    background: linear-gradient(to right, #ff7e5f, #feb47b);\n    border-radius: 32rpx;\n    padding: 6rpx 12rpx;\n    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);\n    z-index: 10;\n\n    .pinned-text {\n      font-size: 18rpx;\n      color: $soul-white;\n      font-weight: 700;\n      margin-left: 4rpx;\n    }\n  }\n}\n\n.info-card, .content-card, .time-card {\n  @include glass-effect(0.6);\n  border-radius: 20rpx;\n  margin: 0 20rpx 16rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n}\n\n.info-card, .content-card {\n  .card-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20rpx;\n    gap: 8rpx;\n\n    .card-title {\n      font-size: 24rpx;\n      font-weight: 700;\n      color: $soul-gray-700;\n    }\n  }\n\n\n\n  .info-grid {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 12rpx;\n\n    .info-item {\n      display: flex;\n      align-items: center;\n      gap: 8rpx;\n      background: rgba(255, 255, 255, 0.8);\n      border-radius: 12rpx;\n      padding: 12rpx 16rpx;\n      box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);\n\n\n\n      .info-value {\n        font-size: 22rpx;\n        font-weight: 600;\n        color: $soul-gray-700;\n\n        &.accept-distance {\n          color: #10b981;\n        }\n      }\n    }\n  }\n\n  .content-text {\n    .description-text {\n      font-size: 24rpx;\n      color: $soul-gray-700;\n      line-height: 1.5;\n    }\n  }\n}\n\n.time-card {\n  .time-info {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8rpx;\n\n    .time-text {\n      font-size: 20rpx;\n      color: $soul-gray-500;\n    }\n  }\n}\n\n.related-submissions {\n  margin: 0 20rpx 16rpx;\n  @include glass-effect(0.6);\n  border-radius: 20rpx;\n  padding: 24rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n\n  .section-header {\n    display: flex;\n    align-items: center;\n    gap: 8rpx;\n    margin-bottom: 20rpx;\n\n    .section-title {\n      font-size: 24rpx;\n      font-weight: 700;\n      color: $soul-gray-700;\n    }\n  }\n\n  .submissions-scroll {\n    width: 100%;\n    white-space: nowrap;\n  }\n\n  .submissions-container {\n    display: flex;\n    gap: 16rpx;\n    padding: 0 4rpx;\n  }\n\n  .mini-submission-card {\n    position: relative;\n    width: 200rpx;\n    height: 240rpx;\n    @include glass-effect(0.7);\n    border-radius: 24rpx;\n    overflow: hidden;\n    flex-shrink: 0;\n    transition: all 0.2s ease;\n    display: flex;\n    flex-direction: column;\n\n    &:hover {\n      transform: translateY(-4rpx);\n    }\n\n    .mini-top-badge {\n      position: absolute;\n      top: 8rpx;\n      right: 8rpx;\n      z-index: 10;\n      background: linear-gradient(135deg, $soul-accent 0%, #FFE066 100%);\n      border-radius: 12rpx;\n      padding: 4rpx 8rpx;\n\n      .mini-top-text {\n        font-size: 16rpx;\n        color: $soul-white;\n        font-weight: 600;\n      }\n    }\n\n    .mini-cover-container {\n      position: relative;\n      width: 100%;\n      height: 160rpx;\n      overflow: hidden;\n      background-color: #f0f0f0;\n      flex-shrink: 0;\n\n      .mini-cover {\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n      }\n    }\n\n    .mini-info-container {\n      height: 80rpx;\n      padding: 6rpx 8rpx;\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n      flex-shrink: 0;\n    }\n\n    .mini-info-row {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      gap: 4rpx;\n\n      .mini-info-item {\n        display: flex;\n        align-items: center;\n        gap: 2rpx;\n        flex: 1;\n        justify-content: flex-start;\n        min-width: 0;\n        overflow: hidden;\n\n        .mini-info-text {\n          font-size: 15rpx;\n          color: $soul-gray-700;\n          font-weight: 500;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          flex: 1;\n          min-width: 0;\n        }\n      }\n    }\n  }\n}\n\n.contact-section {\n  margin: 0 20rpx 16rpx;\n\n  .contact-btn {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12rpx;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 32rpx;\n    padding: 24rpx 40rpx;\n    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);\n    transition: all 0.3s ease;\n    overflow: hidden;\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -100%;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n      transition: left 0.5s ease;\n    }\n\n    &:active {\n      transform: translateY(2rpx) scale(0.98);\n      box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\n\n      &::before {\n        left: 100%;\n      }\n    }\n\n    .btn-icon {\n      animation: heartBeat 2s ease-in-out infinite;\n    }\n\n    .btn-text {\n      font-size: 30rpx;\n      color: $soul-white;\n      font-weight: 700;\n      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n    }\n\n    .btn-sparkle {\n      position: absolute;\n      top: -8rpx;\n      right: -8rpx;\n      font-size: 24rpx;\n      animation: pulse 2s ease-in-out infinite;\n    }\n  }\n}\n\n\n\n.contact-modal {\n  position: relative;\n  width: 680rpx;\n  max-height: 80vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 32rpx;\n  overflow: hidden;\n  box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.3);\n  animation: modalSlideIn 0.3s ease-out;\n  margin: 10vh 0;\n\n  .modal-bg-decoration {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    pointer-events: none;\n    overflow: hidden;\n\n    .bg-circle {\n      position: absolute;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      animation: float 6s ease-in-out infinite;\n\n      &.bg-circle-1 {\n        width: 120rpx;\n        height: 120rpx;\n        top: -60rpx;\n        right: -60rpx;\n        animation-delay: 0s;\n      }\n\n      &.bg-circle-2 {\n        width: 80rpx;\n        height: 80rpx;\n        bottom: 100rpx;\n        left: -40rpx;\n        animation-delay: 2s;\n      }\n\n      &.bg-circle-3 {\n        width: 60rpx;\n        height: 60rpx;\n        top: 200rpx;\n        right: 50rpx;\n        animation-delay: 4s;\n      }\n    }\n  }\n\n  .modal-close {\n    position: absolute;\n    top: 24rpx;\n    right: 24rpx;\n    width: 64rpx;\n    height: 64rpx;\n    background: rgba(255, 255, 255, 0.2);\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    z-index: 10;\n    backdrop-filter: blur(10rpx);\n    transition: all 0.2s ease;\n\n    &:active {\n      transform: scale(0.95);\n      background: rgba(255, 255, 255, 0.3);\n    }\n  }\n\n  .modal-header {\n    text-align: center;\n    padding: 48rpx 40rpx 32rpx;\n    position: relative;\n\n    .header-icon {\n      margin-bottom: 16rpx;\n      animation: heartBeat 2s ease-in-out infinite;\n    }\n\n    .modal-title {\n      font-size: 40rpx;\n      font-weight: 700;\n      color: #ffffff;\n      margin-bottom: 8rpx;\n      display: block;\n      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n    }\n\n    .modal-subtitle {\n      font-size: 26rpx;\n      color: rgba(255, 255, 255, 0.8);\n      display: block;\n    }\n  }\n\n  .modal-content {\n    background: rgba(255, 255, 255, 0.95);\n    backdrop-filter: blur(20rpx);\n    margin: 0 24rpx 24rpx;\n    border-radius: 24rpx;\n    padding: 32rpx;\n    position: relative;\n    overflow-y: auto;\n    max-height: calc(80vh - 200rpx);\n\n    .steps-guide {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 32rpx;\n      padding: 20rpx;\n      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\n      border-radius: 20rpx;\n\n      .step-item {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        flex: 1;\n\n        .step-number {\n          width: 48rpx;\n          height: 48rpx;\n          background: #FF6B9D;\n          color: white;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 24rpx;\n          font-weight: 600;\n          margin-bottom: 8rpx;\n          box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);\n        }\n\n        .step-text {\n          font-size: 22rpx;\n          color: #8B4513;\n          text-align: center;\n          font-weight: 500;\n        }\n      }\n\n      .step-arrow {\n        margin: 0 16rpx;\n        margin-top: -24rpx;\n      }\n    }\n\n    .section-header {\n      display: flex;\n      align-items: center;\n      margin-bottom: 16rpx;\n\n      .section-title {\n        font-size: 30rpx;\n        font-weight: 600;\n        color: #333;\n        margin-left: 12rpx;\n      }\n    }\n\n    .contact-id-section {\n      margin-bottom: 32rpx;\n\n      .contact-id-card {\n        display: flex;\n        align-items: center;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        border-radius: 16rpx;\n        padding: 24rpx;\n        box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);\n\n        .id-display {\n          flex: 1;\n\n          .contact-id {\n            font-size: 32rpx;\n            font-weight: 700;\n            color: #ffffff;\n            letter-spacing: 2rpx;\n          }\n        }\n\n        .copy-btn {\n          display: flex;\n          align-items: center;\n          background: rgba(255, 255, 255, 0.2);\n          border-radius: 12rpx;\n          padding: 16rpx 24rpx;\n          backdrop-filter: blur(10rpx);\n          transition: all 0.2s ease;\n\n          &:active {\n            transform: scale(0.95);\n            background: rgba(255, 255, 255, 0.3);\n          }\n\n          .copy-text {\n            font-size: 24rpx;\n            color: #ffffff;\n            margin-left: 8rpx;\n            font-weight: 500;\n          }\n        }\n      }\n    }\n\n    .service-qr-section {\n      margin-bottom: 24rpx;\n\n      .qr-container {\n        text-align: center;\n\n        .qr-frame {\n          position: relative;\n          display: inline-block;\n          margin-bottom: 16rpx;\n          padding: 16rpx;\n          background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n          border-radius: 20rpx;\n          box-shadow: 0 8rpx 24rpx rgba(168, 237, 234, 0.3);\n\n          .service-qr {\n            width: 160rpx;\n            height: 160rpx;\n            border-radius: 12rpx;\n            display: block;\n          }\n\n          .qr-corners {\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            pointer-events: none;\n\n            .corner {\n              position: absolute;\n              width: 32rpx;\n              height: 32rpx;\n              border: 4rpx solid #FF6B9D;\n\n              &.corner-tl {\n                top: 12rpx;\n                left: 12rpx;\n                border-right: none;\n                border-bottom: none;\n                border-radius: 8rpx 0 0 0;\n              }\n\n              &.corner-tr {\n                top: 12rpx;\n                right: 12rpx;\n                border-left: none;\n                border-bottom: none;\n                border-radius: 0 8rpx 0 0;\n              }\n\n              &.corner-bl {\n                bottom: 12rpx;\n                left: 12rpx;\n                border-right: none;\n                border-top: none;\n                border-radius: 0 0 0 8rpx;\n              }\n\n              &.corner-br {\n                bottom: 12rpx;\n                right: 12rpx;\n                border-left: none;\n                border-top: none;\n                border-radius: 0 0 8rpx 0;\n              }\n            }\n          }\n        }\n\n        .service-tip {\n          font-size: 24rpx;\n          color: #666;\n          line-height: 1.4;\n        }\n      }\n    }\n\n    .warm-tips {\n      background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\n      border-radius: 16rpx;\n      padding: 20rpx;\n\n      .tips-header {\n        display: flex;\n        align-items: center;\n        margin-bottom: 8rpx;\n\n        .tips-title {\n          font-size: 26rpx;\n          font-weight: 600;\n          color: #8B4513;\n          margin-left: 8rpx;\n        }\n      }\n\n      .tips-content {\n        font-size: 24rpx;\n        color: #8B4513;\n        line-height: 1.5;\n      }\n    }\n  }\n}\n\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n/* 动画效果 */\n@keyframes modalSlideIn {\n  0% {\n    opacity: 0;\n    transform: scale(0.8) translateY(100rpx);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-20rpx) rotate(180deg);\n  }\n}\n\n@keyframes heartBeat {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=a398096a&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=a398096a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752120110331\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}