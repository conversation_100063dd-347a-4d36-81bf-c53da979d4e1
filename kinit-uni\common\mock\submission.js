/**
 * 投稿模拟数据
 */

export const mockSubmissions = [
  {
    id: 1,
    title: "💗搭子投稿💗A062705",
    cover_image: "https://picsum.photos/300/400?random=1",
    category: "王者荣耀",
    province: "四川",
    city: "成都",
    gender: "女",
    age: 17,
    height: 164,
    occupation: "学生",
    self_intro: "追星",
    partner_requirements: "找对象的，卡颜，要对我好，和我年龄必须差不多不要比我小的，也不要比我大太多的",
    accept_long_distance: true,
    images: "https://picsum.photos/300/400?random=11,https://picsum.photos/300/400?random=12,https://picsum.photos/300/400?random=13",
    is_top: true,
    submission_code: "A062705",
    create_datetime: "2024-01-15 10:30:00",
    status: "approved",
    is_visible: true
  },
  {
    id: 2,
    title: "恋爱投稿Y7712",
    cover_image: "https://picsum.photos/300/400?random=2",
    category: "恋爱",
    province: "河南",
    city: "南阳",
    gender: "女",
    age: 16,
    height: 168,
    occupation: "学生",
    self_intro: "日常喜欢刷视频 追剧画画等一系列手工  情绪阴晴不定 有时回消息可能会消失 吃饭看电视都能回信息",
    partner_requirements: "希望声音好听 手也好看的 比较吃颜 熟了之后也可以每天都发语音聊天  希望能爆点小金币💩",
    accept_long_distance: true,
    images: "https://picsum.photos/300/400?random=21,https://picsum.photos/300/400?random=22,https://picsum.photos/300/400?random=23",
    is_top: false,
    submission_code: "Y7712",
    create_datetime: "2024-01-14 15:20:00",
    status: "approved",
    is_visible: true
  },
  {
    id: 3,
    title: "💗健身搭子💗F8823",
    cover_image: "https://picsum.photos/300/400?random=3",
    category: "健身",
    province: "广东",
    city: "广州",
    gender: "女",
    age: 25,
    height: 160,
    occupation: "上班族",
    self_intro: "刚办了健身卡，想找个一起锻炼的小伙伴，互相监督，一起变美变健康！主要想练瑜伽和有氧运动。",
    partner_requirements: "希望是女生，年龄相仿，有健身基础最好，可以互相鼓励坚持下去",
    accept_long_distance: false,
    images: "https://picsum.photos/300/400?random=31,https://picsum.photos/300/400?random=32",
    is_top: false,
    submission_code: "F8823",
    create_datetime: "2024-01-13 09:15:00",
    status: "approved",
    is_visible: true
  },
  {
    id: 4,
    title: "摄影搭子M9901",
    cover_image: "https://picsum.photos/300/400?random=4",
    category: "摄影",
    province: "浙江",
    city: "杭州",
    gender: "男",
    age: 30,
    height: 178,
    occupation: "设计师",
    self_intro: "喜欢拍风景和人像，想找志同道合的朋友一起外拍，可以互相学习摄影技巧，分享拍摄心得。",
    partner_requirements: "有摄影基础，有自己的设备，周末有时间外拍，性格开朗好相处",
    accept_long_distance: true,
    images: "https://picsum.photos/300/400?random=41,https://picsum.photos/300/400?random=42",
    is_top: true,
    submission_code: "M9901",
    create_datetime: "2024-01-12 14:45:00",
    status: "approved",
    is_visible: true
  },
  {
    id: 5,
    title: "旅行搭子L5566",
    cover_image: "https://picsum.photos/300/400?random=5",
    category: "旅行",
    province: "四川",
    city: "成都",
    gender: "女",
    age: 26,
    height: 162,
    occupation: "白领",
    self_intro: "计划下个月去云南旅行，行程已经规划好了，寻找1-2个女生一起，可以分摊费用，也更安全。",
    partner_requirements: "女生优先，年龄相仿，有旅行经验，性格开朗，能AA费用",
    accept_long_distance: true,
    images: "https://picsum.photos/300/400?random=51,https://picsum.photos/300/400?random=52,https://picsum.photos/300/400?random=53",
    is_top: false,
    submission_code: "L5566",
    create_datetime: "2024-01-11 11:30:00",
    status: "approved",
    is_visible: true
  },
  {
    id: 6,
    title: "美食搭子T3344",
    cover_image: "https://picsum.photos/300/400?random=6",
    category: "美食",
    province: "江苏",
    city: "南京",
    gender: "不限",
    age: 24,
    height: 165,
    occupation: "学生",
    self_intro: "热爱美食，想找几个小伙伴一起探店，尝试各种新奇的餐厅和小吃。可以一起分享美食心得，拍照打卡。",
    partner_requirements: "热爱美食，愿意尝试新鲜事物，有探店经验更好，能拍照会修图加分",
    accept_long_distance: false,
    images: "https://picsum.photos/300/400?random=61,https://picsum.photos/300/400?random=62",
    is_top: false,
    submission_code: "T3344",
    create_datetime: "2024-01-10 16:20:00",
    status: "approved",
    is_visible: true
  }
]

export const mockFilterOptions = {
  provinces: [
    "北京", "天津", "河北", "山西", "内蒙古", "辽宁", "吉林", "黑龙江",
    "上海", "江苏", "浙江", "安徽", "福建", "江西", "山东", "河南",
    "湖北", "湖南", "广东", "广西", "海南", "重庆", "四川", "贵州",
    "云南", "西藏", "陕西", "甘肃", "青海", "宁夏", "新疆"
  ],
  categories: [
    "王者荣耀", "恋爱", "健身", "摄影", "旅行", "美食", "音乐", "电影",
    "读书", "绘画", "舞蹈", "游戏", "运动", "宠物", "手工", "学习", "其他"
  ],
  genders: ["男", "女", "不限"]
}
