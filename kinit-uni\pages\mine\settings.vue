<template>
  <view class="settings-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="left" size="32" color="#FF6B9D"></uni-icons>
        </view>
        <text class="navbar-title">设置</text>
        <view class="navbar-right"></view>
      </view>
    </view>

    <scroll-view scroll-y="true" class="settings-content">
      <!-- 账户设置 -->
      <view class="settings-section">
        <view class="section-title">账户设置</view>
        <view class="settings-card">
          <view class="setting-item" @click="goToProfile">
            <view class="setting-icon">
              <uni-icons type="person" size="24" color="#f78ca0"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">个人资料</text>
              <text class="setting-desc">编辑个人信息</text>
            </view>
            <view class="setting-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <view class="setting-item" @click="goToPrivacy">
            <view class="setting-icon">
              <uni-icons type="locked" size="24" color="#a6c1ee"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">隐私设置</text>
              <text class="setting-desc">管理隐私权限</text>
            </view>
            <view class="setting-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 通知设置 -->
      <view class="settings-section">
        <view class="section-title">通知设置</view>
        <view class="settings-card">
          <view class="setting-item">
            <view class="setting-icon">
              <uni-icons type="notification" size="24" color="#feb47b"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">推送通知</text>
              <text class="setting-desc">接收新消息通知</text>
            </view>
            <switch 
              :checked="notificationSettings.push" 
              @change="toggleNotification('push', $event)"
              color="#f78ca0"
            />
          </view>

          <view class="setting-item">
            <view class="setting-icon">
              <uni-icons type="sound" size="24" color="#c8a8e9"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">声音提醒</text>
              <text class="setting-desc">播放提示音</text>
            </view>
            <switch 
              :checked="notificationSettings.sound" 
              @change="toggleNotification('sound', $event)"
              color="#f78ca0"
            />
          </view>

          <view class="setting-item">
            <view class="setting-icon">
              <uni-icons type="vibrate" size="24" color="#10b981"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">震动提醒</text>
              <text class="setting-desc">震动反馈</text>
            </view>
            <switch 
              :checked="notificationSettings.vibrate" 
              @change="toggleNotification('vibrate', $event)"
              color="#f78ca0"
            />
          </view>
        </view>
      </view>

      <!-- 应用设置 -->
      <view class="settings-section">
        <view class="section-title">应用设置</view>
        <view class="settings-card">
          <view class="setting-item" @click="clearCache">
            <view class="setting-icon">
              <uni-icons type="trash" size="24" color="#ef4444"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">清除缓存</text>
              <text class="setting-desc">{{ cacheSize }}</text>
            </view>
            <view class="setting-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <view class="setting-item" @click="checkUpdate">
            <view class="setting-icon">
              <uni-icons type="reload" size="24" color="#3b82f6"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">检查更新</text>
              <text class="setting-desc">当前版本 v1.0.0</text>
            </view>
            <view class="setting-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 帮助与支持 -->
      <view class="settings-section">
        <view class="section-title">帮助与支持</view>
        <view class="settings-card">
          <view class="setting-item" @click="goToHelp">
            <view class="setting-icon">
              <uni-icons type="help" size="24" color="#8b5cf6"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">使用帮助</text>
              <text class="setting-desc">常见问题解答</text>
            </view>
            <view class="setting-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <view class="setting-item" @click="contactUs">
            <view class="setting-icon">
              <uni-icons type="chat" size="24" color="#06b6d4"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">联系我们</text>
              <text class="setting-desc">意见反馈</text>
            </view>
            <view class="setting-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <view class="setting-item" @click="goToAbout">
            <view class="setting-icon">
              <uni-icons type="info" size="24" color="#10b981"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">关于我们</text>
              <text class="setting-desc">了解更多信息</text>
            </view>
            <view class="setting-arrow">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 退出登录 -->
      <view v-if="isLoggedIn" class="settings-section">
        <view class="settings-card">
          <view class="setting-item logout" @click="logout">
            <view class="setting-icon">
              <uni-icons type="close" size="24" color="#ef4444"></uni-icons>
            </view>
            <view class="setting-content">
              <text class="setting-title">退出登录</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      notificationSettings: {
        push: true,
        sound: true,
        vibrate: false
      },
      cacheSize: '12.5MB'
    }
  },
  computed: {
    ...mapGetters('user', ['isLoggedIn'])
  },
  onLoad() {
    this.loadSettings()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },

    loadSettings() {
      // 从本地存储加载设置
      const settings = uni.getStorageSync('notificationSettings')
      if (settings) {
        this.notificationSettings = { ...this.notificationSettings, ...settings }
      }
      
      // 计算缓存大小
      this.calculateCacheSize()
    },

    toggleNotification(type, event) {
      this.notificationSettings[type] = event.detail.value
      // 保存到本地存储
      uni.setStorageSync('notificationSettings', this.notificationSettings)
      
      uni.showToast({
        title: event.detail.value ? '已开启' : '已关闭',
        icon: 'success'
      })
    },

    calculateCacheSize() {
      // 模拟计算缓存大小
      const sizes = ['8.2MB', '12.5MB', '15.8MB', '6.3MB', '20.1MB']
      this.cacheSize = sizes[Math.floor(Math.random() * sizes.length)]
    },

    clearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除应用缓存吗？这将删除临时文件和图片缓存。',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({ title: '清理中...' })
            
            setTimeout(() => {
              uni.hideLoading()
              this.cacheSize = '0MB'
              uni.showToast({
                title: '缓存清理完成',
                icon: 'success'
              })
            }, 1500)
          }
        }
      })
    },

    checkUpdate() {
      uni.showLoading({ title: '检查中...' })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '已是最新版本',
          icon: 'success'
        })
      }, 1000)
    },

    goToProfile() {
      uni.navigateTo({
        url: '/pages/mine/profile'
      })
    },

    goToPrivacy() {
      uni.navigateTo({
        url: '/pages/mine/privacy'
      })
    },

    goToHelp() {
      uni.navigateTo({
        url: '/pages/mine/help'
      })
    },

    goToAbout() {
      uni.navigateTo({
        url: '/pages/mine/about'
      })
    },

    contactUs() {
      uni.showActionSheet({
        itemList: ['微信客服', '邮箱反馈', '电话咨询'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              uni.showToast({
                title: '微信客服功能开发中',
                icon: 'none'
              })
              break
            case 1:
              uni.setClipboardData({
                data: '<EMAIL>',
                success: () => {
                  uni.showToast({
                    title: '邮箱已复制',
                    icon: 'success'
                  })
                }
              })
              break
            case 2:
              uni.makePhoneCall({
                phoneNumber: '************'
              })
              break
          }
        }
      })
    },

    logout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.$store.dispatch('user/logout')
              uni.showToast({
                title: '已退出登录',
                icon: 'success'
              })
              
              setTimeout(() => {
                uni.navigateBack()
              }, 1000)
            } catch (error) {
              console.error('退出登录失败:', error)
              uni.showToast({
                title: '退出失败，请重试',
                icon: 'none'
              })
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;

    .navbar-left, .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: center;
    }

    .navbar-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }
  }
}

.settings-content {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.settings-section {
  margin-bottom: 24rpx;

  .section-title {
    font-size: 24rpx;
    font-weight: 600;
    color: $soul-gray-600;
    margin-bottom: 12rpx;
    padding-left: 8rpx;
  }

  .settings-card {
    @include glass-effect(0.6);
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);

    .setting-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      border-bottom: 1rpx solid rgba(0,0,0,0.05);
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active:not(.logout) {
        background: rgba(247, 140, 160, 0.05);
      }

      &.logout {
        justify-content: center;

        &:active {
          background: rgba(239, 68, 68, 0.05);
        }

        .setting-content .setting-title {
          color: #ef4444;
          font-weight: 600;
        }
      }

      .setting-icon {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12rpx;
        margin-right: 16rpx;
      }

      .setting-content {
        flex: 1;

        .setting-title {
          display: block;
          font-size: 26rpx;
          font-weight: 600;
          color: $soul-gray-800;
          margin-bottom: 4rpx;
        }

        .setting-desc {
          font-size: 22rpx;
          color: $soul-gray-500;
        }
      }

      .setting-arrow {
        opacity: 0.6;
      }

      switch {
        transform: scale(0.8);
      }
    }
  }
}
</style>
