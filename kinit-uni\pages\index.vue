<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left">
          <uni-icons type="ghost" size="60" color="#f78ca0"></uni-icons>
          <text class="navbar-title">{{ siteName }}</text>
        </view>
        <view class="navbar-actions">
          <view class="action-btn" @click="goToNetworkTest">
            <uni-icons type="gear" size="24" color="#f78ca0"></uni-icons>
          </view>
          <view class="action-btn" @click="showSearch">
            <uni-icons type="search" size="24" color="#f78ca0"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 省份筛选区域 -->
    <view class="category-section">
      <view class="section-title">
        <uni-icons type="location" size="32" color="#f78ca0"></uni-icons>
        <text class="title-text">按省份筛选</text>
      </view>
      <scroll-view scroll-x="true" class="category-scroll">
        <view class="category-buttons-container">
          <view class="category-row">
            <view
              v-for="(province, index) in quickProvinces.slice(0, Math.ceil(quickProvinces.length / 2))"
              :key="index"
              class="category-btn"
              :class="{ active: currentProvince === province.value }"
              @click="selectProvince(province)"
            >
              <text class="category-text">{{ province.label }}</text>
            </view>
          </view>
          <view class="category-row">
            <view
              v-for="(province, index) in quickProvinces.slice(Math.ceil(quickProvinces.length / 2))"
              :key="index + Math.ceil(quickProvinces.length / 2)"
              class="category-btn"
              :class="{ active: currentProvince === province.value }"
              @click="selectProvince(province)"
            >
              <text class="category-text">{{ province.label }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>



    <!-- 投稿列表 -->
    <view class="posts-section">
      <view class="section-title">
        <view class="title-left">
          <uni-icons type="star" size="32" color="#f78ca0"></uni-icons>
          <text class="title-text">最新搭子</text>
        </view>
        <view class="filter-btn-container" @click="toggleAdvancedFilter">
          <uni-icons type="tune" size="24" color="#f78ca0"></uni-icons>
          <text class="filter-btn-text">筛选</text>
        </view>
      </view>

      <!-- 高级筛选区域 -->
      <view v-if="showAdvancedFilter" class="advanced-filter-section">
        <view class="filter-grid">
          <view class="filter-item">
            <text class="filter-label">关键词搜索</text>
            <input
              v-model="searchKeyword"
              class="filter-input"
              placeholder="年龄、城市、兴趣爱好..."
              @input="handleSearch"
            />
          </view>

          <view class="filter-item">
            <text class="filter-label">性别</text>
            <view class="gender-options">
              <view
                v-for="(option, index) in genderOptions"
                :key="index"
                class="gender-option"
                :class="{ active: genderIndex === index }"
                @click="selectGender(index)"
              >
                <uni-icons :type="option.icon" size="20" :color="genderIndex === index ? '#f78ca0' : '#999'"></uni-icons>
                <text class="gender-text" :class="{ active: genderIndex === index }">{{ option.label }}</text>
              </view>
            </view>
          </view>

          <view class="filter-item">
            <text class="filter-label">年龄范围</text>
            <view class="age-range-container">
              <!-- 年龄范围显示 -->
              <view class="age-display">
                <text class="age-text">
                  {{ ageRange.min }}岁 - {{ ageRange.max === 99 ? '不限' : ageRange.max + '岁' }}
                </text>
              </view>

              <!-- 年龄快捷选择 -->
              <view class="age-quick-select">
                <view
                  v-for="(option, index) in ageOptions"
                  :key="index"
                  class="age-option"
                  :class="{ active: isAgeOptionActive(option) }"
                  @click="selectAgeOption(option)"
                >
                  <text class="age-option-text">{{ option.label }}</text>
                </view>
              </view>

              <!-- 自定义年龄范围 -->
              <view class="custom-age-range" v-if="showCustomAge">
                <!-- 最小年龄滑块 -->
                <view class="slider-container">
                  <text class="slider-label">最小年龄: {{ ageRange.min }}岁</text>
                  <slider
                    class="age-slider"
                    :value="ageRange.min"
                    :min="16"
                    :max="60"
                    :step="1"
                    activeColor="#f78ca0"
                    backgroundColor="#e5e7eb"
                    block-color="#f78ca0"
                    block-size="20"
                    @change="handleMinAgeChange"
                  />
                </view>

                <!-- 最大年龄滑块 -->
                <view class="slider-container">
                  <text class="slider-label">最大年龄: {{ ageRange.max === 99 ? '不限' : ageRange.max + '岁' }}</text>
                  <slider
                    class="age-slider"
                    :value="ageRange.max === 99 ? 60 : ageRange.max"
                    :min="16"
                    :max="60"
                    :step="1"
                    activeColor="#a6c1ee"
                    backgroundColor="#e5e7eb"
                    block-color="#a6c1ee"
                    block-size="20"
                    @change="handleMaxAgeChange"
                  />
                  <view class="no-limit-option" @click="setNoAgeLimit">
                    <text class="no-limit-text" :class="{ active: ageRange.max === 99 }">不限年龄上限</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <view class="filter-actions">
            <view class="filter-btn reset" @click="resetFilters">
              <uni-icons type="refresh" size="20" color="#868f96"></uni-icons>
              <text>重置</text>
            </view>
            <view class="filter-btn apply" @click="applyFilters">
              <uni-icons type="checkmarkempty" size="20" color="#fff"></uni-icons>
              <text>应用筛选</text>
            </view>
          </view>
        </view>
      </view>

      <scroll-view
        scroll-y="true"
        class="submission-list"
        @scrolltolower="loadMore"
        refresher-enabled="true"
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
      >
        <view class="list-container">
          <view class="submission-grid">
            <soul-submission-card
              v-for="(submission, index) in filteredSubmissionList"
              :key="`submission-${submission.id}-${index}`"
              :submission="submission"
              @click="goToDetail"
            ></soul-submission-card>
          </view>

          <!-- 加载状态 -->
          <view v-if="loading" class="loading-container">
            <uni-icons type="heart" size="48" color="#f78ca0" class="loading-icon"></uni-icons>
            <text class="loading-text">正在努力加载搭子信息中...</text>
          </view>

          <!-- 空状态 -->
          <view v-if="!loading && filteredSubmissionList.length === 0" class="empty-container">
            <view class="empty-icon">
              <uni-icons type="search" size="80" color="#c8a8e9"></uni-icons>
            </view>
            <text class="empty-title">没有找到相关搭子</text>
            <text class="empty-subtitle">试试调整筛选条件或搜索关键词</text>

            <!-- 搜索建议 -->
            <view class="search-suggestions">
              <text class="suggestions-title">热门搜索建议：</text>
              <view class="suggestions-tags">
                <view
                  v-for="(tag, index) in searchSuggestions"
                  :key="index"
                  class="suggestion-tag"
                  @click="applySuggestion(tag)"
                >
                  <text class="tag-text">{{ tag }}</text>
                </view>
              </view>
            </view>

            <!-- 重置按钮 -->
            <view class="reset-filters-btn" @click="resetFilters">
              <uni-icons type="refresh" size="20" color="#f78ca0"></uni-icons>
              <text class="reset-text">重置筛选条件</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部菜单栏 -->
    <view class="bottom-tabbar">
      <view class="tab-item active" @click="switchTab('home')">
        <uni-icons type="home" size="24" color="#f78ca0"></uni-icons>
        <text class="tab-text active">主页</text>
      </view>
      <view class="tab-item" @click="switchTab('add')">
        <view class="add-btn">
          <uni-icons type="plus" size="24" color="#fff"></uni-icons>
        </view>
        <text class="tab-text">投稿</text>
      </view>
      <view class="tab-item" @click="switchTab('profile')">
        <uni-icons type="person" size="24" color="#999"></uni-icons>
        <text class="tab-text">我的</text>
      </view>
    </view>

    <!-- 搜索弹窗 -->
    <uni-popup ref="searchPopup" type="top" background-color="#ffffff">
      <view class="search-container">
        <view class="search-bar">
          <uni-icons type="search" size="24" color="#FF6B9D"></uni-icons>
          <input
            v-model="searchKeyword"
            placeholder="搜索投稿内容..."
            class="search-input"
            @confirm="handleSearch"
          />
          <text class="cancel-btn" @click="hideSearch">取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { wxShareMixins } from '@/common/mixins/share.js'
import submissionApi from '@/common/api/submission.js'
import SoulFilter from '@/components/soul-filter/soul-filter.vue'
import SoulSubmissionCard from '@/components/soul-submission-card/soul-submission-card.vue'

export default {
  components: {
    SoulFilter,
    SoulSubmissionCard
  },
  mixins: [wxShareMixins],
  data() {
    return {
      siteName: '搭子星球', // 默认网站名称
      submissionList: [],
      filteredSubmissionList: [],
      filterOptions: {
        categories: [],
        provinces: [],
        genders: []
      },
      quickProvinces: [
        { label: '全部', value: '' },
        { label: '北京', value: '北京' },
        { label: '上海', value: '上海' },
        { label: '广东', value: '广东' },
        { label: '浙江', value: '浙江' },
        { label: '江苏', value: '江苏' },
        { label: '四川', value: '四川' },
        { label: '河南', value: '河南' },
        { label: '山东', value: '山东' },
        { label: '湖北', value: '湖北' },
        { label: '湖南', value: '湖南' },
        { label: '河北', value: '河北' },
        { label: '福建', value: '福建' },
        { label: '安徽', value: '安徽' },
        { label: '江西', value: '江西' },
        { label: '辽宁', value: '辽宁' },
        { label: '天津', value: '天津' },
        { label: '重庆', value: '重庆' },
        { label: '山西', value: '山西' },
        { label: '陕西', value: '陕西' },
        { label: '吉林', value: '吉林' },
        { label: '黑龙江', value: '黑龙江' },
        { label: '内蒙古', value: '内蒙古' },
        { label: '广西', value: '广西' },
        { label: '海南', value: '海南' },
        { label: '贵州', value: '贵州' },
        { label: '云南', value: '云南' },
        { label: '西藏', value: '西藏' },
        { label: '甘肃', value: '甘肃' },
        { label: '青海', value: '青海' },
        { label: '宁夏', value: '宁夏' },
        { label: '新疆', value: '新疆' }
      ],
      currentProvince: '',
      showAdvancedFilter: false,
      currentFilters: {},
      searchKeyword: '',
      genderOptions: [
        { label: '不限', icon: 'person' },
        { label: '男', icon: 'person' },
        { label: '女', icon: 'person-filled' }
      ],
      genderIndex: 0,
      ageRange: {
        min: 16,
        max: 99
      },
      ageOptions: [
        { label: '18-25岁', min: 18, max: 25 },
        { label: '26-30岁', min: 26, max: 30 },
        { label: '31-35岁', min: 31, max: 35 },
        { label: '36-40岁', min: 36, max: 40 },
        { label: '40岁以上', min: 40, max: 99 },
        { label: '自定义', min: null, max: null }
      ],
      showCustomAge: false,
      searchSuggestions: ['游戏', '电影', '运动', '音乐', '旅行', '美食', '读书', '摄影'],
      pagination: {
        page: 1,
        size: 10, // 减少每页数量，提升加载速度
        total: 0,
        pages: 0
      },
      loading: false,
      refreshing: false,
      hasMore: true,
      debounceTimer: null
    }
  },
  onLoad() {
    this.initPage()
  },
  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  methods: {
    // 跳转到网络测试页面
    goToNetworkTest() {
      uni.navigateTo({
        url: '/pages/test-network'
      })
    },

    async initPage() {
      await this.loadSiteConfig()
      await this.loadFilterOptions()
      await this.loadSubmissionList(true)
    },

    async loadSiteConfig() {
      try {
        const res = await this.$store.dispatch('system/getSystemConfig')
        if (res && res.site_name) {
          this.siteName = res.site_name
        }
      } catch (error) {
        console.error('加载网站配置失败:', error)
      }
    },

    async loadFilterOptions() {
      try {
        const res = await submissionApi.getFilterOptions()
        if (res.code === 200) {
          this.filterOptions = res.data
        }
      } catch (error) {
        console.error('加载筛选选项失败:', error)
      }
    },

    async loadSubmissionList(reset = false) {
      if (this.loading) return

      if (reset) {
        this.pagination.page = 1
        this.submissionList = []
        this.hasMore = true
      }

      if (!this.hasMore) return

      this.loading = true

      try {
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          ...this.currentFilters
        }

        // 添加搜索关键词
        if (this.searchKeyword) {
          params.keyword = this.searchKeyword
        }

        // 添加省份筛选
        if (this.currentProvince) {
          params.province = this.currentProvince
        }

        // 添加性别筛选
        if (this.genderIndex > 0) {
          params.gender = this.genderOptions[this.genderIndex].label
        }

        // 添加年龄筛选
        if (this.ageRange.min > 16) {
          params.min_age = this.ageRange.min
        }
        if (this.ageRange.max < 99) {
          params.max_age = this.ageRange.max
        }

        const res = await submissionApi.getSubmissionList(params)

        if (res.code === 200) {
          const { items, total, pages } = res.data

          if (reset) {
            this.submissionList = items
          } else {
            // 加载更多时，需要去重（特别是置顶投稿）
            const existingIds = new Set(this.submissionList.map(item => item.id))
            const newItems = items.filter(item => !existingIds.has(item.id))
            this.submissionList.push(...newItems)
          }

          this.pagination.total = total
          this.pagination.pages = pages
          this.hasMore = this.pagination.page < pages

          if (!reset) {
            this.pagination.page++
          }

          // 应用本地筛选
          this.applyLocalFilters()
        }
      } catch (error) {
        console.error('加载投稿列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 应用本地筛选（仅用于排序，筛选已在后端完成）
    applyLocalFilters() {
      let filtered = [...this.submissionList]

      // 排序：置顶优先，然后按创建时间倒序
      filtered.sort((a, b) => {
        if (a.is_top && !b.is_top) return -1
        if (!a.is_top && b.is_top) return 1
        return new Date(b.create_datetime) - new Date(a.create_datetime)
      })

      this.filteredSubmissionList = filtered
    },

    // 省份选择
    selectProvince(province) {
      this.currentProvince = province.value
      // 使用防抖延迟加载
      this.debounceLoadData()
    },

    // 切换高级筛选显示
    toggleAdvancedFilter() {
      this.showAdvancedFilter = !this.showAdvancedFilter
    },

    // 性别选择
    selectGender(index) {
      this.genderIndex = index
      // 使用防抖延迟加载
      this.debounceLoadData()
    },

    // 选择年龄选项
    selectAgeOption(option) {
      if (option.min === null && option.max === null) {
        // 自定义选项
        this.showCustomAge = true
      } else {
        this.showCustomAge = false
        this.ageRange.min = option.min
        this.ageRange.max = option.max
        this.loadSubmissionList(true) // 重新从后端加载数据
      }
    },

    // 检查年龄选项是否激活
    isAgeOptionActive(option) {
      if (option.min === null && option.max === null) {
        return this.showCustomAge
      }
      return this.ageRange.min === option.min && this.ageRange.max === option.max
    },

    // 设置无年龄上限
    setNoAgeLimit() {
      this.ageRange.max = 99
      this.loadSubmissionList(true) // 重新从后端加载数据
    },

    // 最小年龄变化
    handleMinAgeChange(e) {
      this.ageRange.min = e.detail.value
      // 确保最小年龄不大于最大年龄（除非最大年龄是99）
      if (this.ageRange.max !== 99 && this.ageRange.min > this.ageRange.max) {
        this.ageRange.max = this.ageRange.min
      }
      this.loadSubmissionList(true) // 重新从后端加载数据
    },

    // 最大年龄变化
    handleMaxAgeChange(e) {
      this.ageRange.max = e.detail.value
      // 确保最大年龄不小于最小年龄
      if (this.ageRange.max < this.ageRange.min) {
        this.ageRange.min = this.ageRange.max
      }
      this.loadSubmissionList(true) // 重新从后端加载数据
    },

    // 计算年龄在滑块上的百分比位置
    getAgePercentage(age) {
      const minAge = 16
      const maxAge = 50
      return ((age - minAge) / (maxAge - minAge)) * 100
    },

    // 搜索处理
    handleSearch() {
      this.applyLocalFilters()
    },

    // 应用搜索建议
    applySuggestion(tag) {
      this.searchKeyword = tag
      this.applyLocalFilters()
    },

    // 重置筛选
    resetFilters() {
      this.currentProvince = ''
      this.searchKeyword = ''
      this.genderIndex = 0
      this.ageRange = { min: 18, max: 35 }
      this.applyLocalFilters()
    },

    // 应用筛选
    applyFilters() {
      this.applyLocalFilters()
      uni.showToast({
        title: '筛选已应用',
        icon: 'success'
      })
    },

    handleFilterChange(filters) {
      this.currentFilters = filters
      this.loadSubmissionList(true)
    },

    showSearch() {
      this.$refs.searchPopup.open()
    },

    hideSearch() {
      this.$refs.searchPopup.close()
    },

    handleSearch() {
      this.hideSearch()
      this.loadSubmissionList(true)
    },

    onRefresh() {
      this.refreshing = true
      this.loadSubmissionList(true)
    },

    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadSubmissionList()
      }
    },

    refreshData() {
      this.loadSubmissionList(true)
    },

    goToDetail(submission) {
      uni.navigateTo({
        url: `/pages/submission/detail?id=${submission.id}`
      })
    },

    // 底部菜单切换
    switchTab(tab) {
      switch(tab) {
        case 'home':
          // 当前页面，不需要跳转
          break
        case 'add':
          uni.navigateTo({
            url: '/pages/submission/create'
          })
          break
        case 'profile':
          uni.navigateTo({
            url: '/pages/mine/index'
          })
          break
      }
    },

    // 防抖加载数据
    debounceLoadData() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }
      this.debounceTimer = setTimeout(() => {
        this.loadSubmissionList(true)
      }, 300) // 300ms防抖
    }
  },

  // 初始化时应用筛选
  watch: {
    submissionList: {
      handler() {
        this.applyLocalFilters()
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
  padding-bottom: 80rpx; // 适配新的底部菜单栏高度
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 40rpx;

    .navbar-left {
      display: flex;
      align-items: center;
      gap: 16rpx;
    }

    .navbar-title {
      font-size: 48rpx;
      font-weight: 800;
      background: linear-gradient(45deg, #f78ca0, #f9748f, #fe9a8b, #ffc8a9);
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
    }

    .navbar-actions {
      display: flex;
      align-items: center;
      gap: 8rpx;
      margin-right: 20rpx; // 增加右边距，避免被系统按钮遮挡

      .action-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(0.95);
        }
      }
    }
  }
}

// 分类区域
.category-section {
  padding: 24rpx 20rpx; // 缩小内边距

  .section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx; // 缩小间距
    gap: 12rpx;

    .title-text {
      font-size: 28rpx; // 缩小字体
      font-weight: 700;
      color: $soul-gray-700;
    }
  }

  .category-scroll {

    .category-buttons-container {
      display: flex;
      flex-direction: column;
      gap: 12rpx;
      padding: 0 12rpx;

      .category-row {
        display: flex;
        gap: 12rpx;
        overflow-x: auto;
        white-space: nowrap;

        .category-btn {
          background: rgba(255, 255, 255, 0.5);
          backdrop-filter: blur(10rpx) saturate(150%);
          border: 1rpx solid rgba(209, 213, 219, 0.2);
          border-radius: 12rpx;
          padding: 8rpx 16rpx; // 进一步缩小内边距
          transition: all 0.3s ease;
          box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
          flex-shrink: 0; // 防止压缩

          &:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: translateY(-4rpx);
            box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);
          }

          &.active {
            background: linear-gradient(to right, #fbc2eb, #a6c1ee);
            color: white;
            box-shadow: 0 8rpx 30rpx rgba(172, 191, 233, 0.4);

            .category-text {
              color: white;
              font-weight: 700;
            }
          }

          .category-text {
            font-size: 22rpx; // 进一步缩小字体
            font-weight: 600;
            color: #4b5563;
            white-space: nowrap;
          }
        }
      }
    }
  }
}



// 投稿区域
.posts-section {
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 0 20rpx;

    .title-left {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .title-text {
        font-size: 28rpx;
        font-weight: 700;
        color: $soul-gray-700;
      }
    }

    .filter-btn-container {
      display: flex;
      align-items: center;
      gap: 8rpx;
      background: rgba(255, 255, 255, 0.8);
      border: 1rpx solid #f78ca0;
      border-radius: 20rpx;
      padding: 8rpx 16rpx;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(247, 140, 160, 0.1);
        transform: translateY(-2rpx);
      }

      .filter-btn-text {
        font-size: 22rpx;
        color: #f78ca0;
        font-weight: 600;
      }
    }
  }

  .advanced-filter-section {
    @include glass-effect(0.6);
    margin: 0 20rpx 20rpx;
    border-radius: 24rpx;
    padding: 24rpx;
    box-shadow: 0 8rpx 30rpx rgba(0,0,0,0.08);

    .filter-grid {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
    }

    .filter-item {
      .filter-label {
        display: block;
        font-weight: 600;
        color: #718096;
        margin-bottom: 12rpx;
        font-size: 24rpx;
      }

      .filter-input {
        width: 100%;
        padding: 20rpx 16rpx;
        border-radius: 12rpx;
        border: 1rpx solid rgba(209, 213, 219, 0.5);
        background-color: rgba(255, 255, 255, 0.8);
        font-size: 28rpx;
        min-height: 80rpx;
        box-sizing: border-box;
        transition: all 0.2s ease;

        &:focus {
          border-color: #a6c1ee;
          box-shadow: 0 0 0 4rpx rgba(166, 193, 238, 0.3);
        }
      }

      .gender-options {
        display: flex;
        gap: 16rpx;

        .gender-option {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8rpx;
          padding: 20rpx 16rpx;
          border-radius: 12rpx;
          border: 1rpx solid rgba(209, 213, 219, 0.5);
          background-color: rgba(255, 255, 255, 0.8);
          transition: all 0.2s ease;
          cursor: pointer;

          &.active {
            border-color: #f78ca0;
            background-color: rgba(247, 140, 160, 0.1);
            box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.2);
          }

          .gender-text {
            font-size: 24rpx;
            color: $soul-gray-600;
            font-weight: 500;

            &.active {
              color: #f78ca0;
              font-weight: 600;
            }
          }
        }
      }

      .age-range-container {
        .age-display {
          text-align: center;
          margin-bottom: 24rpx;

          .age-text {
            font-size: 32rpx;
            color: $soul-gray-700;
            font-weight: 600;
            background: linear-gradient(135deg, #f78ca0, #a6c1ee);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }

        .age-quick-select {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;
          margin-bottom: 32rpx;

          .age-option {
            flex: 1;
            min-width: 120rpx;
            height: 60rpx;
            background: rgba(255, 255, 255, 0.8);
            border: 2rpx solid #e5e7eb;
            border-radius: 30rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10rpx);

            &.active {
              background: linear-gradient(135deg, #f78ca0, #a6c1ee);
              border-color: transparent;
              transform: translateY(-2rpx);
              box-shadow: 0 8rpx 20rpx rgba(247, 140, 160, 0.3);

              .age-option-text {
                color: #fff;
                font-weight: 600;
              }
            }

            .age-option-text {
              font-size: 24rpx;
              color: $soul-gray-700;
              font-weight: 500;
              transition: all 0.3s ease;
            }
          }
        }

        .custom-age-range {
          margin-top: 24rpx;
          padding: 24rpx;
          background: rgba(255, 255, 255, 0.6);
          border-radius: 20rpx;
          backdrop-filter: blur(10rpx);
        }

        .age-range-visual {
          margin: 32rpx 0;

          .age-line {
            position: relative;
            height: 8rpx;
            margin: 40rpx 0 32rpx;

            .age-line-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 8rpx;
              background: #e5e7eb;
              border-radius: 4rpx;
            }

            .age-line-active {
              position: absolute;
              top: 0;
              height: 8rpx;
              background: linear-gradient(135deg, #f78ca0, #a6c1ee);
              border-radius: 4rpx;
              transition: all 0.3s ease;
            }

            .age-thumb {
              position: absolute;
              top: -16rpx;
              width: 40rpx;
              height: 40rpx;
              background: #fff;
              border: 3rpx solid #f78ca0;
              border-radius: 50%;
              transform: translateX(-50%);
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
              transition: all 0.3s ease;

              &.age-thumb-max {
                border-color: #a6c1ee;
              }

              .age-thumb-text {
                position: absolute;
                top: -36rpx;
                font-size: 20rpx;
                color: $soul-gray-700;
                font-weight: 600;
                white-space: nowrap;
                background: rgba(255, 255, 255, 0.9);
                padding: 4rpx 8rpx;
                border-radius: 8rpx;
                box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
              }
            }
          }

          .age-scale {
            display: flex;
            justify-content: space-between;
            margin-top: 16rpx;

            .scale-text {
              font-size: 20rpx;
              color: $soul-gray-500;
              font-weight: 500;
            }
          }
        }

        .slider-container {
          margin: 20rpx 0;

          .slider-label {
            display: block;
            font-size: 22rpx;
            color: $soul-gray-600;
            margin-bottom: 12rpx;
            font-weight: 500;
          }

          .age-slider {
            width: 100%;
          }

          .no-limit-option {
            margin-top: 16rpx;
            text-align: center;

            .no-limit-text {
              font-size: 24rpx;
              color: $soul-gray-600;
              padding: 12rpx 24rpx;
              border: 2rpx solid #e5e7eb;
              border-radius: 20rpx;
              background: rgba(255, 255, 255, 0.8);
              transition: all 0.3s ease;

              &.active {
                background: linear-gradient(135deg, #f78ca0, #a6c1ee);
                border-color: transparent;
                color: #fff;
                font-weight: 600;
              }
            }
          }
        }
      }
    }

    .filter-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16rpx;
      margin-top: 12rpx;

      .filter-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 16rpx 24rpx;
        border-radius: 12rpx;
        font-weight: 600;
        font-size: 22rpx;
        transition: all 0.2s ease;

        &.reset {
          background: linear-gradient(to right, #868f96 0%, #596164 100%);
          color: white;
        }

        &.apply {
          background: linear-gradient(to right, #fbc2eb, #a6c1ee);
          color: white;
        }

        &:hover {
          opacity: 0.85;
          transform: translateY(-2rpx);
        }
      }
    }
  }
}

.submission-list {
  flex: 1;
  height: calc(100vh - 500rpx); // 调整高度
}

.list-container {
  padding: 0 20rpx 140rpx 20rpx; // 增加底部内边距，避免被底部菜单栏遮盖
}

.submission-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx; // 缩小间距
}

.loading-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  color: #f78ca0;

  .loading-icon {
    animation: spin 2s linear infinite;
    margin-bottom: 24rpx;
  }

  .loading-text {
    font-size: 28rpx;
    font-weight: 600;
  }
}

.empty-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-icon {
    margin-bottom: 24rpx;
  }

  .empty-title {
    font-size: 32rpx;
    color: $soul-gray-700;
    font-weight: 600;
    margin-bottom: 12rpx;
  }

  .empty-subtitle {
    font-size: 26rpx;
    color: $soul-gray-500;
    margin-bottom: 40rpx;
    line-height: 1.5;
  }

  .search-suggestions {
    width: 100%;
    margin-bottom: 40rpx;

    .suggestions-title {
      font-size: 24rpx;
      color: $soul-gray-600;
      margin-bottom: 20rpx;
      display: block;
    }

    .suggestions-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      justify-content: center;

      .suggestion-tag {
        padding: 12rpx 24rpx;
        background: rgba(247, 140, 160, 0.1);
        border: 2rpx solid rgba(247, 140, 160, 0.3);
        border-radius: 20rpx;
        transition: all 0.3s ease;

        &:active {
          background: rgba(247, 140, 160, 0.2);
          transform: scale(0.95);
        }

        .tag-text {
          font-size: 24rpx;
          color: #f78ca0;
          font-weight: 500;
        }
      }
    }
  }

  .reset-filters-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    padding: 16rpx 32rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 2rpx solid #f78ca0;
    border-radius: 25rpx;
    transition: all 0.3s ease;
    backdrop-filter: blur(10rpx);

    &:active {
      background: rgba(247, 140, 160, 0.1);
      transform: scale(0.95);
    }

    .reset-text {
      font-size: 26rpx;
      color: #f78ca0;
      font-weight: 500;
    }
  }
}

// 底部菜单栏
.bottom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 16rpx 0;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid rgba(209, 213, 219, 0.2);
  z-index: 1000;
  height: 120rpx;

  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    padding: 12rpx 20rpx;
    transition: all 0.3s ease;
    min-width: 80rpx;

    &.active {
      .tab-text {
        color: #f78ca0;
        font-weight: 600;
      }
    }

    .add-btn {
      width: 56rpx;
      height: 56rpx;
      background: linear-gradient(135deg, #f78ca0, #fbc2eb);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 6rpx 20rpx rgba(247, 140, 160, 0.4);
      transform: translateY(-8rpx);
    }

    .tab-text {
      font-size: 24rpx;
      color: $soul-gray-500;
      font-weight: 500;
      text-align: center;
      line-height: 1.2;

      &.active {
        color: #f78ca0;
        font-weight: 600;
      }
    }
  }
}

.search-container {
  padding: 20rpx;
  padding-top: calc(var(--status-bar-height) + 20rpx);
  @include glass-effect(0.9);
  border-bottom: 1rpx solid $soul-gray-200;

  .search-bar {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 24rpx;
    padding: 20rpx 24rpx;

    .search-input {
      flex: 1;
      margin: 0 16rpx;
      font-size: 28rpx;
      color: $soul-gray-800;
    }

    .cancel-btn {
      font-size: 28rpx;
      color: $soul-primary;
      padding: 8rpx 16rpx;
    }
  }
}

// 动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
