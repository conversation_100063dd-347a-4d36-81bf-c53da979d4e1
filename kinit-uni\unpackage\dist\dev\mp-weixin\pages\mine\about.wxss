@charset "UTF-8";
/* uView的全局SCSS主题文件 */
/**
 * Soul风格主题变量
 */
/* Soul风格颜色变量 */
/* 主色调 - 温柔的粉色系 */
/* 辅助色 */
/* 中性色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更圆润的设计 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* Soul风格特有样式 */
/* 毛玻璃效果 */
/* 渐变背景 */
/* 阴影效果 */
/* 卡片样式 */
/* 按钮样式 */
.about-page.data-v-2c1eab8a {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}
.custom-navbar.data-v-2c1eab8a {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  padding-top: 25px;
}
.custom-navbar .navbar-content.data-v-2c1eab8a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
}
.custom-navbar .navbar-content .navbar-left.data-v-2c1eab8a, .custom-navbar .navbar-content .navbar-right.data-v-2c1eab8a {
  width: 60rpx;
  display: flex;
  justify-content: center;
}
.custom-navbar .navbar-content .navbar-title.data-v-2c1eab8a {
  font-size: 28rpx;
  font-weight: 600;
  color: #424242;
}
.about-content.data-v-2c1eab8a {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
  padding-bottom: 40rpx;
}
.app-info-card.data-v-2c1eab8a {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.app-info-card .app-logo.data-v-2c1eab8a {
  margin-bottom: 20rpx;
}
.app-info-card .app-name.data-v-2c1eab8a {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #424242;
  margin-bottom: 8rpx;
}
.app-info-card .app-version.data-v-2c1eab8a {
  display: block;
  font-size: 24rpx;
  color: #9E9E9E;
  margin-bottom: 16rpx;
}
.app-info-card .app-desc.data-v-2c1eab8a {
  font-size: 26rpx;
  color: #757575;
  line-height: 1.5;
}
.feature-section.data-v-2c1eab8a, .contact-section.data-v-2c1eab8a, .legal-section.data-v-2c1eab8a {
  margin-bottom: 24rpx;
}
.feature-section .section-title.data-v-2c1eab8a, .contact-section .section-title.data-v-2c1eab8a, .legal-section .section-title.data-v-2c1eab8a {
  font-size: 28rpx;
  font-weight: 600;
  color: #616161;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}
.feature-list .feature-item.data-v-2c1eab8a {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.feature-list .feature-item .feature-icon.data-v-2c1eab8a {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.feature-list .feature-item .feature-content.data-v-2c1eab8a {
  flex: 1;
}
.feature-list .feature-item .feature-content .feature-title.data-v-2c1eab8a {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #424242;
  margin-bottom: 6rpx;
}
.feature-list .feature-item .feature-content .feature-desc.data-v-2c1eab8a {
  font-size: 22rpx;
  color: #757575;
  line-height: 1.4;
}
.contact-card.data-v-2c1eab8a, .legal-card.data-v-2c1eab8a {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.contact-item.data-v-2c1eab8a {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.contact-item.data-v-2c1eab8a:last-child {
  border-bottom: none;
}
.contact-item.data-v-2c1eab8a:active {
  background: rgba(247, 140, 160, 0.05);
}
.contact-item .contact-icon.data-v-2c1eab8a {
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.contact-item .contact-content.data-v-2c1eab8a {
  flex: 1;
}
.contact-item .contact-content .contact-title.data-v-2c1eab8a {
  display: block;
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 4rpx;
}
.contact-item .contact-content .contact-value.data-v-2c1eab8a {
  font-size: 26rpx;
  font-weight: 600;
  color: #424242;
}
.contact-item .contact-action .action-text.data-v-2c1eab8a {
  font-size: 24rpx;
  color: #f78ca0;
  font-weight: 500;
}
.legal-item.data-v-2c1eab8a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.legal-item.data-v-2c1eab8a:last-child {
  border-bottom: none;
}
.legal-item.data-v-2c1eab8a:active {
  background: rgba(247, 140, 160, 0.05);
}
.legal-item .legal-title.data-v-2c1eab8a {
  font-size: 26rpx;
  font-weight: 500;
  color: #424242;
}
.copyright-section.data-v-2c1eab8a {
  text-align: center;
  padding: 40rpx 20rpx;
}
.copyright-section .copyright-text.data-v-2c1eab8a {
  display: block;
  font-size: 22rpx;
  color: #9E9E9E;
  margin-bottom: 8rpx;
}
.copyright-section .copyright-desc.data-v-2c1eab8a {
  font-size: 20rpx;
  color: #BDBDBD;
  line-height: 1.4;
}
