<template>
  <view class="create-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="left" size="32" color="#FF6B9D"></uni-icons>
        </view>
        <text class="navbar-title">发布投稿</text>
        <view class="navbar-right"></view>
      </view>
    </view>

    <scroll-view scroll-y="true" class="form-content">
      <view class="form-container">
        <!-- 基本信息卡片 -->
        <view class="form-card">
          <view class="card-header">
            <uni-icons type="person-filled" size="24" color="#f78ca0"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>

          <!-- 所在城市 -->
          <view class="form-item">
            <view class="form-label-wrapper">
              <text class="form-label">所在城市</text>
              <text class="required-mark">*</text>
            </view>
            <view class="city-selector" @click="showCityPicker">
              <text class="city-text" :class="{ placeholder: !selectedCity }">
                {{ selectedCity || '请选择所在城市' }}
              </text>
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <!-- 年龄 -->
          <view class="form-item">
            <view class="form-label-wrapper">
              <text class="form-label">年龄</text>
              <text class="required-mark">*</text>
            </view>
            <input 
              v-model="formData.age" 
              type="number" 
              placeholder="请输入年龄（16-60岁）"
              class="form-input"
              maxlength="2"
            />
          </view>

          <!-- 性别 -->
          <view class="form-item">
            <text class="form-label">性别 *</text>
            <view class="gender-options">
              <view 
                v-for="(option, index) in genderOptions" 
                :key="index"
                class="gender-option"
                :class="{ active: formData.gender === option.value }"
                @click="selectGender(option.value)"
              >
                <uni-icons :type="option.icon" size="20" :color="formData.gender === option.value ? '#f78ca0' : '#999'"></uni-icons>
                <text class="gender-text">{{ option.label }}</text>
              </view>
            </view>
          </view>

          <!-- 身高 -->
          <view class="form-item">
            <text class="form-label">身高 *</text>
            <input 
              v-model="formData.height" 
              type="number" 
              placeholder="请输入身高（cm）"
              class="form-input"
              maxlength="3"
            />
          </view>

          <!-- 职业 -->
          <view class="form-item">
            <text class="form-label">职业/工作状态 *</text>
            <input 
              v-model="formData.occupation" 
              type="text" 
              placeholder="如：学生、程序员、设计师等"
              class="form-input"
              maxlength="20"
            />
          </view>

          <!-- 是否接受异地 -->
          <view class="form-item">
            <text class="form-label">是否接受异地 *</text>
            <view class="distance-options">
              <view 
                class="distance-option"
                :class="{ active: formData.accept_long_distance === true }"
                @click="formData.accept_long_distance = true"
              >
                <uni-icons type="checkmarkempty" size="16" :color="formData.accept_long_distance === true ? '#10b981' : '#999'"></uni-icons>
                <text class="distance-text">接受异地</text>
              </view>
              <view 
                class="distance-option"
                :class="{ active: formData.accept_long_distance === false }"
                @click="formData.accept_long_distance = false"
              >
                <uni-icons type="closeempty" size="16" :color="formData.accept_long_distance === false ? '#ef4444' : '#999'"></uni-icons>
                <text class="distance-text">仅限同城</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 详细信息卡片 -->
        <view class="form-card">
          <view class="card-header">
            <uni-icons type="chat" size="24" color="#a6c1ee"></uni-icons>
            <text class="card-title">详细信息</text>
          </view>

          <!-- 自我介绍 -->
          <view class="form-item">
            <text class="form-label">自我介绍 *</text>
            <textarea 
              v-model="formData.self_intro" 
              placeholder="简单介绍一下自己，让大家更了解你～"
              class="form-textarea"
              maxlength="500"
              :show-confirm-bar="false"
            />
            <view class="char-count">{{ formData.self_intro.length }}/500</view>
          </view>

          <!-- 搭子要求 -->
          <view class="form-item">
            <text class="form-label">搭子要求 *</text>
            <textarea 
              v-model="formData.partner_requirements" 
              placeholder="描述一下你希望找到什么样的搭子～"
              class="form-textarea"
              maxlength="500"
              :show-confirm-bar="false"
            />
            <view class="char-count">{{ formData.partner_requirements.length }}/500</view>
          </view>
        </view>

        <!-- 图片上传卡片 -->
        <view class="form-card">
          <view class="card-header">
            <uni-icons type="image" size="24" color="#feb47b"></uni-icons>
            <text class="card-title">上传照片</text>
            <text class="card-subtitle">（1-8张，第一张为主图）</text>
          </view>

          <view class="image-upload-container">
            <view class="image-grid">
              <view 
                v-for="(image, index) in imageList" 
                :key="index"
                class="image-item"
                @click="previewImage(index)"
              >
                <image :src="image" class="upload-image" mode="aspectFill" />
                <view class="image-actions">
                  <view v-if="index === 0" class="main-badge">主图</view>
                  <view class="delete-btn" @click.stop="removeImage(index)">
                    <uni-icons type="close" size="16" color="#fff"></uni-icons>
                  </view>
                </view>
              </view>
              
              <view 
                v-if="imageList.length < 8" 
                class="upload-btn"
                @click="chooseImage"
              >
                <uni-icons type="plus" size="32" color="#999"></uni-icons>
                <text class="upload-text">添加照片</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 联系方式卡片 -->
        <view class="form-card">
          <view class="card-header">
            <uni-icons type="chat" size="24" color="#c8a8e9"></uni-icons>
            <text class="card-title">联系方式</text>
            <text class="card-subtitle">（选填）</text>
          </view>

          <!-- 微信号 -->
          <view class="form-item">
            <text class="form-label">微信号</text>
            <input
              v-model="formData.wechat_id"
              type="text"
              placeholder="请输入微信号"
              class="form-input"
              maxlength="50"
            />
          </view>

          <!-- 微信二维码 -->
          <view class="form-item">
            <text class="form-label">微信二维码</text>
            <view class="qrcode-upload-container">
              <view v-if="wechatQRCode" class="qrcode-preview" @click="previewQRCode">
                <image :src="wechatQRCode" class="qrcode-image" mode="aspectFit" />
                <view class="qrcode-actions">
                  <view class="qrcode-delete" @click.stop="removeQRCode">
                    <uni-icons type="close" size="16" color="#fff"></uni-icons>
                  </view>
                </view>
              </view>

              <view v-else class="qrcode-upload-btn" @click="chooseQRCode">
                <uni-icons type="plus" size="32" color="#999"></uni-icons>
                <text class="upload-text">上传微信二维码</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
          <view class="submit-btn" @click="submitForm" :class="{ disabled: submitting }">
            <view v-if="submitting" class="loading-icon">
              <uni-icons type="spinner-cycle" size="24" color="#fff"></uni-icons>
            </view>
            <text class="submit-text">{{ submitting ? '提交中...' : '发布投稿' }}</text>
          </view>
          
          <view class="tips-text">
            <text>投稿将在审核通过后展示，请确保信息真实有效</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 城市选择器 -->
    <uni-popup ref="cityPopup" type="bottom" background-color="#ffffff">
      <view class="city-picker">
        <view class="picker-header">
          <text class="picker-title">选择所在城市</text>
          <view class="picker-close" @click="hideCityPicker">
            <uni-icons type="close" size="24" color="#999"></uni-icons>
          </view>
        </view>
        <picker-view class="picker-view" :value="pickerValue" @change="onPickerChange">
          <picker-view-column>
            <view v-for="(province, index) in provinces" :key="index" class="picker-item">
              {{ province.name }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(city, index) in currentCities" :key="index" class="picker-item">
              {{ city.name }}
            </view>
          </picker-view-column>
        </picker-view>
        <view class="picker-actions">
          <view class="picker-btn cancel" @click="hideCityPicker">取消</view>
          <view class="picker-btn confirm" @click="confirmCity">确定</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import submissionApi from '@/common/api/submission.js'

export default {
  data() {
    return {
      formData: {
        province: '',
        city: '',
        age: '',
        gender: '女', // 默认选择女
        height: '',
        occupation: '',
        self_intro: '',
        partner_requirements: '',
        accept_long_distance: null,
        wechat_id: '',
        category: '交友'
      },
      imageList: [],
      wechatQRCode: '', // 微信二维码
      submitting: false,
      genderOptions: [
        { label: '女', value: '女', icon: 'person-filled' },
        { label: '男', value: '男', icon: 'person' }
      ],
      // 城市数据
      provinces: [
        { name: '北京', cities: [{ name: '北京' }] },
        { name: '上海', cities: [{ name: '上海' }] },
        { name: '天津', cities: [{ name: '天津' }] },
        { name: '重庆', cities: [{ name: '重庆' }] },
        { name: '河北', cities: [
          { name: '石家庄' }, { name: '唐山' }, { name: '秦皇岛' }, { name: '邯郸' },
          { name: '邢台' }, { name: '保定' }, { name: '张家口' }, { name: '承德' },
          { name: '沧州' }, { name: '廊坊' }, { name: '衡水' }
        ]},
        { name: '山西', cities: [
          { name: '太原' }, { name: '大同' }, { name: '阳泉' }, { name: '长治' },
          { name: '晋城' }, { name: '朔州' }, { name: '晋中' }, { name: '运城' },
          { name: '忻州' }, { name: '临汾' }, { name: '吕梁' }
        ]},
        { name: '内蒙古', cities: [
          { name: '呼和浩特' }, { name: '包头' }, { name: '乌海' }, { name: '赤峰' },
          { name: '通辽' }, { name: '鄂尔多斯' }, { name: '呼伦贝尔' }, { name: '巴彦淖尔' },
          { name: '乌兰察布' }, { name: '兴安盟' }, { name: '锡林郭勒盟' }, { name: '阿拉善盟' }
        ]},
        { name: '辽宁', cities: [
          { name: '沈阳' }, { name: '大连' }, { name: '鞍山' }, { name: '抚顺' },
          { name: '本溪' }, { name: '丹东' }, { name: '锦州' }, { name: '营口' },
          { name: '阜新' }, { name: '辽阳' }, { name: '盘锦' }, { name: '铁岭' },
          { name: '朝阳' }, { name: '葫芦岛' }
        ]},
        { name: '吉林', cities: [
          { name: '长春' }, { name: '吉林' }, { name: '四平' }, { name: '辽源' },
          { name: '通化' }, { name: '白山' }, { name: '松原' }, { name: '白城' },
          { name: '延边朝鲜族自治州' }
        ]},
        { name: '黑龙江', cities: [
          { name: '哈尔滨' }, { name: '齐齐哈尔' }, { name: '鸡西' }, { name: '鹤岗' },
          { name: '双鸭山' }, { name: '大庆' }, { name: '伊春' }, { name: '佳木斯' },
          { name: '七台河' }, { name: '牡丹江' }, { name: '黑河' }, { name: '绥化' },
          { name: '大兴安岭地区' }
        ]},
        { name: '江苏', cities: [
          { name: '南京' }, { name: '无锡' }, { name: '徐州' }, { name: '常州' },
          { name: '苏州' }, { name: '南通' }, { name: '连云港' }, { name: '淮安' },
          { name: '盐城' }, { name: '扬州' }, { name: '镇江' }, { name: '泰州' }, { name: '宿迁' }
        ]},
        { name: '浙江', cities: [
          { name: '杭州' }, { name: '宁波' }, { name: '温州' }, { name: '嘉兴' },
          { name: '湖州' }, { name: '绍兴' }, { name: '金华' }, { name: '衢州' },
          { name: '舟山' }, { name: '台州' }, { name: '丽水' }
        ]},
        { name: '安徽', cities: [
          { name: '合肥' }, { name: '芜湖' }, { name: '蚌埠' }, { name: '淮南' },
          { name: '马鞍山' }, { name: '淮北' }, { name: '铜陵' }, { name: '安庆' },
          { name: '黄山' }, { name: '滁州' }, { name: '阜阳' }, { name: '宿州' },
          { name: '六安' }, { name: '亳州' }, { name: '池州' }, { name: '宣城' }
        ]},
        { name: '福建', cities: [
          { name: '福州' }, { name: '厦门' }, { name: '莆田' }, { name: '三明' },
          { name: '泉州' }, { name: '漳州' }, { name: '南平' }, { name: '龙岩' }, { name: '宁德' }
        ]},
        { name: '江西', cities: [
          { name: '南昌' }, { name: '景德镇' }, { name: '萍乡' }, { name: '九江' },
          { name: '新余' }, { name: '鹰潭' }, { name: '赣州' }, { name: '吉安' },
          { name: '宜春' }, { name: '抚州' }, { name: '上饶' }
        ]},
        { name: '山东', cities: [
          { name: '济南' }, { name: '青岛' }, { name: '淄博' }, { name: '枣庄' },
          { name: '东营' }, { name: '烟台' }, { name: '潍坊' }, { name: '济宁' },
          { name: '泰安' }, { name: '威海' }, { name: '日照' }, { name: '临沂' },
          { name: '德州' }, { name: '聊城' }, { name: '滨州' }, { name: '菏泽' }
        ]},
        { name: '河南', cities: [
          { name: '郑州' }, { name: '开封' }, { name: '洛阳' }, { name: '平顶山' },
          { name: '安阳' }, { name: '鹤壁' }, { name: '新乡' }, { name: '焦作' },
          { name: '濮阳' }, { name: '许昌' }, { name: '漯河' }, { name: '三门峡' },
          { name: '南阳' }, { name: '商丘' }, { name: '信阳' }, { name: '周口' },
          { name: '驻马店' }, { name: '济源' }
        ]},
        { name: '湖北', cities: [
          { name: '武汉' }, { name: '黄石' }, { name: '十堰' }, { name: '宜昌' },
          { name: '襄阳' }, { name: '鄂州' }, { name: '荆门' }, { name: '孝感' },
          { name: '荆州' }, { name: '黄冈' }, { name: '咸宁' }, { name: '随州' },
          { name: '恩施土家族苗族自治州' }, { name: '仙桃' }, { name: '潜江' }, { name: '天门' }, { name: '神农架林区' }
        ]},
        { name: '湖南', cities: [
          { name: '长沙' }, { name: '株洲' }, { name: '湘潭' }, { name: '衡阳' },
          { name: '邵阳' }, { name: '岳阳' }, { name: '常德' }, { name: '张家界' },
          { name: '益阳' }, { name: '郴州' }, { name: '永州' }, { name: '怀化' },
          { name: '娄底' }, { name: '湘西土家族苗族自治州' }
        ]},
        { name: '广东', cities: [
          { name: '广州' }, { name: '韶关' }, { name: '深圳' }, { name: '珠海' },
          { name: '汕头' }, { name: '佛山' }, { name: '江门' }, { name: '湛江' },
          { name: '茂名' }, { name: '肇庆' }, { name: '惠州' }, { name: '梅州' },
          { name: '汕尾' }, { name: '河源' }, { name: '阳江' }, { name: '清远' },
          { name: '东莞' }, { name: '中山' }, { name: '潮州' }, { name: '揭阳' }, { name: '云浮' }
        ]},
        { name: '广西', cities: [
          { name: '南宁' }, { name: '柳州' }, { name: '桂林' }, { name: '梧州' },
          { name: '北海' }, { name: '防城港' }, { name: '钦州' }, { name: '贵港' },
          { name: '玉林' }, { name: '百色' }, { name: '贺州' }, { name: '河池' },
          { name: '来宾' }, { name: '崇左' }
        ]},
        { name: '海南', cities: [
          { name: '海口' }, { name: '三亚' }, { name: '三沙' }, { name: '儋州' },
          { name: '五指山' }, { name: '琼海' }, { name: '文昌' }, { name: '万宁' },
          { name: '东方' }, { name: '定安' }, { name: '屯昌' }, { name: '澄迈' },
          { name: '临高' }, { name: '白沙黎族自治县' }, { name: '昌江黎族自治县' },
          { name: '乐东黎族自治县' }, { name: '陵水黎族自治县' }, { name: '保亭黎族苗族自治县' }, { name: '琼中黎族苗族自治县' }
        ]},
        { name: '四川', cities: [
          { name: '成都' }, { name: '自贡' }, { name: '攀枝花' }, { name: '泸州' },
          { name: '德阳' }, { name: '绵阳' }, { name: '广元' }, { name: '遂宁' },
          { name: '内江' }, { name: '乐山' }, { name: '南充' }, { name: '眉山' },
          { name: '宜宾' }, { name: '广安' }, { name: '达州' }, { name: '雅安' },
          { name: '巴中' }, { name: '资阳' }, { name: '阿坝藏族羌族自治州' },
          { name: '甘孜藏族自治州' }, { name: '凉山彝族自治州' }
        ]},
        { name: '贵州', cities: [
          { name: '贵阳' }, { name: '六盘水' }, { name: '遵义' }, { name: '安顺' },
          { name: '毕节' }, { name: '铜仁' }, { name: '黔西南布依族苗族自治州' },
          { name: '黔东南苗族侗族自治州' }, { name: '黔南布依族苗族自治州' }
        ]},
        { name: '云南', cities: [
          { name: '昆明' }, { name: '曲靖' }, { name: '玉溪' }, { name: '保山' },
          { name: '昭通' }, { name: '丽江' }, { name: '普洱' }, { name: '临沧' },
          { name: '楚雄彝族自治州' }, { name: '红河哈尼族彝族自治州' },
          { name: '文山壮族苗族自治州' }, { name: '西双版纳傣族自治州' },
          { name: '大理白族自治州' }, { name: '德宏傣族景颇族自治州' },
          { name: '怒江傈僳族自治州' }, { name: '迪庆藏族自治州' }
        ]},
        { name: '西藏', cities: [
          { name: '拉萨' }, { name: '日喀则' }, { name: '昌都' }, { name: '林芝' },
          { name: '山南' }, { name: '那曲' }, { name: '阿里地区' }
        ]},
        { name: '陕西', cities: [
          { name: '西安' }, { name: '铜川' }, { name: '宝鸡' }, { name: '咸阳' },
          { name: '渭南' }, { name: '延安' }, { name: '汉中' }, { name: '榆林' },
          { name: '安康' }, { name: '商洛' }
        ]},
        { name: '甘肃', cities: [
          { name: '兰州' }, { name: '嘉峪关' }, { name: '金昌' }, { name: '白银' },
          { name: '天水' }, { name: '武威' }, { name: '张掖' }, { name: '平凉' },
          { name: '酒泉' }, { name: '庆阳' }, { name: '定西' }, { name: '陇南' },
          { name: '临夏回族自治州' }, { name: '甘南藏族自治州' }
        ]},
        { name: '青海', cities: [
          { name: '西宁' }, { name: '海东' }, { name: '海北藏族自治州' },
          { name: '黄南藏族自治州' }, { name: '海南藏族自治州' },
          { name: '果洛藏族自治州' }, { name: '玉树藏族自治州' }, { name: '海西蒙古族藏族自治州' }
        ]},
        { name: '宁夏', cities: [
          { name: '银川' }, { name: '石嘴山' }, { name: '吴忠' }, { name: '固原' }, { name: '中卫' }
        ]},
        { name: '新疆', cities: [
          { name: '乌鲁木齐' }, { name: '克拉玛依' }, { name: '吐鲁番' }, { name: '哈密' },
          { name: '昌吉回族自治州' }, { name: '博尔塔拉蒙古自治州' },
          { name: '巴音郭楞蒙古自治州' }, { name: '阿克苏地区' }, { name: '克孜勒苏柯尔克孜自治州' },
          { name: '喀什地区' }, { name: '和田地区' }, { name: '伊犁哈萨克自治州' },
          { name: '塔城地区' }, { name: '阿勒泰地区' }, { name: '石河子' }, { name: '阿拉尔' },
          { name: '图木舒克' }, { name: '五家渠' }, { name: '北屯' }, { name: '铁门关' },
          { name: '双河' }, { name: '可克达拉' }, { name: '昆玉' }, { name: '胡杨河' }
        ]},
        { name: '中国香港', cities: [
          { name: '中西区' }, { name: '湾仔区' }, { name: '东区' }, { name: '南区' },
          { name: '油尖旺区' }, { name: '深水埗区' }, { name: '九龙城区' }, { name: '黄大仙区' },
          { name: '观塘区' }, { name: '荃湾区' }, { name: '屯门区' }, { name: '元朗区' },
          { name: '北区' }, { name: '大埔区' }, { name: '沙田区' }, { name: '西贡区' },
          { name: '葵青区' }, { name: '离岛区' }
        ]},
        { name: '中国澳门', cities: [
          { name: '澳门半岛' }, { name: '氹仔' }, { name: '路环' }
        ]},
        { name: '中国台湾', cities: [
          { name: '台北市' }, { name: '新北市' }, { name: '桃园市' }, { name: '台中市' },
          { name: '台南市' }, { name: '高雄市' }, { name: '基隆市' }, { name: '新竹市' },
          { name: '嘉义市' }, { name: '新竹县' }, { name: '苗栗县' }, { name: '彰化县' },
          { name: '南投县' }, { name: '云林县' }, { name: '嘉义县' }, { name: '屏东县' },
          { name: '宜兰县' }, { name: '花莲县' }, { name: '台东县' }, { name: '澎湖县' },
          { name: '金门县' }, { name: '连江县' }
        ]}
      ],
      pickerValue: [0, 0],
      selectedProvinceIndex: 0,
      selectedCityIndex: 0
    }
  },
  computed: {
    selectedCity() {
      if (this.formData.province && this.formData.city) {
        return `${this.formData.province}-${this.formData.city}`
      }
      return ''
    },
    currentCities() {
      return this.provinces[this.selectedProvinceIndex]?.cities || []
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    selectGender(value) {
      this.formData.gender = value
    },
    
    showCityPicker() {
      this.$refs.cityPopup.open()
    },
    
    hideCityPicker() {
      this.$refs.cityPopup.close()
    },
    
    onPickerChange(e) {
      this.pickerValue = e.detail.value
      this.selectedProvinceIndex = e.detail.value[0]
      this.selectedCityIndex = e.detail.value[1]
    },
    
    confirmCity() {
      const province = this.provinces[this.selectedProvinceIndex]
      const city = this.currentCities[this.selectedCityIndex]

      if (!province || !city) {
        uni.showToast({
          title: '请选择有效的城市',
          icon: 'none'
        })
        return
      }

      this.formData.province = province.name
      this.formData.city = city.name
      this.hideCityPicker()
    },
    
    chooseImage() {
      const remainingCount = 8 - this.imageList.length
      uni.chooseImage({
        count: remainingCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.imageList.push(...res.tempFilePaths)
        }
      })
    },
    
    removeImage(index) {
      this.imageList.splice(index, 1)
    },
    
    previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: index
      })
    },

    chooseQRCode() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.wechatQRCode = res.tempFilePaths[0]
        }
      })
    },

    removeQRCode() {
      this.wechatQRCode = ''
    },

    previewQRCode() {
      uni.previewImage({
        urls: [this.wechatQRCode],
        current: 0
      })
    },
    
    validateForm() {
      if (!this.formData.province || !this.formData.city) {
        uni.showToast({ title: '请选择所在城市', icon: 'none' })
        return false
      }
      
      if (!this.formData.age || this.formData.age < 16 || this.formData.age > 60) {
        uni.showToast({ title: '请输入正确的年龄（16-60岁）', icon: 'none' })
        return false
      }
      
      if (!this.formData.height || this.formData.height < 140 || this.formData.height > 220) {
        uni.showToast({ title: '请输入正确的身高（140-220cm）', icon: 'none' })
        return false
      }
      
      if (!this.formData.occupation.trim()) {
        uni.showToast({ title: '请填写职业/工作状态', icon: 'none' })
        return false
      }
      
      if (!this.formData.self_intro.trim()) {
        uni.showToast({ title: '请填写自我介绍', icon: 'none' })
        return false
      }
      
      if (!this.formData.partner_requirements.trim()) {
        uni.showToast({ title: '请填写搭子要求', icon: 'none' })
        return false
      }
      
      if (this.formData.accept_long_distance === null || this.formData.accept_long_distance === undefined) {
        uni.showToast({ title: '请选择是否接受异地', icon: 'none' })
        return false
      }
      
      if (this.imageList.length === 0) {
        uni.showToast({ title: '请至少上传一张照片', icon: 'none' })
        return false
      }
      
      return true
    },
    
    async submitForm() {
      if (this.submitting) return
      
      if (!this.validateForm()) return
      
      this.submitting = true
      
      try {
        // 上传图片
        const imageUrls = await this.uploadImages()

        // 上传微信二维码
        let wechatQRCodeUrl = ''
        if (this.wechatQRCode) {
          wechatQRCodeUrl = await this.uploadSingleImage(this.wechatQRCode)
        }

        // 准备提交数据
        const submitData = {
          ...this.formData,
          cover_image: imageUrls[0], // 第一张作为主图
          images: imageUrls.slice(1).join(','), // 其余图片
          wechat_qrcode: wechatQRCodeUrl, // 微信二维码
          age: parseInt(this.formData.age),
          height: parseInt(this.formData.height),
          status: 'pending', // 用户投稿默认为待审核状态
          is_visible: false // 用户投稿默认不可见，需要管理员审核
        }
        
        // 提交表单
        const res = await submissionApi.createSubmission(submitData)
        
        if (res.code === 200) {
          uni.showToast({
            title: '投稿提交成功，等待审核',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          throw new Error(res.message || '提交失败')
        }
        
      } catch (error) {
        console.error('提交投稿失败:', error)
        uni.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none'
        })
      } finally {
        this.submitting = false
      }
    },
    
    async uploadImages() {
      const uploadPromises = this.imageList.map(imagePath => {
        return new Promise((resolve, reject) => {
          uni.uploadFile({
            url: `${require('@/config.js').baseUrl}/upload/image`,
            filePath: imagePath,
            name: 'file',
            header: {
              'Authorization': `Bearer ${uni.getStorageSync('token')}`
            },
            success: (res) => {
              try {
                const data = JSON.parse(res.data)
                if (data.code === 200) {
                  resolve(data.data.url)
                } else {
                  reject(new Error(data.message || '图片上传失败'))
                }
              } catch (e) {
                reject(new Error('图片上传响应解析失败'))
              }
            },
            fail: (error) => {
              reject(error)
            }
          })
        })
      })
      
      return Promise.all(uploadPromises)
    },

    async uploadSingleImage(imagePath) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: `${require('@/config.js').baseUrl}/upload/image`,
          filePath: imagePath,
          name: 'file',
          header: {
            'Authorization': `Bearer ${uni.getStorageSync('token')}`
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              if (data.code === 200) {
                resolve(data.data.url)
              } else {
                reject(new Error(data.message || '图片上传失败'))
              }
            } catch (e) {
              reject(new Error('图片上传响应解析失败'))
            }
          },
          fail: (error) => {
            reject(error)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.create-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;

    .navbar-left, .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: center;
    }

    .navbar-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }
  }
}

.form-content {
  height: calc(100vh - 120rpx);
}

.form-container {
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.form-card {
  @include glass-effect(0.6);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    gap: 8rpx;

    .card-title {
      font-size: 24rpx;
      font-weight: 700;
      color: $soul-gray-700;
    }

    .card-subtitle {
      font-size: 20rpx;
      color: $soul-gray-500;
      margin-left: auto;
    }
  }
}

.form-item {
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .form-label-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
    gap: 4rpx;
  }

  .form-label {
    font-size: 24rpx;
    font-weight: 600;
    color: $soul-gray-700;
  }

  .required-mark {
    font-size: 24rpx;
    color: #ef4444;
    font-weight: 600;
  }

  .form-input {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    border: 2rpx solid #e5e7eb;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.8);
    font-size: 26rpx;
    color: $soul-gray-800;
    box-sizing: border-box;

    &:focus {
      border-color: #f78ca0;
      box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.1);
    }
  }

  .form-textarea {
    width: 100%;
    min-height: 160rpx;
    padding: 20rpx;
    border: 2rpx solid #e5e7eb;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.8);
    font-size: 26rpx;
    color: $soul-gray-800;
    box-sizing: border-box;
    resize: none;

    &:focus {
      border-color: #f78ca0;
      box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.1);
    }
  }

  .char-count {
    text-align: right;
    font-size: 20rpx;
    color: $soul-gray-500;
    margin-top: 8rpx;
  }
}

.city-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);

  .city-text {
    font-size: 26rpx;
    color: $soul-gray-800;

    &.placeholder {
      color: #999;
    }
  }
}

.gender-options {
  display: flex;
  gap: 16rpx;

  .gender-option {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    padding: 20rpx;
    border: 2rpx solid #e5e7eb;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &.active {
      border-color: #f78ca0;
      background: rgba(247, 140, 160, 0.1);
    }

    .gender-text {
      font-size: 24rpx;
      color: $soul-gray-700;
      font-weight: 500;
    }
  }
}

.distance-options {
  display: flex;
  gap: 16rpx;

  .distance-option {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 20rpx;
    border: 2rpx solid #e5e7eb;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &.active {
      border-color: #10b981;
      background: rgba(16, 185, 129, 0.1);
    }

    .distance-text {
      font-size: 24rpx;
      color: $soul-gray-700;
      font-weight: 500;
    }
  }
}

.image-upload-container {
  .image-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
  }

  .image-item {
    position: relative;
    width: 100%;
    height: 200rpx;
    border-radius: 12rpx;
    overflow: hidden;

    .upload-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-actions {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 8rpx;

      .main-badge {
        background: #f78ca0;
        color: white;
        font-size: 18rpx;
        padding: 4rpx 8rpx;
        border-radius: 8rpx;
      }

      .delete-btn {
        width: 32rpx;
        height: 32rpx;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .upload-btn {
    width: 100%;
    height: 200rpx;
    border: 2rpx dashed #d1d5db;
    border-radius: 12rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    background: rgba(255, 255, 255, 0.5);

    .upload-text {
      font-size: 22rpx;
      color: #999;
    }
  }
}

.submit-section {
  margin-top: 40rpx;
  text-align: center;

  .submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    height: 88rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 44rpx;
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;

    &:active:not(.disabled) {
      transform: translateY(2rpx) scale(0.98);
    }

    &.disabled {
      opacity: 0.6;
    }

    .loading-icon {
      animation: spin 1s linear infinite;
    }

    .submit-text {
      font-size: 30rpx;
      color: white;
      font-weight: 700;
    }
  }

  .tips-text {
    margin-top: 16rpx;
    font-size: 22rpx;
    color: $soul-gray-500;
    line-height: 1.4;
  }
}

.city-picker {
  background: white;
  border-radius: 20rpx 20rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .picker-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }
  }

  .picker-view {
    height: 400rpx;
  }

  .picker-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80rpx;
    font-size: 26rpx;
    color: $soul-gray-700;
  }

  .picker-actions {
    display: flex;
    border-top: 1rpx solid #f0f0f0;

    .picker-btn {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      font-weight: 600;

      &.cancel {
        color: $soul-gray-600;
        border-right: 1rpx solid #f0f0f0;
      }

      &.confirm {
        color: #f78ca0;
      }
    }
  }
}

.qrcode-upload-container {
  .qrcode-preview {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
    overflow: hidden;

    .qrcode-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .qrcode-actions {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      padding: 8rpx;

      .qrcode-delete {
        width: 32rpx;
        height: 32rpx;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .qrcode-upload-btn {
    width: 200rpx;
    height: 200rpx;
    border: 2rpx dashed #d1d5db;
    border-radius: 12rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    background: rgba(255, 255, 255, 0.5);

    .upload-text {
      font-size: 22rpx;
      color: #999;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
