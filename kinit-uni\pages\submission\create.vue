<template>
  <view class="create-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="left" size="32" color="#FF6B9D"></uni-icons>
        </view>
        <text class="navbar-title">发布投稿</text>
        <view class="navbar-right"></view>
      </view>
    </view>

    <scroll-view scroll-y="true" class="form-content">
      <view class="form-container">
        <!-- 基本信息卡片 -->
        <view class="form-card">
          <view class="card-header">
            <uni-icons type="person-filled" size="24" color="#f78ca0"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>

          <!-- 所在城市 -->
          <view class="form-item">
            <text class="form-label">所在城市 *</text>
            <view class="city-selector" @click="showCityPicker">
              <text class="city-text" :class="{ placeholder: !selectedCity }">
                {{ selectedCity || '请选择所在城市' }}
              </text>
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>

          <!-- 年龄 -->
          <view class="form-item">
            <text class="form-label">年龄 *</text>
            <input 
              v-model="formData.age" 
              type="number" 
              placeholder="请输入年龄（16-60岁）"
              class="form-input"
              maxlength="2"
            />
          </view>

          <!-- 性别 -->
          <view class="form-item">
            <text class="form-label">性别 *</text>
            <view class="gender-options">
              <view 
                v-for="(option, index) in genderOptions" 
                :key="index"
                class="gender-option"
                :class="{ active: formData.gender === option.value }"
                @click="selectGender(option.value)"
              >
                <uni-icons :type="option.icon" size="20" :color="formData.gender === option.value ? '#f78ca0' : '#999'"></uni-icons>
                <text class="gender-text">{{ option.label }}</text>
              </view>
            </view>
          </view>

          <!-- 身高 -->
          <view class="form-item">
            <text class="form-label">身高 *</text>
            <input 
              v-model="formData.height" 
              type="number" 
              placeholder="请输入身高（cm）"
              class="form-input"
              maxlength="3"
            />
          </view>

          <!-- 职业 -->
          <view class="form-item">
            <text class="form-label">职业/工作状态 *</text>
            <input 
              v-model="formData.occupation" 
              type="text" 
              placeholder="如：学生、程序员、设计师等"
              class="form-input"
              maxlength="20"
            />
          </view>

          <!-- 是否接受异地 -->
          <view class="form-item">
            <text class="form-label">是否接受异地 *</text>
            <view class="distance-options">
              <view 
                class="distance-option"
                :class="{ active: formData.accept_long_distance === true }"
                @click="formData.accept_long_distance = true"
              >
                <uni-icons type="checkmarkempty" size="16" :color="formData.accept_long_distance === true ? '#10b981' : '#999'"></uni-icons>
                <text class="distance-text">接受异地</text>
              </view>
              <view 
                class="distance-option"
                :class="{ active: formData.accept_long_distance === false }"
                @click="formData.accept_long_distance = false"
              >
                <uni-icons type="closeempty" size="16" :color="formData.accept_long_distance === false ? '#ef4444' : '#999'"></uni-icons>
                <text class="distance-text">仅限同城</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 详细信息卡片 -->
        <view class="form-card">
          <view class="card-header">
            <uni-icons type="chat" size="24" color="#a6c1ee"></uni-icons>
            <text class="card-title">详细信息</text>
          </view>

          <!-- 自我介绍 -->
          <view class="form-item">
            <text class="form-label">自我介绍 *</text>
            <textarea 
              v-model="formData.self_intro" 
              placeholder="简单介绍一下自己，让大家更了解你～"
              class="form-textarea"
              maxlength="500"
              :show-confirm-bar="false"
            />
            <view class="char-count">{{ formData.self_intro.length }}/500</view>
          </view>

          <!-- 搭子要求 -->
          <view class="form-item">
            <text class="form-label">搭子要求 *</text>
            <textarea 
              v-model="formData.partner_requirements" 
              placeholder="描述一下你希望找到什么样的搭子～"
              class="form-textarea"
              maxlength="500"
              :show-confirm-bar="false"
            />
            <view class="char-count">{{ formData.partner_requirements.length }}/500</view>
          </view>
        </view>

        <!-- 图片上传卡片 -->
        <view class="form-card">
          <view class="card-header">
            <uni-icons type="image" size="24" color="#feb47b"></uni-icons>
            <text class="card-title">上传照片</text>
            <text class="card-subtitle">（1-8张，第一张为主图）</text>
          </view>

          <view class="image-upload-container">
            <view class="image-grid">
              <view 
                v-for="(image, index) in imageList" 
                :key="index"
                class="image-item"
                @click="previewImage(index)"
              >
                <image :src="image" class="upload-image" mode="aspectFill" />
                <view class="image-actions">
                  <view v-if="index === 0" class="main-badge">主图</view>
                  <view class="delete-btn" @click.stop="removeImage(index)">
                    <uni-icons type="close" size="16" color="#fff"></uni-icons>
                  </view>
                </view>
              </view>
              
              <view 
                v-if="imageList.length < 8" 
                class="upload-btn"
                @click="chooseImage"
              >
                <uni-icons type="plus" size="32" color="#999"></uni-icons>
                <text class="upload-text">添加照片</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 联系方式卡片 -->
        <view class="form-card">
          <view class="card-header">
            <uni-icons type="chat" size="24" color="#c8a8e9"></uni-icons>
            <text class="card-title">联系方式</text>
            <text class="card-subtitle">（选填）</text>
          </view>

          <!-- 微信号 -->
          <view class="form-item">
            <text class="form-label">微信号</text>
            <input 
              v-model="formData.wechat_id" 
              type="text" 
              placeholder="请输入微信号"
              class="form-input"
              maxlength="50"
            />
          </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
          <view class="submit-btn" @click="submitForm" :class="{ disabled: submitting }">
            <view v-if="submitting" class="loading-icon">
              <uni-icons type="spinner-cycle" size="24" color="#fff"></uni-icons>
            </view>
            <text class="submit-text">{{ submitting ? '提交中...' : '发布投稿' }}</text>
          </view>
          
          <view class="tips-text">
            <text>投稿将在审核通过后展示，请确保信息真实有效</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 城市选择器 -->
    <uni-popup ref="cityPopup" type="bottom" background-color="#ffffff">
      <view class="city-picker">
        <view class="picker-header">
          <text class="picker-title">选择所在城市</text>
          <view class="picker-close" @click="hideCityPicker">
            <uni-icons type="close" size="24" color="#999"></uni-icons>
          </view>
        </view>
        <picker-view class="picker-view" :value="pickerValue" @change="onPickerChange">
          <picker-view-column>
            <view v-for="(province, index) in provinces" :key="index" class="picker-item">
              {{ province.name }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(city, index) in currentCities" :key="index" class="picker-item">
              {{ city.name }}
            </view>
          </picker-view-column>
        </picker-view>
        <view class="picker-actions">
          <view class="picker-btn cancel" @click="hideCityPicker">取消</view>
          <view class="picker-btn confirm" @click="confirmCity">确定</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import submissionApi from '@/common/api/submission.js'

export default {
  data() {
    return {
      formData: {
        province: '',
        city: '',
        age: '',
        gender: '女', // 默认选择女
        height: '',
        occupation: '',
        self_intro: '',
        partner_requirements: '',
        accept_long_distance: false,
        wechat_id: '',
        category: '交友'
      },
      imageList: [],
      submitting: false,
      genderOptions: [
        { label: '女', value: '女', icon: 'person-filled' },
        { label: '男', value: '男', icon: 'person' }
      ],
      // 城市数据
      provinces: [
        { name: '北京', cities: [{ name: '北京' }] },
        { name: '上海', cities: [{ name: '上海' }] },
        { name: '天津', cities: [{ name: '天津' }] },
        { name: '重庆', cities: [{ name: '重庆' }] },
        { name: '广东', cities: [
          { name: '广州' }, { name: '深圳' }, { name: '珠海' }, { name: '汕头' }, 
          { name: '佛山' }, { name: '韶关' }, { name: '湛江' }, { name: '肇庆' }
        ]},
        { name: '浙江', cities: [
          { name: '杭州' }, { name: '宁波' }, { name: '温州' }, { name: '嘉兴' },
          { name: '湖州' }, { name: '绍兴' }, { name: '金华' }, { name: '衢州' }
        ]},
        { name: '江苏', cities: [
          { name: '南京' }, { name: '无锡' }, { name: '徐州' }, { name: '常州' },
          { name: '苏州' }, { name: '南通' }, { name: '连云港' }, { name: '淮安' }
        ]},
        { name: '山东', cities: [
          { name: '济南' }, { name: '青岛' }, { name: '淄博' }, { name: '枣庄' },
          { name: '东营' }, { name: '烟台' }, { name: '潍坊' }, { name: '临沂' }
        ]}
      ],
      pickerValue: [0, 0],
      selectedProvinceIndex: 0,
      selectedCityIndex: 0
    }
  },
  computed: {
    selectedCity() {
      if (this.formData.province && this.formData.city) {
        return `${this.formData.province}-${this.formData.city}`
      }
      return ''
    },
    currentCities() {
      return this.provinces[this.selectedProvinceIndex]?.cities || []
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    selectGender(value) {
      this.formData.gender = value
    },
    
    showCityPicker() {
      this.$refs.cityPopup.open()
    },
    
    hideCityPicker() {
      this.$refs.cityPopup.close()
    },
    
    onPickerChange(e) {
      this.pickerValue = e.detail.value
      this.selectedProvinceIndex = e.detail.value[0]
      this.selectedCityIndex = e.detail.value[1]
    },
    
    confirmCity() {
      const province = this.provinces[this.selectedProvinceIndex]
      const city = this.currentCities[this.selectedCityIndex]
      
      this.formData.province = province.name
      this.formData.city = city.name
      this.hideCityPicker()
    },
    
    chooseImage() {
      const remainingCount = 8 - this.imageList.length
      uni.chooseImage({
        count: remainingCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.imageList.push(...res.tempFilePaths)
        }
      })
    },
    
    removeImage(index) {
      this.imageList.splice(index, 1)
    },
    
    previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: index
      })
    },
    
    validateForm() {
      if (!this.formData.province || !this.formData.city) {
        uni.showToast({ title: '请选择所在城市', icon: 'none' })
        return false
      }
      
      if (!this.formData.age || this.formData.age < 16 || this.formData.age > 60) {
        uni.showToast({ title: '请输入正确的年龄（16-60岁）', icon: 'none' })
        return false
      }
      
      if (!this.formData.height || this.formData.height < 140 || this.formData.height > 220) {
        uni.showToast({ title: '请输入正确的身高（140-220cm）', icon: 'none' })
        return false
      }
      
      if (!this.formData.occupation.trim()) {
        uni.showToast({ title: '请填写职业/工作状态', icon: 'none' })
        return false
      }
      
      if (!this.formData.self_intro.trim()) {
        uni.showToast({ title: '请填写自我介绍', icon: 'none' })
        return false
      }
      
      if (!this.formData.partner_requirements.trim()) {
        uni.showToast({ title: '请填写搭子要求', icon: 'none' })
        return false
      }
      
      if (this.formData.accept_long_distance === null || this.formData.accept_long_distance === undefined) {
        uni.showToast({ title: '请选择是否接受异地', icon: 'none' })
        return false
      }
      
      if (this.imageList.length === 0) {
        uni.showToast({ title: '请至少上传一张照片', icon: 'none' })
        return false
      }
      
      return true
    },
    
    async submitForm() {
      if (this.submitting) return
      
      if (!this.validateForm()) return
      
      this.submitting = true
      
      try {
        // 上传图片
        const imageUrls = await this.uploadImages()
        
        // 准备提交数据
        const submitData = {
          ...this.formData,
          cover_image: imageUrls[0], // 第一张作为主图
          images: imageUrls.slice(1).join(','), // 其余图片
          age: parseInt(this.formData.age),
          height: parseInt(this.formData.height)
        }
        
        // 提交表单
        const res = await submissionApi.createSubmission(submitData)
        
        if (res.code === 200) {
          uni.showToast({
            title: '投稿提交成功，等待审核',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          throw new Error(res.message || '提交失败')
        }
        
      } catch (error) {
        console.error('提交投稿失败:', error)
        uni.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none'
        })
      } finally {
        this.submitting = false
      }
    },
    
    async uploadImages() {
      const uploadPromises = this.imageList.map(imagePath => {
        return new Promise((resolve, reject) => {
          uni.uploadFile({
            url: `${require('@/config.js').baseUrl}/upload/image`,
            filePath: imagePath,
            name: 'file',
            success: (res) => {
              try {
                const data = JSON.parse(res.data)
                if (data.code === 200) {
                  resolve(data.data.url)
                } else {
                  reject(new Error(data.message || '图片上传失败'))
                }
              } catch (e) {
                reject(new Error('图片上传响应解析失败'))
              }
            },
            fail: (error) => {
              reject(error)
            }
          })
        })
      })
      
      return Promise.all(uploadPromises)
    }
  }
}
</script>

<style lang="scss" scoped>
.create-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;

    .navbar-left, .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: center;
    }

    .navbar-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }
  }
}

.form-content {
  height: calc(100vh - 120rpx);
}

.form-container {
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.form-card {
  @include glass-effect(0.6);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    gap: 8rpx;

    .card-title {
      font-size: 24rpx;
      font-weight: 700;
      color: $soul-gray-700;
    }

    .card-subtitle {
      font-size: 20rpx;
      color: $soul-gray-500;
      margin-left: auto;
    }
  }
}

.form-item {
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .form-label {
    display: block;
    font-size: 24rpx;
    font-weight: 600;
    color: $soul-gray-700;
    margin-bottom: 12rpx;
  }

  .form-input {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    border: 2rpx solid #e5e7eb;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.8);
    font-size: 26rpx;
    color: $soul-gray-800;
    box-sizing: border-box;

    &:focus {
      border-color: #f78ca0;
      box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.1);
    }
  }

  .form-textarea {
    width: 100%;
    min-height: 160rpx;
    padding: 20rpx;
    border: 2rpx solid #e5e7eb;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.8);
    font-size: 26rpx;
    color: $soul-gray-800;
    box-sizing: border-box;
    resize: none;

    &:focus {
      border-color: #f78ca0;
      box-shadow: 0 0 0 4rpx rgba(247, 140, 160, 0.1);
    }
  }

  .char-count {
    text-align: right;
    font-size: 20rpx;
    color: $soul-gray-500;
    margin-top: 8rpx;
  }
}

.city-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);

  .city-text {
    font-size: 26rpx;
    color: $soul-gray-800;

    &.placeholder {
      color: #999;
    }
  }
}

.gender-options {
  display: flex;
  gap: 16rpx;

  .gender-option {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    padding: 20rpx;
    border: 2rpx solid #e5e7eb;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &.active {
      border-color: #f78ca0;
      background: rgba(247, 140, 160, 0.1);
    }

    .gender-text {
      font-size: 24rpx;
      color: $soul-gray-700;
      font-weight: 500;
    }
  }
}

.distance-options {
  display: flex;
  gap: 16rpx;

  .distance-option {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 20rpx;
    border: 2rpx solid #e5e7eb;
    border-radius: 12rpx;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;

    &.active {
      border-color: #10b981;
      background: rgba(16, 185, 129, 0.1);
    }

    .distance-text {
      font-size: 24rpx;
      color: $soul-gray-700;
      font-weight: 500;
    }
  }
}

.image-upload-container {
  .image-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
  }

  .image-item {
    position: relative;
    width: 100%;
    height: 200rpx;
    border-radius: 12rpx;
    overflow: hidden;

    .upload-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-actions {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 8rpx;

      .main-badge {
        background: #f78ca0;
        color: white;
        font-size: 18rpx;
        padding: 4rpx 8rpx;
        border-radius: 8rpx;
      }

      .delete-btn {
        width: 32rpx;
        height: 32rpx;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .upload-btn {
    width: 100%;
    height: 200rpx;
    border: 2rpx dashed #d1d5db;
    border-radius: 12rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    background: rgba(255, 255, 255, 0.5);

    .upload-text {
      font-size: 22rpx;
      color: #999;
    }
  }
}

.submit-section {
  margin-top: 40rpx;
  text-align: center;

  .submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    height: 88rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 44rpx;
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;

    &:active:not(.disabled) {
      transform: translateY(2rpx) scale(0.98);
    }

    &.disabled {
      opacity: 0.6;
    }

    .loading-icon {
      animation: spin 1s linear infinite;
    }

    .submit-text {
      font-size: 30rpx;
      color: white;
      font-weight: 700;
    }
  }

  .tips-text {
    margin-top: 16rpx;
    font-size: 22rpx;
    color: $soul-gray-500;
    line-height: 1.4;
  }
}

.city-picker {
  background: white;
  border-radius: 20rpx 20rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .picker-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }
  }

  .picker-view {
    height: 400rpx;
  }

  .picker-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80rpx;
    font-size: 26rpx;
    color: $soul-gray-700;
  }

  .picker-actions {
    display: flex;
    border-top: 1rpx solid #f0f0f0;

    .picker-btn {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      font-weight: 600;

      &.cancel {
        color: $soul-gray-600;
        border-right: 1rpx solid #f0f0f0;
      }

      &.confirm {
        color: #f78ca0;
      }
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
