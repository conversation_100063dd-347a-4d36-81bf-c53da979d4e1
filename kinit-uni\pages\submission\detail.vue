<template>
  <view class="detail-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="left" size="32" color="#FF6B9D"></uni-icons>
        </view>
        <text class="navbar-title">投稿详情</text>
        <view class="navbar-right">
          <view class="favorite-btn" @click="toggleFavorite">
            <uni-icons
              :type="isFavorited ? 'heart-filled' : 'heart'"
              size="28"
              :color="isFavorited ? '#ff4757' : '#FF6B9D'"
            ></uni-icons>
          </view>
        </view>
      </view>
    </view>
    
    <scroll-view scroll-y="true" class="detail-content" v-if="submission">
      <!-- 图片轮播 -->
      <view class="image-section">
        <swiper
          class="image-swiper"
          :indicator-dots="imageList.length > 1"
          indicator-color="rgba(255, 255, 255, 0.5)"
          indicator-active-color="#f78ca0"
          :autoplay="false"
          :circular="true"
        >
          <swiper-item v-for="(image, index) in imageList" :key="index">
            <image
              :src="image"
              class="detail-image"
              mode="aspectFit"
              @click="previewImage(index)"
            />
          </swiper-item>
        </swiper>

        <!-- 模糊背景填充 -->
        <view class="blur-background">
          <image
            :src="imageList[0]"
            class="blur-image"
            mode="aspectFill"
          />
        </view>

        <!-- 置顶标签 -->
        <view v-if="submission && submission.is_top" class="pinned-badge">
          <uni-icons type="star-filled" size="16" color="#FFFFFF"></uni-icons>
          <text class="pinned-text">精选</text>
        </view>
      </view>

      <!-- 基本信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <uni-icons type="person-filled" size="24" color="#f78ca0"></uni-icons>
          <text class="card-title">基本信息</text>
        </view>

        <view class="info-grid">
          <!-- 城市（省份-城市） -->
          <view class="info-item">
            <uni-icons type="location" size="16" color="#f78ca0"></uni-icons>
            <text class="info-value">{{ formatLocation(submission) }}</text>
          </view>

          <!-- 年龄 -->
          <view class="info-item">
            <uni-icons type="calendar" size="16" color="#a6c1ee"></uni-icons>
            <text class="info-value">{{ submission.age }}岁</text>
          </view>

          <!-- 性别 -->
          <view class="info-item">
            <uni-icons type="heart" size="16" :color="genderColor"></uni-icons>
            <text class="info-value" :style="{ color: genderColor }">{{ submission.gender }}</text>
          </view>

          <!-- 身高 -->
          <view class="info-item">
            <uni-icons type="person" size="16" color="#feb47b"></uni-icons>
            <text class="info-value">{{ submission.height }}cm</text>
          </view>

          <!-- 工作状态 -->
          <view class="info-item">
            <uni-icons type="briefcase" size="16" color="#c8a8e9"></uni-icons>
            <text class="info-value">{{ submission.occupation }}</text>
          </view>

          <!-- 是否接受异地 -->
          <view class="info-item">
            <uni-icons type="map" size="16" color="#10b981"></uni-icons>
            <text class="info-value" :class="{ 'accept-distance': submission.accept_long_distance }">
              {{ submission.accept_long_distance ? '接受异地' : '不接受异地' }}
            </text>
          </view>

          <!-- 投稿编号 -->
          <view class="info-item">
            <uni-icons type="paperplane" size="16" color="#9E9E9E"></uni-icons>
            <text class="info-value">{{ submission.submission_code || submission.id }}</text>
          </view>
        </view>
      </view>
      
      <!-- 自我介绍卡片 -->
      <view v-if="submission.self_intro" class="content-card">
        <view class="card-header">
          <uni-icons type="chat" size="24" color="#a6c1ee"></uni-icons>
          <text class="card-title">自我介绍</text>
        </view>
        <view class="content-text">
          <text class="description-text">{{ submission.self_intro }}</text>
        </view>
      </view>

      <!-- 搭子要求卡片 -->
      <view v-if="submission.partner_requirements" class="content-card">
        <view class="card-header">
          <uni-icons type="heart" size="24" color="#feb47b"></uni-icons>
          <text class="card-title">搭子要求</text>
        </view>
        <view class="content-text">
          <text class="description-text">{{ submission.partner_requirements }}</text>
        </view>
      </view>

      <!-- 其他投稿推荐 -->
      <view class="related-submissions">
        <view class="section-header">
          <uni-icons type="star" size="24" color="#f78ca0"></uni-icons>
          <text class="section-title">更多搭子</text>
        </view>
        <scroll-view scroll-x="true" class="submissions-scroll">
          <view class="submissions-container">
            <view
              v-for="item in relatedSubmissions"
              :key="item.id"
              class="mini-submission-card"
              @click="goToSubmission(item.id)"
            >
              <!-- 置顶标签 -->
              <view v-if="item.is_top" class="mini-top-badge">
                <text class="mini-top-text">置顶</text>
              </view>

              <!-- 封面图片 -->
              <view class="mini-cover-container">
                <image :src="item.cover_image" class="mini-cover" mode="aspectFill" />
              </view>

              <!-- 信息内容 -->
              <view class="mini-info-container">
                <view class="mini-info-row">
                  <!-- 城市（省份-城市） -->
                  <view class="mini-info-item">
                    <uni-icons type="location" size="12" color="#f78ca0"></uni-icons>
                    <text class="mini-info-text">{{ formatLocation(item) }}</text>
                  </view>

                  <!-- 年龄 -->
                  <view class="mini-info-item">
                    <uni-icons type="calendar" size="12" color="#a6c1ee"></uni-icons>
                    <text class="mini-info-text">{{ item.age }}岁</text>
                  </view>
                </view>

                <view class="mini-info-row">
                  <!-- 性别 -->
                  <view class="mini-info-item">
                    <uni-icons :type="getGenderIcon(item.gender)" size="12" :color="getGenderColor(item.gender)"></uni-icons>
                    <text class="mini-info-text" :style="{ color: getGenderColor(item.gender) }">{{ item.gender }}</text>
                  </view>

                  <!-- 身高 -->
                  <view class="mini-info-item">
                    <uni-icons type="person" size="12" color="#feb47b"></uni-icons>
                    <text class="mini-info-text">{{ item.height }}cm</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 发布时间 -->
      <view class="time-card">
        <view class="time-info">
          <uni-icons type="clock" size="20" color="#9E9E9E"></uni-icons>
          <text class="time-text">发布于 {{ submission && submission.create_datetime ? formatTime(submission.create_datetime) : '未知时间' }}</text>
        </view>
      </view>

      <!-- 获取联系方式按钮 -->
      <view class="contact-section">
        <view class="contact-btn" @click="showContactInfo">
          <view class="btn-icon">
            <uni-icons type="chat" size="24" color="#FFFFFF"></uni-icons>
          </view>
          <text class="btn-text">获取联系方式</text>
          <view class="btn-sparkle">✨</view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 联系方式弹窗 -->
    <uni-popup ref="contactPopup" type="center" background-color="rgba(0,0,0,0.4)">
      <view class="contact-modal">
        <!-- 装饰性背景 -->
        <view class="modal-bg-decoration">
          <view class="bg-circle bg-circle-1"></view>
          <view class="bg-circle bg-circle-2"></view>
          <view class="bg-circle bg-circle-3"></view>
        </view>

        <!-- 关闭按钮 -->
        <view class="modal-close" @click="hideContactInfo">
          <uni-icons type="close" size="40" color="#ffffff"></uni-icons>
        </view>

        <!-- 标题区域 -->
        <view class="modal-header">
          <view class="header-icon">
            <uni-icons type="heart-filled" size="48" color="#FF6B9D"></uni-icons>
          </view>
          <text class="modal-title">获取联系方式</text>
          <text class="modal-subtitle">让美好的相遇开始吧～</text>
        </view>

        <view class="modal-content">
          <!-- 步骤指引 -->
          <view class="steps-guide">
            <view class="step-item">
              <view class="step-number">1</view>
              <text class="step-text">复制投稿ID</text>
            </view>
            <view class="step-arrow">
              <uni-icons type="right" size="24" color="#FF6B9D"></uni-icons>
            </view>
            <view class="step-item">
              <view class="step-number">2</view>
              <text class="step-text">扫码添加客服</text>
            </view>
            <view class="step-arrow">
              <uni-icons type="right" size="24" color="#FF6B9D"></uni-icons>
            </view>
            <view class="step-item">
              <view class="step-number">3</view>
              <text class="step-text">发送ID获取联系</text>
            </view>
          </view>

          <!-- 投稿ID区域 -->
          <view class="contact-id-section">
            <view class="section-header">
              <uni-icons type="paperplane" size="32" color="#FF6B9D"></uni-icons>
              <text class="section-title">投稿ID</text>
            </view>
            <view class="contact-id-card">
              <view class="id-display">
                <text class="contact-id">{{ submission && submission.submission_code || (submission && submission.id) || '暂无' }}</text>
              </view>
              <view class="copy-btn" @click="copyContactId">
                <uni-icons type="copy" size="28" color="#ffffff"></uni-icons>
                <text class="copy-text">复制</text>
              </view>
            </view>
          </view>

          <!-- 客服二维码区域 -->
          <view class="service-qr-section">
            <view class="section-header">
              <uni-icons type="chat" size="32" color="#FF6B9D"></uni-icons>
              <text class="section-title">客服微信</text>
            </view>
            <view class="qr-container">
              <view class="qr-frame">
                <image :src="customerServiceQr || '/static/images/service-qr.png'" class="service-qr" mode="aspectFit"></image>
                <view class="qr-corners">
                  <view class="corner corner-tl"></view>
                  <view class="corner corner-tr"></view>
                  <view class="corner corner-bl"></view>
                  <view class="corner corner-br"></view>
                </view>
              </view>
              <text class="service-tip">{{ contactPopupContent }}</text>
            </view>
          </view>

          <!-- 温馨提示 -->
          <view class="warm-tips">
            <view class="tips-header">
              <uni-icons type="info" size="28" color="#FFA726"></uni-icons>
              <text class="tips-title">温馨提示</text>
            </view>
            <text class="tips-content">为了保护用户隐私，联系方式需通过客服获取。请耐心等待客服回复哦～</text>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>

<script>
import submissionApi from '@/common/api/submission.js'
import systemApi from '@/common/api/system.js'

export default {
  data() {
    return {
      submissionId: null,
      submission: null,
      loading: true,
      imageList: [],
      relatedSubmissions: [],
      systemConfig: null,
      isFavorited: false,
      favoriteLoading: false
    }
  },
  computed: {
    genderColor() {
      switch (this.submission && this.submission.gender) {
        case '男':
          return '#87CEEB'
        case '女':
          return '#FF6B9D'
        default:
          return '#C8A8E9'
      }
    },

    // 安全的submission数据访问
    safeSubmission() {
      return this.submission || {}
    },

    // 客服设置
    customerServiceQr() {
      const qrPath = this.systemConfig?.customer_service_qr
      if (qrPath && !qrPath.startsWith('http')) {
        // 如果是相对路径，添加API基础URL
        const config = require('@/config.js')
        return config.baseUrl + qrPath
      }
      return qrPath || '/static/images/service-qr.png'
    },

    contactPopupContent() {
      return this.systemConfig?.contact_popup_content || '扫码添加客服微信，发送投稿ID获取联系方式'
    }
  },
  onLoad(options) {
    if (options.id) {
      this.submissionId = parseInt(options.id)
      this.loadSystemConfig()
      this.loadSubmissionDetail()
      this.loadRelatedSubmissions()
    }
  },

  methods: {
    async loadSystemConfig() {
      try {
        const res = await systemApi.getSystemConfig()
        console.log('系统配置API响应:', res)
        if (res.code === 200) {
          this.systemConfig = res.data
          console.log('客服二维码配置:', this.systemConfig.customer_service_qr)
        }
      } catch (error) {
        console.error('加载系统配置失败:', error)
      }
    },

    async loadSubmissionDetail() {
      this.loading = true
      
      try {
        const res = await submissionApi.getSubmissionDetail(this.submissionId)
        
        if (res.code === 200) {
          this.submission = res.data
          this.processImages()
          // 检查收藏状态
          this.checkFavoriteStatus()
        } else {
          uni.showToast({
            title: '加载失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载投稿详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    processImages() {
      if (!this.submission) return

      this.imageList = [this.submission.cover_image]

      if (this.submission.images) {
        const additionalImages = this.submission.images.split(',').filter(img => img.trim())
        this.imageList.push(...additionalImages)
      }
    },
    
    previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: index
      })
    },
    
    showContactInfo() {
      this.$refs.contactPopup.open()
    },
    
    hideContactInfo() {
      this.$refs.contactPopup.close()
    },
    
    copyContactId() {
      if (!this.submission) {
        uni.showToast({
          title: '数据加载中，请稍后',
          icon: 'none'
        })
        return
      }

      const submissionId = this.submission.submission_code || (this.submission.id && this.submission.id.toString()) || '暂无'

      uni.setClipboardData({
        data: submissionId,
        success: () => {
          uni.showToast({
            title: '投稿ID已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },
    

    
    async toggleFavorite() {
      // 检查登录状态
      if (!this.$store.getters['user/isLoggedIn']) {
        uni.showModal({
          title: '登录提示',
          content: '需要登录后才能收藏投稿',
          confirmText: '立即登录',
          success: (res) => {
            if (res.confirm) {
              this.performWxLogin()
            }
          }
        })
        return
      }

      if (this.favoriteLoading) return

      this.favoriteLoading = true

      try {
        const res = await submissionApi.toggleFavorite(this.submissionId)
        if (res.code === 200) {
          this.isFavorited = !this.isFavorited
          uni.showToast({
            title: this.isFavorited ? '收藏成功' : '取消收藏',
            icon: 'success'
          })
        } else {
          throw new Error(res.message || '操作失败')
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        uni.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        })
      } finally {
        this.favoriteLoading = false
      }
    },

    async performWxLogin() {
      try {
        // 获取微信登录code
        const loginRes = await uni.login({ provider: 'weixin' })

        // 获取用户信息
        const userInfoRes = await uni.getUserProfile({
          desc: '用于完善用户资料'
        })

        // 调用登录接口
        await this.$store.dispatch('user/wxLogin', {
          code: loginRes.code,
          userInfo: userInfoRes.userInfo
        })

        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 登录成功后检查收藏状态
        this.checkFavoriteStatus()

      } catch (error) {
        console.error('微信登录失败:', error)
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
      }
    },

    async checkFavoriteStatus() {
      if (!this.$store.getters['user/isLoggedIn'] || !this.submissionId) {
        return
      }

      try {
        const res = await submissionApi.checkFavoriteStatus(this.submissionId)
        if (res.code === 200) {
          this.isFavorited = res.data.is_favorited
        }
      } catch (error) {
        console.error('检查收藏状态失败:', error)
      }
    },
    
    goBack() {
      // 点击主页按钮跳转并重置所有操作
      uni.navigateTo({
        url: '/pages/index'
      })
    },
    
    async loadRelatedSubmissions() {
      try {
        const res = await submissionApi.getSubmissionList({
          page: 1,
          size: 10
        })

        if (res.code === 200) {
          // 过滤掉当前投稿，随机选择5个
          const filtered = res.data.items.filter(item => item.id !== this.submissionId)
          const shuffled = filtered.sort(() => 0.5 - Math.random())
          this.relatedSubmissions = shuffled.slice(0, 5)
        }
      } catch (error) {
        console.error('加载相关投稿失败:', error)
      }
    },

    goToSubmission(id) {
      uni.navigateTo({
        url: `/pages/submission/detail?id=${id}`
      })
    },

    getGenderIcon(gender) {
      switch (gender) {
        case '男':
          return 'person'
        case '女':
          return 'person-filled'
        default:
          return 'person'
      }
    },

    getGenderColor(gender) {
      switch (gender) {
        case '男':
          return '#87ceeb'
        case '女':
          return '#f78ca0'
        default:
          return '#c8a8e9'
      }
    },

    formatTime(timeStr) {
      const date = new Date(timeStr)
      const now = new Date()
      const diff = now - date

      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else if (days < 7) {
        return `${days}天前`
      } else {
        return date.toLocaleDateString()
      }
    },

    // 格式化地理位置显示
    formatLocation(item) {
      if (!item) return ''

      // 直辖市列表
      const municipalities = ['北京', '上海', '天津', '重庆']

      // 如果有省份和城市信息
      if (item.province && item.city) {
        // 如果是直辖市，只显示城市名
        if (municipalities.includes(item.province)) {
          return item.city
        }
        // 其他情况显示省份-城市
        return `${item.province}-${item.city}`
      }

      // 只有城市信息
      if (item.city) {
        return item.city
      }

      // 只有省份信息
      if (item.province) {
        return item.province
      }

      return '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.custom-navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  @include glass-effect(0.6);
  padding-top: var(--status-bar-height);

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;

    .navbar-left, .navbar-right {
      width: 60rpx;
      display: flex;
      justify-content: center;
    }

    .navbar-title {
      font-size: 28rpx;
      font-weight: 600;
      color: $soul-gray-800;
    }

    .favorite-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(0.95);
      }
    }
  }
}

.detail-content {
  min-height: calc(100vh - 200rpx);
  padding-bottom: 40rpx;
}

.image-section {
  position: relative;
  height: 400rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  border-radius: 0 0 24rpx 24rpx;

  .blur-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .blur-image {
      width: 100%;
      height: 100%;
      filter: blur(20rpx);
      opacity: 0.3;
    }
  }

  .image-swiper {
    position: relative;
    height: 100%;
    z-index: 2;

    .detail-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .pinned-badge {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    display: flex;
    align-items: center;
    background: linear-gradient(to right, #ff7e5f, #feb47b);
    border-radius: 32rpx;
    padding: 6rpx 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
    z-index: 10;

    .pinned-text {
      font-size: 18rpx;
      color: $soul-white;
      font-weight: 700;
      margin-left: 4rpx;
    }
  }
}

.info-card, .content-card, .time-card {
  @include glass-effect(0.6);
  border-radius: 20rpx;
  margin: 0 20rpx 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}

.info-card, .content-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    gap: 8rpx;

    .card-title {
      font-size: 24rpx;
      font-weight: 700;
      color: $soul-gray-700;
    }
  }



  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12rpx;

    .info-item {
      display: flex;
      align-items: center;
      gap: 8rpx;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 12rpx;
      padding: 12rpx 16rpx;
      box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.05);



      .info-value {
        font-size: 22rpx;
        font-weight: 600;
        color: $soul-gray-700;

        &.accept-distance {
          color: #10b981;
        }
      }
    }
  }

  .content-text {
    .description-text {
      font-size: 24rpx;
      color: $soul-gray-700;
      line-height: 1.5;
    }
  }
}

.time-card {
  .time-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;

    .time-text {
      font-size: 20rpx;
      color: $soul-gray-500;
    }
  }
}

.related-submissions {
  margin: 0 20rpx 16rpx;
  @include glass-effect(0.6);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);

  .section-header {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 24rpx;
      font-weight: 700;
      color: $soul-gray-700;
    }
  }

  .submissions-scroll {
    width: 100%;
    white-space: nowrap;
  }

  .submissions-container {
    display: flex;
    gap: 16rpx;
    padding: 0 4rpx;
  }

  .mini-submission-card {
    position: relative;
    width: 200rpx;
    height: 240rpx;
    @include glass-effect(0.7);
    border-radius: 24rpx;
    overflow: hidden;
    flex-shrink: 0;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-4rpx);
    }

    .mini-top-badge {
      position: absolute;
      top: 8rpx;
      right: 8rpx;
      z-index: 10;
      background: linear-gradient(135deg, $soul-accent 0%, #FFE066 100%);
      border-radius: 12rpx;
      padding: 4rpx 8rpx;

      .mini-top-text {
        font-size: 16rpx;
        color: $soul-white;
        font-weight: 600;
      }
    }

    .mini-cover-container {
      position: relative;
      width: 100%;
      height: 160rpx;
      overflow: hidden;
      background-color: #f0f0f0;
      flex-shrink: 0;

      .mini-cover {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .mini-info-container {
      height: 80rpx;
      padding: 6rpx 8rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex-shrink: 0;
    }

    .mini-info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 4rpx;

      .mini-info-item {
        display: flex;
        align-items: center;
        gap: 2rpx;
        flex: 1;
        justify-content: flex-start;
        min-width: 0;
        overflow: hidden;

        .mini-info-text {
          font-size: 15rpx;
          color: $soul-gray-700;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}

.contact-section {
  margin: 0 20rpx 16rpx;

  .contact-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 32rpx;
    padding: 24rpx 40rpx;
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:active {
      transform: translateY(2rpx) scale(0.98);
      box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);

      &::before {
        left: 100%;
      }
    }

    .btn-icon {
      animation: heartBeat 2s ease-in-out infinite;
    }

    .btn-text {
      font-size: 30rpx;
      color: $soul-white;
      font-weight: 700;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }

    .btn-sparkle {
      position: absolute;
      top: -8rpx;
      right: -8rpx;
      font-size: 24rpx;
      animation: pulse 2s ease-in-out infinite;
    }
  }
}



.contact-modal {
  position: relative;
  width: 680rpx;
  max-height: 80vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.3);
  animation: modalSlideIn 0.3s ease-out;
  margin: 10vh 0;

  .modal-bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;

    .bg-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.bg-circle-1 {
        width: 120rpx;
        height: 120rpx;
        top: -60rpx;
        right: -60rpx;
        animation-delay: 0s;
      }

      &.bg-circle-2 {
        width: 80rpx;
        height: 80rpx;
        bottom: 100rpx;
        left: -40rpx;
        animation-delay: 2s;
      }

      &.bg-circle-3 {
        width: 60rpx;
        height: 60rpx;
        top: 200rpx;
        right: 50rpx;
        animation-delay: 4s;
      }
    }
  }

  .modal-close {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    width: 64rpx;
    height: 64rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    backdrop-filter: blur(10rpx);
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
      background: rgba(255, 255, 255, 0.3);
    }
  }

  .modal-header {
    text-align: center;
    padding: 48rpx 40rpx 32rpx;
    position: relative;

    .header-icon {
      margin-bottom: 16rpx;
      animation: heartBeat 2s ease-in-out infinite;
    }

    .modal-title {
      font-size: 40rpx;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 8rpx;
      display: block;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }

    .modal-subtitle {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
      display: block;
    }
  }

  .modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    margin: 0 24rpx 24rpx;
    border-radius: 24rpx;
    padding: 32rpx;
    position: relative;
    overflow-y: auto;
    max-height: calc(80vh - 200rpx);

    .steps-guide {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 32rpx;
      padding: 20rpx;
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      border-radius: 20rpx;

      .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;

        .step-number {
          width: 48rpx;
          height: 48rpx;
          background: #FF6B9D;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          font-weight: 600;
          margin-bottom: 8rpx;
          box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
        }

        .step-text {
          font-size: 22rpx;
          color: #8B4513;
          text-align: center;
          font-weight: 500;
        }
      }

      .step-arrow {
        margin: 0 16rpx;
        margin-top: -24rpx;
      }
    }

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .section-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        margin-left: 12rpx;
      }
    }

    .contact-id-section {
      margin-bottom: 32rpx;

      .contact-id-card {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16rpx;
        padding: 24rpx;
        box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);

        .id-display {
          flex: 1;

          .contact-id {
            font-size: 32rpx;
            font-weight: 700;
            color: #ffffff;
            letter-spacing: 2rpx;
          }
        }

        .copy-btn {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 12rpx;
          padding: 16rpx 24rpx;
          backdrop-filter: blur(10rpx);
          transition: all 0.2s ease;

          &:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.3);
          }

          .copy-text {
            font-size: 24rpx;
            color: #ffffff;
            margin-left: 8rpx;
            font-weight: 500;
          }
        }
      }
    }

    .service-qr-section {
      margin-bottom: 24rpx;

      .qr-container {
        text-align: center;

        .qr-frame {
          position: relative;
          display: inline-block;
          margin-bottom: 16rpx;
          padding: 16rpx;
          background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
          border-radius: 20rpx;
          box-shadow: 0 8rpx 24rpx rgba(168, 237, 234, 0.3);

          .service-qr {
            width: 160rpx;
            height: 160rpx;
            border-radius: 12rpx;
            display: block;
          }

          .qr-corners {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;

            .corner {
              position: absolute;
              width: 32rpx;
              height: 32rpx;
              border: 4rpx solid #FF6B9D;

              &.corner-tl {
                top: 12rpx;
                left: 12rpx;
                border-right: none;
                border-bottom: none;
                border-radius: 8rpx 0 0 0;
              }

              &.corner-tr {
                top: 12rpx;
                right: 12rpx;
                border-left: none;
                border-bottom: none;
                border-radius: 0 8rpx 0 0;
              }

              &.corner-bl {
                bottom: 12rpx;
                left: 12rpx;
                border-right: none;
                border-top: none;
                border-radius: 0 0 0 8rpx;
              }

              &.corner-br {
                bottom: 12rpx;
                right: 12rpx;
                border-left: none;
                border-top: none;
                border-radius: 0 0 8rpx 0;
              }
            }
          }
        }

        .service-tip {
          font-size: 24rpx;
          color: #666;
          line-height: 1.4;
        }
      }
    }

    .warm-tips {
      background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
      border-radius: 16rpx;
      padding: 20rpx;

      .tips-header {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .tips-title {
          font-size: 26rpx;
          font-weight: 600;
          color: #8B4513;
          margin-left: 8rpx;
        }
      }

      .tips-content {
        font-size: 24rpx;
        color: #8B4513;
        line-height: 1.5;
      }
    }
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 动画效果 */
@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(100rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>
