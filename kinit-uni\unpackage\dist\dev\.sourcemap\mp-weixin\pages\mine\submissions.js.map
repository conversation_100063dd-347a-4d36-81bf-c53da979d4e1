{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/submissions.vue?a09b", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/submissions.vue?b754", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/submissions.vue?0c75", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/submissions.vue?5967", "uni-app:///pages/mine/submissions.vue", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/submissions.vue?04f3", "webpack:///E:/kaifa/投稿/kinit2/kinit-uni/pages/mine/submissions.vue?3397"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentStatus", "statusTabs", "label", "value", "count", "submissionsList", "id", "title", "submission_code", "city", "province", "age", "gender", "occupation", "status", "cover_image", "views", "created_at", "computed", "filteredSubmissions", "onLoad", "methods", "goBack", "uni", "checkLoginAndLoad", "content", "confirmText", "cancelText", "success", "performWxLogin", "provider", "loginRes", "desc", "userInfoRes", "code", "userInfo", "icon", "console", "mockLogin", "mockUserInfo", "nickname", "avatar", "mockToken", "loadSubmissions", "switchStatus", "getStatusClass", "getStatusText", "getEmptyText", "formatLocation", "formatTime", "goToDetail", "url", "goToCreate", "editSubmission", "deleteSubmission"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AAC4K;AAC5K,gBAAgB,qLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAwoB,CAAgB,6pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiH5pB;EACAC;IACA;MACAC;MACAC,aACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC;MACA;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;IACA;IAEAC;MAAA;MACA;QACAD;UACAhB;UACAkB;UACAC;UACAC;UACAC;YACA;cACA;YACA;cACAL;YACA;UACA;QACA;QACA;MACA;MACA;IACA;IAEAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OASAN;kBAAAO;gBAAA;cAAA;gBAAAC;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGAR;kBACAS;gBACA;cAAA;gBAFAC;gBAAA,IAIAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA,OAGA;kBACAC;kBACAC;gBACA;cAAA;gBAEAZ;kBACAhB;kBACA6B;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;;gBAEA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAd;kBACAhB;kBACA6B;gBACA;gBACAb;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;kBACAjC;kBACAkC;kBACAC;kBACA7B;kBACAoB;gBACA,GAEA;gBACAU,wCAEA;gBACA;gBACA;gBAEAnB;kBACAhB;kBACA6B;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAO;MACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;UACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MAEA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA3B;QACA4B;MACA;IACA;IAEAC;MACA7B;QACA4B;MACA;IACA;IAEAE;MACA9B;QACA4B;MACA;IACA;IAEAG;MAAA;MACA/B;QACAhB;QACAkB;QACAG;UACA;YACA;YACA;cAAA;YAAA;YACA;cACA;cACA;cACAL;gBACAhB;gBACA6B;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjYA;AAAA;AAAA;AAAA;AAAuuC,CAAgB,osCAAG,EAAC,C;;;;;;;;;;;ACA3vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/submissions.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/submissions.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./submissions.vue?vue&type=template&id=608f4c38&scoped=true&\"\nvar renderjs\nimport script from \"./submissions.vue?vue&type=script&lang=js&\"\nexport * from \"./submissions.vue?vue&type=script&lang=js&\"\nimport style0 from \"./submissions.vue?vue&type=style&index=0&id=608f4c38&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"608f4c38\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/submissions.vue\"\nexport default component.exports", "export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submissions.vue?vue&type=template&id=608f4c38&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filteredSubmissions, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getStatusClass(item.status)\n    var m1 = _vm.getStatusText(item.status)\n    var m2 = _vm.formatTime(item.created_at)\n    var m3 = _vm.formatLocation(item)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n    }\n  })\n  var g0 = _vm.filteredSubmissions.length\n  var m4 = g0 === 0 ? _vm.getEmptyText() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        m4: m4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submissions.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submissions.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"submissions-page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\">\n      <view class=\"navbar-content\">\n        <view class=\"navbar-left\" @click=\"goBack\">\n          <uni-icons type=\"left\" size=\"32\" color=\"#FF6B9D\"></uni-icons>\n        </view>\n        <text class=\"navbar-title\">我的投稿</text>\n        <view class=\"navbar-right\">\n          <view class=\"action-btn\" @click=\"goToCreate\">\n            <uni-icons type=\"plus\" size=\"24\" color=\"#f78ca0\"></uni-icons>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 状态筛选 -->\n    <view class=\"filter-tabs\">\n      <view \n        v-for=\"(tab, index) in statusTabs\" \n        :key=\"index\"\n        class=\"filter-tab\"\n        :class=\"{ active: currentStatus === tab.value }\"\n        @click=\"switchStatus(tab.value)\"\n      >\n        <text class=\"tab-text\">{{ tab.label }}</text>\n        <view v-if=\"tab.count > 0\" class=\"tab-badge\">{{ tab.count }}</view>\n      </view>\n    </view>\n\n    <scroll-view scroll-y=\"true\" class=\"submissions-content\">\n      <!-- 投稿列表 -->\n      <view class=\"submissions-list\">\n        <view \n          v-for=\"(item, index) in filteredSubmissions\" \n          :key=\"index\"\n          class=\"submission-item\"\n          @click=\"goToDetail(item)\"\n        >\n          <!-- 封面图片 -->\n          <view class=\"item-cover\">\n            <image \n              :src=\"item.cover_image || '/static/images/placeholder.jpg'\" \n              class=\"cover-image\" \n              mode=\"aspectFill\"\n            />\n            <view class=\"status-badge\" :class=\"[getStatusClass(item.status)]\">\n              {{ getStatusText(item.status) }}\n            </view>\n          </view>\n\n          <!-- 投稿信息 -->\n          <view class=\"item-info\">\n            <view class=\"info-header\">\n              <text class=\"submission-title\">{{ item.title || `${item.city}-${item.submission_code}` }}</text>\n              <text class=\"submission-time\">{{ formatTime(item.created_at) }}</text>\n            </view>\n\n            <view class=\"info-content\">\n              <view class=\"info-row\">\n                <view class=\"info-item\">\n                  <uni-icons type=\"location\" size=\"16\" color=\"#f78ca0\"></uni-icons>\n                  <text class=\"info-text\">{{ formatLocation(item) }}</text>\n                </view>\n                <view class=\"info-item\">\n                  <uni-icons type=\"calendar\" size=\"16\" color=\"#a6c1ee\"></uni-icons>\n                  <text class=\"info-text\">{{ item.age }}岁</text>\n                </view>\n              </view>\n\n              <view class=\"info-row\">\n                <view class=\"info-item\">\n                  <uni-icons type=\"person\" size=\"16\" color=\"#feb47b\"></uni-icons>\n                  <text class=\"info-text\">{{ item.occupation }}</text>\n                </view>\n                <view class=\"info-item\">\n                  <uni-icons type=\"eye\" size=\"16\" color=\"#c8a8e9\"></uni-icons>\n                  <text class=\"info-text\">{{ item.views || 0 }}次浏览</text>\n                </view>\n              </view>\n            </view>\n\n            <!-- 操作按钮 -->\n            <view class=\"item-actions\">\n              <view class=\"action-btn edit\" @click.stop=\"editSubmission(item)\">\n                <uni-icons type=\"compose\" size=\"16\" color=\"#f78ca0\"></uni-icons>\n                <text class=\"action-text\">编辑</text>\n              </view>\n              <view class=\"action-btn delete\" @click.stop=\"deleteSubmission(item)\">\n                <uni-icons type=\"trash\" size=\"16\" color=\"#ef4444\"></uni-icons>\n                <text class=\"action-text\">删除</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"filteredSubmissions.length === 0\" class=\"empty-state\">\n        <view class=\"empty-icon\">\n          <uni-icons type=\"compose\" size=\"80\" color=\"#d1d5db\"></uni-icons>\n        </view>\n        <text class=\"empty-text\">{{ getEmptyText() }}</text>\n        <view class=\"empty-action\" @click=\"goToCreate\">\n          <text class=\"action-text\">发布第一个投稿</text>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      currentStatus: 'all',\n      statusTabs: [\n        { label: '全部', value: 'all', count: 0 },\n        { label: '审核中', value: 'pending', count: 0 },\n        { label: '已通过', value: 'approved', count: 0 },\n        { label: '已拒绝', value: 'rejected', count: 0 }\n      ],\n      submissionsList: [\n        // 模拟数据\n        {\n          id: 1,\n          title: '寻找游戏搭子',\n          submission_code: 'zhiyu001',\n          city: '杭州',\n          province: '浙江',\n          age: 22,\n          gender: '女',\n          occupation: '学生',\n          status: 'approved',\n          cover_image: '/static/images/demo1.jpg',\n          views: 156,\n          created_at: '2024-01-15 14:30:00'\n        },\n        {\n          id: 2,\n          title: '一起看电影',\n          submission_code: 'zhiyu002',\n          city: '上海',\n          province: '上海',\n          age: 25,\n          gender: '女',\n          occupation: '设计师',\n          status: 'pending',\n          cover_image: '/static/images/demo2.jpg',\n          views: 0,\n          created_at: '2024-01-16 09:15:00'\n        }\n      ]\n    }\n  },\n  computed: {\n    filteredSubmissions() {\n      if (this.currentStatus === 'all') {\n        return this.submissionsList\n      }\n      return this.submissionsList.filter(item => item.status === this.currentStatus)\n    }\n  },\n  onLoad() {\n    this.checkLoginAndLoad()\n  },\n  methods: {\n    goBack() {\n      uni.navigateBack()\n    },\n\n    checkLoginAndLoad() {\n      if (!this.$store.getters['user/isLoggedIn']) {\n        uni.showModal({\n          title: '登录提示',\n          content: '需要登录后才能查看我的投稿',\n          confirmText: '立即登录',\n          cancelText: '返回',\n          success: (res) => {\n            if (res.confirm) {\n              this.performWxLogin()\n            } else {\n              uni.navigateBack()\n            }\n          }\n        })\n        return\n      }\n      this.loadSubmissions()\n    },\n\n    async performWxLogin() {\n      try {\n        // 检查是否在开发环境\n        // #ifdef H5\n        // 开发环境使用模拟登录\n        await this.mockLogin()\n        return\n        // #endif\n\n        const loginRes = await uni.login({ provider: 'weixin' })\n\n        if (!loginRes.code) {\n          throw new Error('获取微信登录code失败')\n        }\n\n        const userInfoRes = await uni.getUserProfile({\n          desc: '用于完善用户资料'\n        })\n\n        if (!userInfoRes.userInfo) {\n          throw new Error('获取用户信息失败')\n        }\n\n        await this.$store.dispatch('user/wxLogin', {\n          code: loginRes.code,\n          userInfo: userInfoRes.userInfo\n        })\n\n        uni.showToast({\n          title: '登录成功',\n          icon: 'success'\n        })\n\n        this.loadSubmissions()\n\n      } catch (error) {\n        console.error('微信登录失败:', error)\n\n        // 如果微信登录失败，尝试模拟登录\n        try {\n          await this.mockLogin()\n        } catch (mockError) {\n          console.error('模拟登录也失败:', mockError)\n          uni.showToast({\n            title: '登录失败，请重试',\n            icon: 'none'\n          })\n          uni.navigateBack()\n        }\n      }\n    },\n\n    // 模拟登录（开发环境使用）\n    async mockLogin() {\n      try {\n        // 模拟用户信息\n        const mockUserInfo = {\n          id: 1,\n          nickname: '测试用户',\n          avatar: '/static/images/default-avatar.png',\n          gender: 1,\n          desc: '这是一个测试用户'\n        }\n\n        // 模拟token\n        const mockToken = 'mock_token_' + Date.now()\n\n        // 直接设置到store\n        this.$store.commit('user/SET_TOKEN', mockToken)\n        this.$store.commit('user/SET_USER_INFO', mockUserInfo)\n\n        uni.showToast({\n          title: '模拟登录成功',\n          icon: 'success'\n        })\n\n        this.loadSubmissions()\n\n      } catch (error) {\n        throw new Error('模拟登录失败: ' + error.message)\n      }\n    },\n\n    loadSubmissions() {\n      // 更新状态标签的数量\n      this.statusTabs[0].count = this.submissionsList.length\n      this.statusTabs[1].count = this.submissionsList.filter(item => item.status === 'pending').length\n      this.statusTabs[2].count = this.submissionsList.filter(item => item.status === 'approved').length\n      this.statusTabs[3].count = this.submissionsList.filter(item => item.status === 'rejected').length\n    },\n\n    switchStatus(status) {\n      this.currentStatus = status\n    },\n\n    getStatusClass(status) {\n      const statusMap = {\n        'pending': 'pending',\n        'approved': 'approved',\n        'rejected': 'rejected'\n      }\n      return statusMap[status] || 'pending'\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'pending': '审核中',\n        'approved': '已通过',\n        'rejected': '已拒绝'\n      }\n      return statusMap[status] || '未知'\n    },\n\n    getEmptyText() {\n      const textMap = {\n        'all': '还没有投稿，快去发布第一个吧～',\n        'pending': '没有审核中的投稿',\n        'approved': '没有已通过的投稿',\n        'rejected': '没有被拒绝的投稿'\n      }\n      return textMap[this.currentStatus] || '暂无数据'\n    },\n\n    formatLocation(item) {\n      const municipalities = ['北京', '上海', '天津', '重庆']\n      if (item.province && item.city) {\n        if (municipalities.includes(item.province)) {\n          return item.city\n        }\n        return `${item.province}-${item.city}`\n      }\n      return item.city || item.province || '未知'\n    },\n\n    formatTime(timeStr) {\n      const date = new Date(timeStr)\n      const now = new Date()\n      const diff = now - date\n\n      const minutes = Math.floor(diff / (1000 * 60))\n      const hours = Math.floor(diff / (1000 * 60 * 60))\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n\n      if (minutes < 60) {\n        return `${minutes}分钟前`\n      } else if (hours < 24) {\n        return `${hours}小时前`\n      } else if (days < 7) {\n        return `${days}天前`\n      } else {\n        return date.toLocaleDateString()\n      }\n    },\n\n    goToDetail(item) {\n      uni.navigateTo({\n        url: `/pages/submission/detail?id=${item.id}`\n      })\n    },\n\n    goToCreate() {\n      uni.navigateTo({\n        url: '/pages/submission/create'\n      })\n    },\n\n    editSubmission(item) {\n      uni.navigateTo({\n        url: `/pages/submission/edit?id=${item.id}`\n      })\n    },\n\n    deleteSubmission(item) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这个投稿吗？删除后无法恢复。',\n        success: (res) => {\n          if (res.confirm) {\n            // 执行删除操作\n            const index = this.submissionsList.findIndex(sub => sub.id === item.id)\n            if (index > -1) {\n              this.submissionsList.splice(index, 1)\n              this.loadSubmissions()\n              uni.showToast({\n                title: '删除成功',\n                icon: 'success'\n              })\n            }\n          }\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.submissions-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);\n}\n\n.custom-navbar {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n  @include glass-effect(0.6);\n  padding-top: var(--status-bar-height);\n\n  .navbar-content {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 16rpx 24rpx;\n\n    .navbar-left, .navbar-right {\n      width: 60rpx;\n      display: flex;\n      justify-content: center;\n    }\n\n    .navbar-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: $soul-gray-800;\n    }\n\n    .action-btn {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      transition: all 0.3s ease;\n\n      &:active {\n        background: rgba(255, 255, 255, 0.2);\n        transform: scale(0.95);\n      }\n    }\n  }\n}\n\n.filter-tabs {\n  display: flex;\n  padding: 16rpx 20rpx;\n  gap: 8rpx;\n\n  .filter-tab {\n    position: relative;\n    flex: 1;\n    height: 60rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: rgba(255, 255, 255, 0.6);\n    border-radius: 30rpx;\n    transition: all 0.3s ease;\n\n    &.active {\n      background: #f78ca0;\n\n      .tab-text {\n        color: white;\n        font-weight: 600;\n      }\n\n      .tab-badge {\n        background: rgba(255, 255, 255, 0.3);\n        color: white;\n      }\n    }\n\n    .tab-text {\n      font-size: 24rpx;\n      color: $soul-gray-700;\n      transition: all 0.3s ease;\n    }\n\n    .tab-badge {\n      position: absolute;\n      top: -8rpx;\n      right: -8rpx;\n      min-width: 32rpx;\n      height: 32rpx;\n      background: #f78ca0;\n      border-radius: 16rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 18rpx;\n      color: white;\n      font-weight: 600;\n      padding: 0 8rpx;\n    }\n  }\n}\n\n.submissions-content {\n  height: calc(100vh - 200rpx);\n  padding: 0 20rpx 40rpx;\n}\n\n.submissions-list {\n  .submission-item {\n    @include glass-effect(0.6);\n    border-radius: 20rpx;\n    margin-bottom: 16rpx;\n    overflow: hidden;\n    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);\n    transition: all 0.3s ease;\n\n    &:active {\n      transform: scale(0.98);\n    }\n\n    .item-cover {\n      position: relative;\n      height: 200rpx;\n\n      .cover-image {\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n      }\n\n      .status-badge {\n        position: absolute;\n        top: 12rpx;\n        right: 12rpx;\n        padding: 8rpx 16rpx;\n        border-radius: 20rpx;\n        font-size: 20rpx;\n        font-weight: 600;\n        color: white;\n\n        &.pending {\n          background: #f59e0b;\n        }\n\n        &.approved {\n          background: #10b981;\n        }\n\n        &.rejected {\n          background: #ef4444;\n        }\n      }\n    }\n\n    .item-info {\n      padding: 20rpx;\n\n      .info-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 16rpx;\n\n        .submission-title {\n          font-size: 26rpx;\n          font-weight: 600;\n          color: $soul-gray-800;\n          flex: 1;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n\n        .submission-time {\n          font-size: 20rpx;\n          color: $soul-gray-500;\n          margin-left: 16rpx;\n        }\n      }\n\n      .info-content {\n        margin-bottom: 16rpx;\n\n        .info-row {\n          display: flex;\n          gap: 24rpx;\n          margin-bottom: 8rpx;\n\n          &:last-child {\n            margin-bottom: 0;\n          }\n\n          .info-item {\n            display: flex;\n            align-items: center;\n            gap: 6rpx;\n            flex: 1;\n\n            .info-text {\n              font-size: 22rpx;\n              color: $soul-gray-600;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n          }\n        }\n      }\n\n      .item-actions {\n        display: flex;\n        gap: 12rpx;\n        padding-top: 16rpx;\n        border-top: 1rpx solid rgba(0,0,0,0.05);\n\n        .action-btn {\n          flex: 1;\n          height: 60rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 8rpx;\n          border-radius: 12rpx;\n          transition: all 0.3s ease;\n\n          &.edit {\n            background: rgba(247, 140, 160, 0.1);\n            border: 1rpx solid rgba(247, 140, 160, 0.3);\n\n            .action-text {\n              color: #f78ca0;\n            }\n          }\n\n          &.delete {\n            background: rgba(239, 68, 68, 0.1);\n            border: 1rpx solid rgba(239, 68, 68, 0.3);\n\n            .action-text {\n              color: #ef4444;\n            }\n          }\n\n          .action-text {\n            font-size: 22rpx;\n            font-weight: 500;\n          }\n\n          &:active {\n            transform: scale(0.95);\n          }\n        }\n      }\n    }\n  }\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 40rpx;\n  text-align: center;\n\n  .empty-icon {\n    margin-bottom: 24rpx;\n    opacity: 0.5;\n  }\n\n  .empty-text {\n    font-size: 26rpx;\n    color: $soul-gray-500;\n    margin-bottom: 32rpx;\n    line-height: 1.4;\n  }\n\n  .empty-action {\n    padding: 16rpx 32rpx;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 24rpx;\n    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);\n\n    .action-text {\n      font-size: 24rpx;\n      color: white;\n      font-weight: 600;\n    }\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submissions.vue?vue&type=style&index=0&id=608f4c38&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\atool\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./submissions.vue?vue&type=style&index=0&id=608f4c38&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752120110330\n      var cssReload = require(\"D:/atool/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}