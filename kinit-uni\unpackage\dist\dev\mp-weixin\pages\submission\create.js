(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/submission/create"],{

/***/ 221:
/*!***********************************************************************************!*\
  !*** E:/kaifa/投稿/kinit2/kinit-uni/main.js?{"page":"pages%2Fsubmission%2Fcreate"} ***!
  \***********************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _create = _interopRequireDefault(__webpack_require__(/*! ./pages/submission/create.vue */ 222));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_create.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 222:
/*!****************************************************************!*\
  !*** E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue ***!
  \****************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create.vue?vue&type=template&id=3d953676&scoped=true& */ 223);
/* harmony import */ var _create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./create.vue?vue&type=script&lang=js& */ 225);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _create_vue_vue_type_style_index_0_id_3d953676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./create.vue?vue&type=style&index=0&id=3d953676&lang=scss&scoped=true& */ 227);
/* harmony import */ var _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 35);

var renderjs





/* normalize component */

var component = Object(_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "3d953676",
  null,
  false,
  _create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/submission/create.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 223:
/*!***********************************************************************************************************!*\
  !*** E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?vue&type=template&id=3d953676&scoped=true& ***!
  \***********************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./create.vue?vue&type=template&id=3d953676&scoped=true& */ 224);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_template_id_3d953676_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 224:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?vue&type=template&id=3d953676&scoped=true& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniIcons: function () {
      return Promise.all(/*! import() | uni_modules/uni-icons/components/uni-icons/uni-icons */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uni-icons/components/uni-icons/uni-icons.vue */ 277))
    },
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 292))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.formData.self_intro.length
  var g1 = _vm.formData.partner_requirements.length
  var g2 = _vm.imageList.length
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.formData.accept_long_distance = true
    }
    _vm.e1 = function ($event) {
      _vm.formData.accept_long_distance = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 225:
/*!*****************************************************************************************!*\
  !*** E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./create.vue?vue&type=script&lang=js& */ 226);
/* harmony import */ var _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 226:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 30));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 32));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _submission = _interopRequireDefault(__webpack_require__(/*! @/common/api/submission.js */ 209));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      formData: {
        province: '',
        city: '',
        age: '',
        gender: '女',
        // 默认选择女
        height: '',
        occupation: '',
        self_intro: '',
        partner_requirements: '',
        accept_long_distance: null,
        wechat_id: '',
        category: '交友'
      },
      imageList: [],
      wechatQRCode: '',
      // 微信二维码
      submitting: false,
      genderOptions: [{
        label: '女',
        value: '女',
        icon: 'person-filled'
      }, {
        label: '男',
        value: '男',
        icon: 'person'
      }],
      // 城市数据
      provinces: [{
        name: '北京',
        cities: [{
          name: '北京'
        }]
      }, {
        name: '上海',
        cities: [{
          name: '上海'
        }]
      }, {
        name: '天津',
        cities: [{
          name: '天津'
        }]
      }, {
        name: '重庆',
        cities: [{
          name: '重庆'
        }]
      }, {
        name: '河北',
        cities: [{
          name: '石家庄'
        }, {
          name: '唐山'
        }, {
          name: '秦皇岛'
        }, {
          name: '邯郸'
        }, {
          name: '邢台'
        }, {
          name: '保定'
        }, {
          name: '张家口'
        }, {
          name: '承德'
        }, {
          name: '沧州'
        }, {
          name: '廊坊'
        }, {
          name: '衡水'
        }]
      }, {
        name: '山西',
        cities: [{
          name: '太原'
        }, {
          name: '大同'
        }, {
          name: '阳泉'
        }, {
          name: '长治'
        }, {
          name: '晋城'
        }, {
          name: '朔州'
        }, {
          name: '晋中'
        }, {
          name: '运城'
        }, {
          name: '忻州'
        }, {
          name: '临汾'
        }, {
          name: '吕梁'
        }]
      }, {
        name: '内蒙古',
        cities: [{
          name: '呼和浩特'
        }, {
          name: '包头'
        }, {
          name: '乌海'
        }, {
          name: '赤峰'
        }, {
          name: '通辽'
        }, {
          name: '鄂尔多斯'
        }, {
          name: '呼伦贝尔'
        }, {
          name: '巴彦淖尔'
        }, {
          name: '乌兰察布'
        }, {
          name: '兴安盟'
        }, {
          name: '锡林郭勒盟'
        }, {
          name: '阿拉善盟'
        }]
      }, {
        name: '辽宁',
        cities: [{
          name: '沈阳'
        }, {
          name: '大连'
        }, {
          name: '鞍山'
        }, {
          name: '抚顺'
        }, {
          name: '本溪'
        }, {
          name: '丹东'
        }, {
          name: '锦州'
        }, {
          name: '营口'
        }, {
          name: '阜新'
        }, {
          name: '辽阳'
        }, {
          name: '盘锦'
        }, {
          name: '铁岭'
        }, {
          name: '朝阳'
        }, {
          name: '葫芦岛'
        }]
      }, {
        name: '吉林',
        cities: [{
          name: '长春'
        }, {
          name: '吉林'
        }, {
          name: '四平'
        }, {
          name: '辽源'
        }, {
          name: '通化'
        }, {
          name: '白山'
        }, {
          name: '松原'
        }, {
          name: '白城'
        }, {
          name: '延边朝鲜族自治州'
        }]
      }, {
        name: '黑龙江',
        cities: [{
          name: '哈尔滨'
        }, {
          name: '齐齐哈尔'
        }, {
          name: '鸡西'
        }, {
          name: '鹤岗'
        }, {
          name: '双鸭山'
        }, {
          name: '大庆'
        }, {
          name: '伊春'
        }, {
          name: '佳木斯'
        }, {
          name: '七台河'
        }, {
          name: '牡丹江'
        }, {
          name: '黑河'
        }, {
          name: '绥化'
        }, {
          name: '大兴安岭地区'
        }]
      }, {
        name: '江苏',
        cities: [{
          name: '南京'
        }, {
          name: '无锡'
        }, {
          name: '徐州'
        }, {
          name: '常州'
        }, {
          name: '苏州'
        }, {
          name: '南通'
        }, {
          name: '连云港'
        }, {
          name: '淮安'
        }, {
          name: '盐城'
        }, {
          name: '扬州'
        }, {
          name: '镇江'
        }, {
          name: '泰州'
        }, {
          name: '宿迁'
        }]
      }, {
        name: '浙江',
        cities: [{
          name: '杭州'
        }, {
          name: '宁波'
        }, {
          name: '温州'
        }, {
          name: '嘉兴'
        }, {
          name: '湖州'
        }, {
          name: '绍兴'
        }, {
          name: '金华'
        }, {
          name: '衢州'
        }, {
          name: '舟山'
        }, {
          name: '台州'
        }, {
          name: '丽水'
        }]
      }, {
        name: '安徽',
        cities: [{
          name: '合肥'
        }, {
          name: '芜湖'
        }, {
          name: '蚌埠'
        }, {
          name: '淮南'
        }, {
          name: '马鞍山'
        }, {
          name: '淮北'
        }, {
          name: '铜陵'
        }, {
          name: '安庆'
        }, {
          name: '黄山'
        }, {
          name: '滁州'
        }, {
          name: '阜阳'
        }, {
          name: '宿州'
        }, {
          name: '六安'
        }, {
          name: '亳州'
        }, {
          name: '池州'
        }, {
          name: '宣城'
        }]
      }, {
        name: '福建',
        cities: [{
          name: '福州'
        }, {
          name: '厦门'
        }, {
          name: '莆田'
        }, {
          name: '三明'
        }, {
          name: '泉州'
        }, {
          name: '漳州'
        }, {
          name: '南平'
        }, {
          name: '龙岩'
        }, {
          name: '宁德'
        }]
      }, {
        name: '江西',
        cities: [{
          name: '南昌'
        }, {
          name: '景德镇'
        }, {
          name: '萍乡'
        }, {
          name: '九江'
        }, {
          name: '新余'
        }, {
          name: '鹰潭'
        }, {
          name: '赣州'
        }, {
          name: '吉安'
        }, {
          name: '宜春'
        }, {
          name: '抚州'
        }, {
          name: '上饶'
        }]
      }, {
        name: '山东',
        cities: [{
          name: '济南'
        }, {
          name: '青岛'
        }, {
          name: '淄博'
        }, {
          name: '枣庄'
        }, {
          name: '东营'
        }, {
          name: '烟台'
        }, {
          name: '潍坊'
        }, {
          name: '济宁'
        }, {
          name: '泰安'
        }, {
          name: '威海'
        }, {
          name: '日照'
        }, {
          name: '临沂'
        }, {
          name: '德州'
        }, {
          name: '聊城'
        }, {
          name: '滨州'
        }, {
          name: '菏泽'
        }]
      }, {
        name: '河南',
        cities: [{
          name: '郑州'
        }, {
          name: '开封'
        }, {
          name: '洛阳'
        }, {
          name: '平顶山'
        }, {
          name: '安阳'
        }, {
          name: '鹤壁'
        }, {
          name: '新乡'
        }, {
          name: '焦作'
        }, {
          name: '濮阳'
        }, {
          name: '许昌'
        }, {
          name: '漯河'
        }, {
          name: '三门峡'
        }, {
          name: '南阳'
        }, {
          name: '商丘'
        }, {
          name: '信阳'
        }, {
          name: '周口'
        }, {
          name: '驻马店'
        }, {
          name: '济源'
        }]
      }, {
        name: '湖北',
        cities: [{
          name: '武汉'
        }, {
          name: '黄石'
        }, {
          name: '十堰'
        }, {
          name: '宜昌'
        }, {
          name: '襄阳'
        }, {
          name: '鄂州'
        }, {
          name: '荆门'
        }, {
          name: '孝感'
        }, {
          name: '荆州'
        }, {
          name: '黄冈'
        }, {
          name: '咸宁'
        }, {
          name: '随州'
        }, {
          name: '恩施土家族苗族自治州'
        }, {
          name: '仙桃'
        }, {
          name: '潜江'
        }, {
          name: '天门'
        }, {
          name: '神农架林区'
        }]
      }, {
        name: '湖南',
        cities: [{
          name: '长沙'
        }, {
          name: '株洲'
        }, {
          name: '湘潭'
        }, {
          name: '衡阳'
        }, {
          name: '邵阳'
        }, {
          name: '岳阳'
        }, {
          name: '常德'
        }, {
          name: '张家界'
        }, {
          name: '益阳'
        }, {
          name: '郴州'
        }, {
          name: '永州'
        }, {
          name: '怀化'
        }, {
          name: '娄底'
        }, {
          name: '湘西土家族苗族自治州'
        }]
      }, {
        name: '广东',
        cities: [{
          name: '广州'
        }, {
          name: '韶关'
        }, {
          name: '深圳'
        }, {
          name: '珠海'
        }, {
          name: '汕头'
        }, {
          name: '佛山'
        }, {
          name: '江门'
        }, {
          name: '湛江'
        }, {
          name: '茂名'
        }, {
          name: '肇庆'
        }, {
          name: '惠州'
        }, {
          name: '梅州'
        }, {
          name: '汕尾'
        }, {
          name: '河源'
        }, {
          name: '阳江'
        }, {
          name: '清远'
        }, {
          name: '东莞'
        }, {
          name: '中山'
        }, {
          name: '潮州'
        }, {
          name: '揭阳'
        }, {
          name: '云浮'
        }]
      }, {
        name: '广西',
        cities: [{
          name: '南宁'
        }, {
          name: '柳州'
        }, {
          name: '桂林'
        }, {
          name: '梧州'
        }, {
          name: '北海'
        }, {
          name: '防城港'
        }, {
          name: '钦州'
        }, {
          name: '贵港'
        }, {
          name: '玉林'
        }, {
          name: '百色'
        }, {
          name: '贺州'
        }, {
          name: '河池'
        }, {
          name: '来宾'
        }, {
          name: '崇左'
        }]
      }, {
        name: '海南',
        cities: [{
          name: '海口'
        }, {
          name: '三亚'
        }, {
          name: '三沙'
        }, {
          name: '儋州'
        }, {
          name: '五指山'
        }, {
          name: '琼海'
        }, {
          name: '文昌'
        }, {
          name: '万宁'
        }, {
          name: '东方'
        }, {
          name: '定安'
        }, {
          name: '屯昌'
        }, {
          name: '澄迈'
        }, {
          name: '临高'
        }, {
          name: '白沙黎族自治县'
        }, {
          name: '昌江黎族自治县'
        }, {
          name: '乐东黎族自治县'
        }, {
          name: '陵水黎族自治县'
        }, {
          name: '保亭黎族苗族自治县'
        }, {
          name: '琼中黎族苗族自治县'
        }]
      }, {
        name: '四川',
        cities: [{
          name: '成都'
        }, {
          name: '自贡'
        }, {
          name: '攀枝花'
        }, {
          name: '泸州'
        }, {
          name: '德阳'
        }, {
          name: '绵阳'
        }, {
          name: '广元'
        }, {
          name: '遂宁'
        }, {
          name: '内江'
        }, {
          name: '乐山'
        }, {
          name: '南充'
        }, {
          name: '眉山'
        }, {
          name: '宜宾'
        }, {
          name: '广安'
        }, {
          name: '达州'
        }, {
          name: '雅安'
        }, {
          name: '巴中'
        }, {
          name: '资阳'
        }, {
          name: '阿坝藏族羌族自治州'
        }, {
          name: '甘孜藏族自治州'
        }, {
          name: '凉山彝族自治州'
        }]
      }, {
        name: '贵州',
        cities: [{
          name: '贵阳'
        }, {
          name: '六盘水'
        }, {
          name: '遵义'
        }, {
          name: '安顺'
        }, {
          name: '毕节'
        }, {
          name: '铜仁'
        }, {
          name: '黔西南布依族苗族自治州'
        }, {
          name: '黔东南苗族侗族自治州'
        }, {
          name: '黔南布依族苗族自治州'
        }]
      }, {
        name: '云南',
        cities: [{
          name: '昆明'
        }, {
          name: '曲靖'
        }, {
          name: '玉溪'
        }, {
          name: '保山'
        }, {
          name: '昭通'
        }, {
          name: '丽江'
        }, {
          name: '普洱'
        }, {
          name: '临沧'
        }, {
          name: '楚雄彝族自治州'
        }, {
          name: '红河哈尼族彝族自治州'
        }, {
          name: '文山壮族苗族自治州'
        }, {
          name: '西双版纳傣族自治州'
        }, {
          name: '大理白族自治州'
        }, {
          name: '德宏傣族景颇族自治州'
        }, {
          name: '怒江傈僳族自治州'
        }, {
          name: '迪庆藏族自治州'
        }]
      }, {
        name: '西藏',
        cities: [{
          name: '拉萨'
        }, {
          name: '日喀则'
        }, {
          name: '昌都'
        }, {
          name: '林芝'
        }, {
          name: '山南'
        }, {
          name: '那曲'
        }, {
          name: '阿里地区'
        }]
      }, {
        name: '陕西',
        cities: [{
          name: '西安'
        }, {
          name: '铜川'
        }, {
          name: '宝鸡'
        }, {
          name: '咸阳'
        }, {
          name: '渭南'
        }, {
          name: '延安'
        }, {
          name: '汉中'
        }, {
          name: '榆林'
        }, {
          name: '安康'
        }, {
          name: '商洛'
        }]
      }, {
        name: '甘肃',
        cities: [{
          name: '兰州'
        }, {
          name: '嘉峪关'
        }, {
          name: '金昌'
        }, {
          name: '白银'
        }, {
          name: '天水'
        }, {
          name: '武威'
        }, {
          name: '张掖'
        }, {
          name: '平凉'
        }, {
          name: '酒泉'
        }, {
          name: '庆阳'
        }, {
          name: '定西'
        }, {
          name: '陇南'
        }, {
          name: '临夏回族自治州'
        }, {
          name: '甘南藏族自治州'
        }]
      }, {
        name: '青海',
        cities: [{
          name: '西宁'
        }, {
          name: '海东'
        }, {
          name: '海北藏族自治州'
        }, {
          name: '黄南藏族自治州'
        }, {
          name: '海南藏族自治州'
        }, {
          name: '果洛藏族自治州'
        }, {
          name: '玉树藏族自治州'
        }, {
          name: '海西蒙古族藏族自治州'
        }]
      }, {
        name: '宁夏',
        cities: [{
          name: '银川'
        }, {
          name: '石嘴山'
        }, {
          name: '吴忠'
        }, {
          name: '固原'
        }, {
          name: '中卫'
        }]
      }, {
        name: '新疆',
        cities: [{
          name: '乌鲁木齐'
        }, {
          name: '克拉玛依'
        }, {
          name: '吐鲁番'
        }, {
          name: '哈密'
        }, {
          name: '昌吉回族自治州'
        }, {
          name: '博尔塔拉蒙古自治州'
        }, {
          name: '巴音郭楞蒙古自治州'
        }, {
          name: '阿克苏地区'
        }, {
          name: '克孜勒苏柯尔克孜自治州'
        }, {
          name: '喀什地区'
        }, {
          name: '和田地区'
        }, {
          name: '伊犁哈萨克自治州'
        }, {
          name: '塔城地区'
        }, {
          name: '阿勒泰地区'
        }, {
          name: '石河子'
        }, {
          name: '阿拉尔'
        }, {
          name: '图木舒克'
        }, {
          name: '五家渠'
        }, {
          name: '北屯'
        }, {
          name: '铁门关'
        }, {
          name: '双河'
        }, {
          name: '可克达拉'
        }, {
          name: '昆玉'
        }, {
          name: '胡杨河'
        }]
      }, {
        name: '中国香港',
        cities: [{
          name: '中西区'
        }, {
          name: '湾仔区'
        }, {
          name: '东区'
        }, {
          name: '南区'
        }, {
          name: '油尖旺区'
        }, {
          name: '深水埗区'
        }, {
          name: '九龙城区'
        }, {
          name: '黄大仙区'
        }, {
          name: '观塘区'
        }, {
          name: '荃湾区'
        }, {
          name: '屯门区'
        }, {
          name: '元朗区'
        }, {
          name: '北区'
        }, {
          name: '大埔区'
        }, {
          name: '沙田区'
        }, {
          name: '西贡区'
        }, {
          name: '葵青区'
        }, {
          name: '离岛区'
        }]
      }, {
        name: '中国澳门',
        cities: [{
          name: '澳门半岛'
        }, {
          name: '氹仔'
        }, {
          name: '路环'
        }]
      }, {
        name: '中国台湾',
        cities: [{
          name: '台北市'
        }, {
          name: '新北市'
        }, {
          name: '桃园市'
        }, {
          name: '台中市'
        }, {
          name: '台南市'
        }, {
          name: '高雄市'
        }, {
          name: '基隆市'
        }, {
          name: '新竹市'
        }, {
          name: '嘉义市'
        }, {
          name: '新竹县'
        }, {
          name: '苗栗县'
        }, {
          name: '彰化县'
        }, {
          name: '南投县'
        }, {
          name: '云林县'
        }, {
          name: '嘉义县'
        }, {
          name: '屏东县'
        }, {
          name: '宜兰县'
        }, {
          name: '花莲县'
        }, {
          name: '台东县'
        }, {
          name: '澎湖县'
        }, {
          name: '金门县'
        }, {
          name: '连江县'
        }]
      }],
      pickerValue: [0, 0],
      selectedProvinceIndex: 0,
      selectedCityIndex: 0
    };
  },
  computed: {
    selectedCity: function selectedCity() {
      if (this.formData.province && this.formData.city) {
        return "".concat(this.formData.province, "-").concat(this.formData.city);
      }
      return '';
    },
    currentCities: function currentCities() {
      var _this$provinces$this$;
      return ((_this$provinces$this$ = this.provinces[this.selectedProvinceIndex]) === null || _this$provinces$this$ === void 0 ? void 0 : _this$provinces$this$.cities) || [];
    }
  },
  methods: {
    goBack: function goBack() {
      uni.navigateBack();
    },
    selectGender: function selectGender(value) {
      this.formData.gender = value;
    },
    showCityPicker: function showCityPicker() {
      this.$refs.cityPopup.open();
    },
    hideCityPicker: function hideCityPicker() {
      this.$refs.cityPopup.close();
    },
    onPickerChange: function onPickerChange(e) {
      this.pickerValue = e.detail.value;
      this.selectedProvinceIndex = e.detail.value[0];
      this.selectedCityIndex = e.detail.value[1];
    },
    confirmCity: function confirmCity() {
      var province = this.provinces[this.selectedProvinceIndex];
      var city = this.currentCities[this.selectedCityIndex];
      if (!province || !city) {
        uni.showToast({
          title: '请选择有效的城市',
          icon: 'none'
        });
        return;
      }
      this.formData.province = province.name;
      this.formData.city = city.name;
      this.hideCityPicker();
    },
    chooseImage: function chooseImage() {
      var _this = this;
      var remainingCount = 8 - this.imageList.length;
      uni.chooseImage({
        count: remainingCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: function success(res) {
          var _this$imageList;
          (_this$imageList = _this.imageList).push.apply(_this$imageList, (0, _toConsumableArray2.default)(res.tempFilePaths));
        }
      });
    },
    removeImage: function removeImage(index) {
      this.imageList.splice(index, 1);
    },
    previewImage: function previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: index
      });
    },
    chooseQRCode: function chooseQRCode() {
      var _this2 = this;
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: function success(res) {
          _this2.wechatQRCode = res.tempFilePaths[0];
        }
      });
    },
    removeQRCode: function removeQRCode() {
      this.wechatQRCode = '';
    },
    previewQRCode: function previewQRCode() {
      uni.previewImage({
        urls: [this.wechatQRCode],
        current: 0
      });
    },
    validateForm: function validateForm() {
      if (!this.formData.province || !this.formData.city) {
        uni.showToast({
          title: '请选择所在城市',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.age || this.formData.age < 16 || this.formData.age > 60) {
        uni.showToast({
          title: '请输入正确的年龄（16-60岁）',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.height || this.formData.height < 140 || this.formData.height > 220) {
        uni.showToast({
          title: '请输入正确的身高（140-220cm）',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.occupation.trim()) {
        uni.showToast({
          title: '请填写职业/工作状态',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.self_intro.trim()) {
        uni.showToast({
          title: '请填写自我介绍',
          icon: 'none'
        });
        return false;
      }
      if (!this.formData.partner_requirements.trim()) {
        uni.showToast({
          title: '请填写搭子要求',
          icon: 'none'
        });
        return false;
      }
      if (this.formData.accept_long_distance === null || this.formData.accept_long_distance === undefined) {
        uni.showToast({
          title: '请选择是否接受异地',
          icon: 'none'
        });
        return false;
      }
      if (this.imageList.length === 0) {
        uni.showToast({
          title: '请至少上传一张照片',
          icon: 'none'
        });
        return false;
      }
      return true;
    },
    submitForm: function submitForm() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var imageUrls, wechatQRCodeUrl, submitData, res;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!_this3.submitting) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                if (_this3.validateForm()) {
                  _context.next = 4;
                  break;
                }
                return _context.abrupt("return");
              case 4:
                _this3.submitting = true;
                _context.prev = 5;
                _context.next = 8;
                return _this3.uploadImages();
              case 8:
                imageUrls = _context.sent;
                // 上传微信二维码
                wechatQRCodeUrl = '';
                if (!_this3.wechatQRCode) {
                  _context.next = 14;
                  break;
                }
                _context.next = 13;
                return _this3.uploadSingleImage(_this3.wechatQRCode);
              case 13:
                wechatQRCodeUrl = _context.sent;
              case 14:
                // 准备提交数据
                submitData = _objectSpread(_objectSpread({}, _this3.formData), {}, {
                  cover_image: imageUrls[0],
                  // 第一张作为主图
                  images: imageUrls.slice(1).join(','),
                  // 其余图片
                  wechat_qrcode: wechatQRCodeUrl,
                  // 微信二维码
                  age: parseInt(_this3.formData.age),
                  height: parseInt(_this3.formData.height),
                  status: 'pending',
                  // 用户投稿默认为待审核状态
                  is_visible: false // 用户投稿默认不可见，需要管理员审核
                }); // 提交表单
                _context.next = 17;
                return _submission.default.createSubmission(submitData);
              case 17:
                res = _context.sent;
                if (!(res.code === 200)) {
                  _context.next = 23;
                  break;
                }
                uni.showToast({
                  title: '投稿提交成功，等待审核',
                  icon: 'success'
                });
                setTimeout(function () {
                  uni.navigateBack();
                }, 1500);
                _context.next = 24;
                break;
              case 23:
                throw new Error(res.message || '提交失败');
              case 24:
                _context.next = 30;
                break;
              case 26:
                _context.prev = 26;
                _context.t0 = _context["catch"](5);
                console.error('提交投稿失败:', _context.t0);
                uni.showToast({
                  title: _context.t0.message || '提交失败，请重试',
                  icon: 'none'
                });
              case 30:
                _context.prev = 30;
                _this3.submitting = false;
                return _context.finish(30);
              case 33:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[5, 26, 30, 33]]);
      }))();
    },
    uploadImages: function uploadImages() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var uploadPromises;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                uploadPromises = _this4.imageList.map(function (imagePath) {
                  return new Promise(function (resolve, reject) {
                    uni.uploadFile({
                      url: "".concat(__webpack_require__(/*! @/config.js */ 41).baseUrl, "/upload/image"),
                      filePath: imagePath,
                      name: 'file',
                      header: {
                        'Authorization': "Bearer ".concat(uni.getStorageSync('token'))
                      },
                      success: function success(res) {
                        try {
                          var data = JSON.parse(res.data);
                          if (data.code === 200) {
                            resolve(data.data.url);
                          } else {
                            reject(new Error(data.message || '图片上传失败'));
                          }
                        } catch (e) {
                          reject(new Error('图片上传响应解析失败'));
                        }
                      },
                      fail: function fail(error) {
                        reject(error);
                      }
                    });
                  });
                });
                return _context2.abrupt("return", Promise.all(uploadPromises));
              case 2:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    uploadSingleImage: function uploadSingleImage(imagePath) {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                return _context3.abrupt("return", new Promise(function (resolve, reject) {
                  uni.uploadFile({
                    url: "".concat(__webpack_require__(/*! @/config.js */ 41).baseUrl, "/upload/image"),
                    filePath: imagePath,
                    name: 'file',
                    header: {
                      'Authorization': "Bearer ".concat(uni.getStorageSync('token'))
                    },
                    success: function success(res) {
                      try {
                        var data = JSON.parse(res.data);
                        if (data.code === 200) {
                          resolve(data.data.url);
                        } else {
                          reject(new Error(data.message || '图片上传失败'));
                        }
                      } catch (e) {
                        reject(new Error('图片上传响应解析失败'));
                      }
                    },
                    fail: function fail(error) {
                      reject(error);
                    }
                  });
                }));
              case 1:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 227:
/*!**************************************************************************************************************************!*\
  !*** E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?vue&type=style&index=0&id=3d953676&lang=scss&scoped=true& ***!
  \**************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_style_index_0_id_3d953676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./create.vue?vue&type=style&index=0&id=3d953676&lang=scss&scoped=true& */ 228);
/* harmony import */ var _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_style_index_0_id_3d953676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_style_index_0_id_3d953676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_style_index_0_id_3d953676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_atool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_style_index_0_id_3d953676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_atool_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_create_vue_vue_type_style_index_0_id_3d953676_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 228:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/kaifa/投稿/kinit2/kinit-uni/pages/submission/create.vue?vue&type=style&index=0&id=3d953676&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[221,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/submission/create.js.map