<view class="favorites-page data-v-c823fa58"><view class="custom-navbar data-v-c823fa58"><view class="navbar-content data-v-c823fa58"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left data-v-c823fa58" bindtap="__e"><uni-icons vue-id="3c5a4e9c-1" type="left" size="32" color="#FF6B9D" class="data-v-c823fa58" bind:__l="__l"></uni-icons></view><text class="navbar-title data-v-c823fa58">我的收藏</text><view class="navbar-right data-v-c823fa58"><view data-event-opts="{{[['tap',[['toggleEditMode',['$event']]]]]}}" class="action-btn data-v-c823fa58" bindtap="__e"><uni-icons vue-id="3c5a4e9c-2" type="{{editMode?'checkmarkempty':'compose'}}" size="24" color="#f78ca0" class="data-v-c823fa58" bind:__l="__l"></uni-icons></view></view></view></view><scroll-view class="favorites-content data-v-c823fa58" scroll-y="true"><view class="stats-card data-v-c823fa58"><view class="stats-content data-v-c823fa58"><view class="stats-item data-v-c823fa58"><text class="stats-number data-v-c823fa58">{{$root.g0}}</text><text class="stats-label data-v-c823fa58">收藏总数</text></view><view class="stats-item data-v-c823fa58"><text class="stats-number data-v-c823fa58">{{$root.g1}}</text><text class="stats-label data-v-c823fa58">已选择</text></view></view><block wx:if="{{$root.g2}}"><view class="batch-actions data-v-c823fa58"><view data-event-opts="{{[['tap',[['batchDelete',['$event']]]]]}}" class="batch-btn delete data-v-c823fa58" bindtap="__e"><uni-icons vue-id="3c5a4e9c-3" type="trash" size="16" color="#ef4444" class="data-v-c823fa58" bind:__l="__l"></uni-icons><text class="batch-text data-v-c823fa58">删除选中</text></view></view></block></view><view class="favorites-list data-v-c823fa58"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleItemClick',['$0'],[[['favoritesList','',index]]]]]]]}}" class="{{['favorite-item','data-v-c823fa58',(item.g3)?'selected':'']}}" bindtap="__e"><block wx:if="{{editMode}}"><view data-event-opts="{{[['tap',[['toggleSelect',['$0'],[[['favoritesList','',index,'id']]]]]]]}}" class="select-checkbox data-v-c823fa58" catchtap="__e"><uni-icons vue-id="{{'3c5a4e9c-4-'+index}}" type="{{item.g4?'checkmarkempty':'circle'}}" size="20" color="{{item.g5?'#f78ca0':'#d1d5db'}}" class="data-v-c823fa58" bind:__l="__l"></uni-icons></view></block><soul-submission-card vue-id="{{'3c5a4e9c-5-'+index}}" submission="{{item.$orig}}" show-favorite="{{false}}" data-event-opts="{{[['^click',[['goToDetail',['$0'],[[['favoritesList','',index]]]]]]]}}" bind:click="__e" class="data-v-c823fa58" bind:__l="__l"></soul-submission-card><view class="favorite-time data-v-c823fa58"><uni-icons vue-id="{{'3c5a4e9c-6-'+index}}" type="calendar" size="14" color="#999" class="data-v-c823fa58" bind:__l="__l"></uni-icons><text class="time-text data-v-c823fa58">{{"收藏于 "+item.m0}}</text></view></view></block></view><block wx:if="{{$root.g6}}"><view class="empty-state data-v-c823fa58"><view class="empty-icon data-v-c823fa58"><uni-icons vue-id="3c5a4e9c-7" type="heart" size="80" color="#d1d5db" class="data-v-c823fa58" bind:__l="__l"></uni-icons></view><text class="empty-text data-v-c823fa58">还没有收藏任何投稿</text><text class="empty-desc data-v-c823fa58">去首页看看有没有喜欢的投稿吧～</text><view data-event-opts="{{[['tap',[['goToHome',['$event']]]]]}}" class="empty-action data-v-c823fa58" bindtap="__e"><text class="action-text data-v-c823fa58">去首页看看</text></view></view></block><block wx:if="{{loading}}"><view class="loading-state data-v-c823fa58"><uni-icons vue-id="3c5a4e9c-8" type="spinner-cycle" size="32" color="#f78ca0" class="data-v-c823fa58" bind:__l="__l"></uni-icons><text class="loading-text data-v-c823fa58">加载中...</text></view></block></scroll-view></view>