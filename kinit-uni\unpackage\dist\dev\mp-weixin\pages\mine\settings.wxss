@charset "UTF-8";
/* uView的全局SCSS主题文件 */
/**
 * Soul风格主题变量
 */
/* Soul风格颜色变量 */
/* 主色调 - 温柔的粉色系 */
/* 辅助色 */
/* 中性色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更圆润的设计 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* Soul风格特有样式 */
/* 毛玻璃效果 */
/* 渐变背景 */
/* 阴影效果 */
/* 卡片样式 */
/* 按钮样式 */
.settings-page.data-v-68887b36 {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}
.custom-navbar.data-v-68887b36 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  padding-top: 25px;
}
.custom-navbar .navbar-content.data-v-68887b36 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
}
.custom-navbar .navbar-content .navbar-left.data-v-68887b36, .custom-navbar .navbar-content .navbar-right.data-v-68887b36 {
  width: 60rpx;
  display: flex;
  justify-content: center;
}
.custom-navbar .navbar-content .navbar-title.data-v-68887b36 {
  font-size: 28rpx;
  font-weight: 600;
  color: #424242;
}
.settings-content.data-v-68887b36 {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
  padding-bottom: 40rpx;
}
.settings-section.data-v-68887b36 {
  margin-bottom: 24rpx;
}
.settings-section .section-title.data-v-68887b36 {
  font-size: 24rpx;
  font-weight: 600;
  color: #757575;
  margin-bottom: 12rpx;
  padding-left: 8rpx;
}
.settings-section .settings-card.data-v-68887b36 {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.settings-section .settings-card .setting-item.data-v-68887b36 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.settings-section .settings-card .setting-item.data-v-68887b36:last-child {
  border-bottom: none;
}
.settings-section .settings-card .setting-item.data-v-68887b36:active:not(.logout) {
  background: rgba(247, 140, 160, 0.05);
}
.settings-section .settings-card .setting-item.logout.data-v-68887b36 {
  justify-content: center;
}
.settings-section .settings-card .setting-item.logout.data-v-68887b36:active {
  background: rgba(239, 68, 68, 0.05);
}
.settings-section .settings-card .setting-item.logout .setting-content .setting-title.data-v-68887b36 {
  color: #ef4444;
  font-weight: 600;
}
.settings-section .settings-card .setting-item .setting-icon.data-v-68887b36 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  margin-right: 16rpx;
}
.settings-section .settings-card .setting-item .setting-content.data-v-68887b36 {
  flex: 1;
}
.settings-section .settings-card .setting-item .setting-content .setting-title.data-v-68887b36 {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #424242;
  margin-bottom: 4rpx;
}
.settings-section .settings-card .setting-item .setting-content .setting-desc.data-v-68887b36 {
  font-size: 22rpx;
  color: #9E9E9E;
}
.settings-section .settings-card .setting-item .setting-arrow.data-v-68887b36 {
  opacity: 0.6;
}
.settings-section .settings-card .setting-item switch.data-v-68887b36 {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}
