<script>
export default {
  onLaunch: function () {
    this.initApp()
  },
  methods: {
    // 初始化应用
    async initApp() {
      // 初始化应用配置
      this.$store.dispatch('app/InitConfig')
      // 初始化系统配置
      await this.$store.dispatch('system/getSystemConfig')
      // 初始化用户状态
      this.$store.dispatch('user/initUserState')
    }
  }
}
</script>

<style lang="scss">
@import '@/static/scss/index.scss';
</style>
