@charset "UTF-8";
/* uView的全局SCSS主题文件 */
/**
 * Soul风格主题变量
 */
/* Soul风格颜色变量 */
/* 主色调 - 温柔的粉色系 */
/* 辅助色 */
/* 中性色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius - 更圆润的设计 */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* Soul风格特有样式 */
/* 毛玻璃效果 */
/* 渐变背景 */
/* 阴影效果 */
/* 卡片样式 */
/* 按钮样式 */
.mine-page.data-v-4bd6864f {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}
.custom-navbar.data-v-4bd6864f {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  padding-top: 25px;
}
.custom-navbar .navbar-content.data-v-4bd6864f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
}
.custom-navbar .navbar-content .navbar-left.data-v-4bd6864f, .custom-navbar .navbar-content .navbar-right.data-v-4bd6864f {
  width: 60rpx;
  display: flex;
  justify-content: center;
}
.custom-navbar .navbar-content .navbar-title.data-v-4bd6864f {
  font-size: 28rpx;
  font-weight: 600;
  color: #424242;
}
.custom-navbar .navbar-content .action-btn.data-v-4bd6864f {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}
.custom-navbar .navbar-content .action-btn.data-v-4bd6864f:active {
  background: rgba(255, 255, 255, 0.2);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.mine-content.data-v-4bd6864f {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
  padding-bottom: 140rpx;
}
.user-card.data-v-4bd6864f {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.user-card .user-avatar.data-v-4bd6864f {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
}
.user-card .user-avatar .avatar-image.data-v-4bd6864f {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(247, 140, 160, 0.2);
}
.user-card .user-avatar .avatar-edit.data-v-4bd6864f {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  background: #f78ca0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid white;
}
.user-card .user-info.data-v-4bd6864f {
  margin-bottom: 32rpx;
}
.user-card .user-info .user-name.data-v-4bd6864f {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #424242;
  margin-bottom: 8rpx;
}
.user-card .user-info .user-desc.data-v-4bd6864f {
  font-size: 24rpx;
  color: #9E9E9E;
  line-height: 1.4;
}
.user-card .user-stats.data-v-4bd6864f {
  display: flex;
  justify-content: space-around;
}
.user-card .user-stats .stat-item.data-v-4bd6864f {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.user-card .user-stats .stat-item .stat-number.data-v-4bd6864f {
  font-size: 36rpx;
  font-weight: 700;
  color: #f78ca0;
}
.user-card .user-stats .stat-item .stat-label.data-v-4bd6864f {
  font-size: 22rpx;
  color: #757575;
}
.menu-section .menu-card.data-v-4bd6864f {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}
.menu-section .menu-card .menu-item.data-v-4bd6864f {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.menu-section .menu-card .menu-item.data-v-4bd6864f:last-child {
  border-bottom: none;
}
.menu-section .menu-card .menu-item.data-v-4bd6864f:active {
  background: rgba(247, 140, 160, 0.05);
}
.menu-section .menu-card .menu-item .menu-icon.data-v-4bd6864f {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  margin-right: 16rpx;
}
.menu-section .menu-card .menu-item .menu-content.data-v-4bd6864f {
  flex: 1;
}
.menu-section .menu-card .menu-item .menu-content .menu-title.data-v-4bd6864f {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #424242;
  margin-bottom: 4rpx;
}
.menu-section .menu-card .menu-item .menu-content .menu-desc.data-v-4bd6864f {
  font-size: 22rpx;
  color: #9E9E9E;
}
.menu-section .menu-card .menu-item .menu-badge.data-v-4bd6864f {
  background: #f78ca0;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  margin-right: 12rpx;
}
.menu-section .menu-card .menu-item .menu-badge .badge-text.data-v-4bd6864f {
  font-size: 18rpx;
  color: white;
  font-weight: 600;
}
.menu-section .menu-card .menu-item .menu-arrow.data-v-4bd6864f {
  opacity: 0.6;
}
.quick-actions.data-v-4bd6864f {
  margin-top: 20rpx;
}
.quick-actions .action-card.data-v-4bd6864f {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(24rpx) saturate(180%);
  -webkit-backdrop-filter: blur(24rpx) saturate(180%);
  border: 1rpx solid rgba(209, 213, 219, 0.3);
  box-shadow: 0 16rpx 64rpx 0 rgba(100, 100, 135, 0.1);
  border-radius: 20rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}
.quick-actions .action-card.data-v-4bd6864f:active {
  -webkit-transform: translateY(2rpx) scale(0.98);
          transform: translateY(2rpx) scale(0.98);
}
.quick-actions .action-card .action-icon.data-v-4bd6864f {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.quick-actions .action-card .action-text.data-v-4bd6864f {
  font-size: 28rpx;
  color: white;
  font-weight: 600;
}
.bottom-tabbar.data-v-4bd6864f {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 1000;
}
.bottom-tabbar .tab-item.data-v-4bd6864f {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 0;
  transition: all 0.3s ease;
}
.bottom-tabbar .tab-item.data-v-4bd6864f:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.bottom-tabbar .tab-item .tab-text.data-v-4bd6864f {
  font-size: 20rpx;
  color: #999;
  font-weight: 500;
}
.bottom-tabbar .tab-item .tab-text.active.data-v-4bd6864f {
  color: #f78ca0;
}
.bottom-tabbar .tab-item .add-btn.data-v-4bd6864f {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}
.bottom-tabbar .tab-item.active .tab-text.data-v-4bd6864f {
  color: #f78ca0;
}
