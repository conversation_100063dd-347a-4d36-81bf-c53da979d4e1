import request from '@/common/request/index.js'

/**
 * 用户相关API
 */
export default {
  /**
   * 微信登录
   * @param {Object} data 登录数据
   */
  wxLogin(data) {
    return request.post('/user/wx-login', data)
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return request.get('/user/info')
  },

  /**
   * 更新用户信息
   * @param {Object} data 用户数据
   */
  updateUserInfo(data) {
    return request.put('/user/info', data)
  },

  /**
   * 上传头像
   * @param {String} filePath 文件路径
   */
  uploadAvatar(filePath) {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: `${require('@/config.js').baseUrl}/user/upload-avatar`,
        filePath: filePath,
        name: 'avatar',
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('token')}`
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 200) {
              resolve(data)
            } else {
              reject(new Error(data.message || '头像上传失败'))
            }
          } catch (e) {
            reject(new Error('头像上传响应解析失败'))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  },

  /**
   * 获取用户投稿列表
   * @param {Object} params 查询参数
   */
  getUserSubmissions(params = {}) {
    return request.get('/user/submissions', params)
  },

  /**
   * 获取用户收藏列表
   * @param {Object} params 查询参数
   */
  getUserFavorites(params = {}) {
    return request.get('/user/favorites', params)
  },

  /**
   * 收藏/取消收藏投稿
   * @param {Number} submissionId 投稿ID
   */
  toggleFavorite(submissionId) {
    return request.post('/user/toggle-favorite', { submission_id: submissionId })
  },

  /**
   * 获取用户统计数据
   */
  getUserStats() {
    return request.get('/user/stats')
  },

  /**
   * 删除用户投稿
   * @param {Number} submissionId 投稿ID
   */
  deleteUserSubmission(submissionId) {
    return request.delete(`/user/submissions/${submissionId}`)
  },

  /**
   * 注销登录
   */
  logout() {
    return request.post('/user/logout')
  }
}
