2025-07-10 11:02:15.824 | ERROR    | utils.tools:import_modules:84 - AttributeError：导入中间件失败，未找到该模块：core.middleware.register_request_log_middleware
2025-07-10 11:02:15.826 | ERROR    | utils.tools:import_modules:84 - AttributeError：导入中间件失败，未找到该模块：core.middleware.register_demo_env_middleware
2025-07-10 11:02:15.827 | ERROR    | utils.tools:import_modules:84 - AttributeError：导入中间件失败，未找到该模块：core.middleware.register_jwt_refresh_middleware
2025-07-10 11:02:21.376 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA358BB50>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA358BB50>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA358BB50>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA3644820>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA36481F0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA36481F0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA36481F0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA36481F0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:22.258 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354FD90>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354FD90>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354FD90>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA3647340>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354EF80>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354EF80>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354EF80>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354EF80>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:23.067 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle <TaskStepMethWrapper object at 0x000002ADA35DEC20>()>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskStepMethWrapper object at 0x000002ADA35DEC20>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskStepMethWrapper object at 0x000002ADA35DEC20>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x000002ADA35DEC20>()>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1990>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': '127.0.0.1:9001', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Android"', 'user-agent': 'Mozilla/5.0 ...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1990>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002ADA07005E0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA36442B0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FAC0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FAC0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 808, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FAC0>
          │    │       │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 692, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:26.561 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354DAB0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354DAB0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354DAB0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA35A6020>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354EEF0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354EEF0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354EEF0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354EEF0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:29.997 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E4D0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E4D0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E4D0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA36473A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D870>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D870>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D870>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D870>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:34.406 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1990>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1990>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1990>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA35A6140>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FA30>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FA30>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FA30>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FA30>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:41.903 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1A20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1A20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1A20>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA3812B30>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1B40>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1B40>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1B40>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1B40>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:43.142 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354E7A0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354E7A0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354E7A0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA3647BB0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1900>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1900>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1900>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1900>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:48.215 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1870>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1870>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1870>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383C7C0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E440>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E440>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E440>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E440>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:02:51.586 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E320>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E320>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E320>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA3647D30>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D7E0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D7E0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D7E0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D7E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:03:04.789 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354FA30>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354FA30>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354FA30>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA3812E00>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354E0E0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354E0E0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354E0E0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354E0E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:03:06.538 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1A20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1A20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA37D1A20>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA3647880>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1B40>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1B40>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1B40>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA37D1B40>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:05:09.930 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E4D0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E4D0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E4D0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383C430>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DF30>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DF30>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DF30>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DF30>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:05:41.914 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382DCF0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382DCF0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382DCF0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383EA40>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DC60>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DC60>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DC60>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DC60>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:10:31.767 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E440>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E440>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E440>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383FC40>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E8C0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E8C0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E8C0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E8C0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:10:34.574 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E200>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E200>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E200>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383F0A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DE10>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DE10>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DE10>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DE10>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:10:45.731 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA1ECA170>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA1ECA170>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA1ECA170>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA36443D0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA1ECAEF0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA1ECAEF0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA1ECAEF0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA1ECAEF0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:10:47.244 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354DAB0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354DAB0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354DAB0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383E620>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FC70>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FC70>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FC70>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA354FC70>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:10:48.059 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354E0E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354E0E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA354E0E0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383E290>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DFC0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DFC0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DFC0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DFC0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:11:36.273 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382D870>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382D870>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382D870>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383D7E0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DEA0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DEA0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DEA0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DEA0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:11:37.898 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E3B0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E3B0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E3B0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383D390>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DC60>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DC60>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DC60>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DC60>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:12:11.245 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E710>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E710>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E710>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383D090>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E830>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E830>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E830>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E830>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:12:12.518 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E290>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E290>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E290>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383DF60>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DF30>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DF30>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DF30>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DF30>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:12:14.693 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E9E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E9E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E9E0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383DEA0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E830>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E830>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E830>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382E830>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:12:16.884 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E560>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E560>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382E560>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383F640>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D7E0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D7E0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D7E0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382D7E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:12:18.869 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 86, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002AD9D6ABA30>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002AD9D6ABA30>
           └ <function get_command at 0x000002ADA339E710>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperCommand.main at 0x000002ADA338B400>
           └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 716, in main
    return _main(
           └ <function _main at 0x000002ADA338A9E0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002ADA33B8AC0>
         │    └ <function Command.invoke at 0x000002ADA063E290>
         └ <TyperCommand run>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001}
           │   │      │    │           └ <click.core.Context object at 0x000002ADA33B8AC0>
           │   │      │    └ <function run at 0x000002ADA33C15A0>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002ADA063D000>
           └ <click.core.Context object at 0x000002ADA33B8AC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9001}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9001}
           └ <function run at 0x000002ADA339F760>

  File "E:\kaifa\投稿\kinit2\kinit-api\main.py", line 79, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9001
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002ADA06A16C0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalC...

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002ADA06A1870>
    └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002ADA06A1900>
           │       │   └ <uvicorn.server.Server object at 0x000002ADA33B8FA0>
           │       └ <function run at 0x000002AD9D73BF40>
           └ <module 'asyncio' from 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\\...

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002ADA333F610>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002AD9D9DA830>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002AD9DA82200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002AD9D9DC310>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002AD9D95FC70>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.10_3.10.3056.0_x64__qbz5n2kfra8p0\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>

  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002ADA3480DC0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002ADA3...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
          └ <fastapi.applications.FastAPI object at 0x000002ADA33F7E80>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382EB90>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002ADA35879D0>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382EB90>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002ADA3587760>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002ADA382EB90>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002ADA383DC00>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002ADA3587640>
          └ <function wrap_app_handling_exceptions at 0x000002ADA0475B40>
> File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DE10>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DE10>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002ADA3480550>>
          └ <fastapi.routing.APIRouter object at 0x000002ADA3480550>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DE10>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Mount.handle at 0x000002ADA04775B0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\routing.py", line 487, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002ADA382DE10>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x000002ADA3264B80>
                     └ <starlette.staticfiles.StaticFiles object at 0x000002ADA3480D00>
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\site-packages\starlette\staticfiles.py", line 156, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-10 11:12:26.983 | ERROR    | utils.tools:import_modules:84 - AttributeError：导入中间件失败，未找到该模块：core.middleware.register_request_log_middleware
2025-07-10 11:12:26.984 | ERROR    | utils.tools:import_modules:84 - AttributeError：导入中间件失败，未找到该模块：core.middleware.register_demo_env_middleware
2025-07-10 11:12:26.985 | ERROR    | utils.tools:import_modules:84 - AttributeError：导入中间件失败，未找到该模块：core.middleware.register_jwt_refresh_middleware
